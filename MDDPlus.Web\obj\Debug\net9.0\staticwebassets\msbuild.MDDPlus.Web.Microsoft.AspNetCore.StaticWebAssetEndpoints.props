﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/css/mdd-fintech.270rw1o113.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mdd-fintech.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"270rw1o113"},{"Name":"integrity","Value":"sha256-QkLpyK95QmS4wfyKYPIl16o5hWHtqcRG4qY0UtLGo\u002BM="},{"Name":"label","Value":"_content/MDDPlus.Web/css/mdd-fintech.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14556"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022QkLpyK95QmS4wfyKYPIl16o5hWHtqcRG4qY0UtLGo\u002BM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 10:11:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/css/mdd-fintech.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mdd-fintech.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QkLpyK95QmS4wfyKYPIl16o5hWHtqcRG4qY0UtLGo\u002BM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14556"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022QkLpyK95QmS4wfyKYPIl16o5hWHtqcRG4qY0UtLGo\u002BM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 10:11:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/MDDPlus.Web/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 10:07:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 10:07:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/js/mdd-fintech.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\mdd-fintech.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PpVr\u002BVlrO36p10nJX39C63WSYQjpVb9O98zMbWwD3Tw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"17585"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PpVr\u002BVlrO36p10nJX39C63WSYQjpVb9O98zMbWwD3Tw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 12:30:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MDDPlus.Web/js/mdd-fintech.u14x8ov2t1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\mdd-fintech.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u14x8ov2t1"},{"Name":"integrity","Value":"sha256-PpVr\u002BVlrO36p10nJX39C63WSYQjpVb9O98zMbWwD3Tw="},{"Name":"label","Value":"_content/MDDPlus.Web/js/mdd-fintech.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17585"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PpVr\u002BVlrO36p10nJX39C63WSYQjpVb9O98zMbWwD3Tw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 17 Jul 2025 12:30:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>