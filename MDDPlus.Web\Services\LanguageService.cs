using Microsoft.AspNetCore.Localization;
using System.Globalization;

namespace MDDPlus.Web.Services
{
    public interface ILanguageService
    {
        string GetCurrentLanguage();
        string GetCurrentDirection();
        void SetLanguage(string language);
        List<LanguageOption> GetAvailableLanguages();
        string GetLocalizedUrl(string currentUrl, string targetLanguage);
    }

    public class LanguageService : ILanguageService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly List<LanguageOption> _availableLanguages;

        public LanguageService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
            _availableLanguages = new List<LanguageOption>
            {
                new LanguageOption { Code = "ar", Name = "العربية", Direction = "rtl", IsDefault = true },
                new LanguageOption { Code = "en", Name = "English", Direction = "ltr", IsDefault = false }
            };
        }

        public string GetCurrentLanguage()
        {
            var culture = CultureInfo.CurrentCulture;
            return culture.Name.StartsWith("ar") ? "ar" : "en";
        }

        public string GetCurrentDirection()
        {
            return GetCurrentLanguage() == "ar" ? "rtl" : "ltr";
        }

        public void SetLanguage(string language)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                var culture = language == "ar" ? "ar-SA" : "en-US";
                var requestCulture = new RequestCulture(culture);
                
                context.Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(requestCulture),
                    new CookieOptions
                    {
                        Expires = DateTimeOffset.UtcNow.AddYears(1),
                        HttpOnly = false,
                        Secure = context.Request.IsHttps,
                        SameSite = SameSiteMode.Lax
                    }
                );
            }
        }

        public List<LanguageOption> GetAvailableLanguages()
        {
            return _availableLanguages;
        }

        public string GetLocalizedUrl(string currentUrl, string targetLanguage)
        {
            // Simple implementation - in a real scenario, you might want to use Umbraco's culture and hostnames
            var uri = new Uri(currentUrl, UriKind.RelativeOrAbsolute);
            var query = uri.Query;
            
            if (query.Contains("lang="))
            {
                // Replace existing language parameter
                query = System.Text.RegularExpressions.Regex.Replace(query, @"lang=[^&]*", $"lang={targetLanguage}");
            }
            else
            {
                // Add language parameter
                query = string.IsNullOrEmpty(query) ? $"?lang={targetLanguage}" : $"{query}&lang={targetLanguage}";
            }
            
            return $"{uri.GetLeftPart(UriPartial.Path)}{query}";
        }
    }

    public class LanguageOption
    {
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public string Direction { get; set; } = "ltr";
        public bool IsDefault { get; set; } = false;
    }
}
