[{"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.QueryContentApiController", "Method": "Query", "RelativePath": "umbraco/delivery/api/v1/content", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fetch", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String[]", "IsRequired": false}, {"Name": "sort", "Type": "System.String[]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByIdsContentApiController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/delivery/api/v1/content/item", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByIdContentApiController", "Method": "ById", "RelativePath": "umbraco/delivery/api/v1/content/item/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByRouteContentApiController", "Method": "ByRoute", "RelativePath": "umbraco/delivery/api/v1/content/item/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.QueryMediaApiController", "Method": "Query", "RelativePath": "umbraco/delivery/api/v1/media", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fetch", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String[]", "IsRequired": false}, {"Name": "sort", "Type": "System.String[]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByIdsMediaApiController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/delivery/api/v1/media/item", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByIdMediaApiController", "Method": "ById", "RelativePath": "umbraco/delivery/api/v1/media/item/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByPathMediaApiController", "Method": "ByPath", "RelativePath": "umbraco/delivery/api/v1/media/item/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.QueryContentApiController", "Method": "QueryV20", "RelativePath": "umbraco/delivery/api/v2/content", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fetch", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String[]", "IsRequired": false}, {"Name": "sort", "Type": "System.String[]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByIdContentApiController", "Method": "ByIdV20", "RelativePath": "umbraco/delivery/api/v2/content/item/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByRouteContentApiController", "Method": "ByRouteV20", "RelativePath": "umbraco/delivery/api/v2/content/item/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Content.ByIdsContentApiController", "Method": "ItemsV20", "RelativePath": "umbraco/delivery/api/v2/content/items", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiContentResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 403}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.QueryMediaApiController", "Method": "QueryV20", "RelativePath": "umbraco/delivery/api/v2/media", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fetch", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String[]", "IsRequired": false}, {"Name": "sort", "Type": "System.String[]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByIdMediaApiController", "Method": "ByIdV20", "RelativePath": "umbraco/delivery/api/v2/media/item/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByPathMediaApiController", "Method": "ByPathV20", "RelativePath": "umbraco/delivery/api/v2/media/item/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Delivery.Controllers.Media.ByIdsMediaApiController", "Method": "ItemsV20", "RelativePath": "umbraco/delivery/api/v2/media/items", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.DeliveryApi.IApiMediaWithCropsResponse, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "text/json", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ValidateUpdateDocumentController", "Method": "ValidateV1_1", "RelativePath": "umbraco/management/api/v1.1/document/{id}/validate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.ValidateUpdateDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Collection.ByKeyDocumentCollectionController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/collection/document/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderCulture", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Document.Collection.DocumentCollectionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Collection.ByKeyMediaCollectionController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/collection/media", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Media.Collection.MediaCollectionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Culture.AllCultureController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/culture", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Culture.CultureReponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.CreateDataTypeController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/data-type", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDataTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.CreateDataTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.ByKeyDataTypeController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/data-type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.DataTypeResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.DeleteDataTypeController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/data-type/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.UpdateDataTypeController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/data-type/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateDataTypeViewModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.UpdateDataTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.CopyDataTypeController", "Method": "Copy", "RelativePath": "umbraco/management/api/v1/data-type/{id}/copy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "copyDataTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.CopyDataTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.IsUsedDataTypeController", "Method": "IsUsed", "RelativePath": "umbraco/management/api/v1/data-type/{id}/is-used", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.MoveDataTypeController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/data-type/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDataTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.MoveDataTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.ReferencesDataTypeController", "Method": "References", "RelativePath": "umbraco/management/api/v1/data-type/{id}/references", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.DataTypeReferenceResponseModel[]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.ConfigurationDataTypeController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/data-type/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DataType.DatatypeConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Folder.CreateDataTypeFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/data-type/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createFolderRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.CreateFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Folder.ByKeyDataTypeFolderController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/data-type/folder/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.FolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Folder.DeleteDataTypeFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/data-type/folder/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Folder.UpdateDataTypeFolderController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/data-type/folder/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateFolderResponseModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.UpdateFolderResponseModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.AllDictionaryController", "Method": "All", "RelativePath": "umbraco/management/api/v1/dictionary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Dictionary.DictionaryOverviewResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.CreateDictionaryController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/dictionary", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDictionaryItemRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Dictionary.CreateDictionaryItemRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 409}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.ByKeyDictionaryController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/dictionary/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Dictionary.DictionaryItemResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.DeleteDictionaryController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/dictionary/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.UpdateDictionaryController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/dictionary/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateDictionaryItemRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Dictionary.UpdateDictionaryItemRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.ExportDictionaryController", "Method": "Export", "RelativePath": "umbraco/management/api/v1/dictionary/{id}/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.FileContentResult", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.MoveDictionaryController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/dictionary/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDictionaryRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Dictionary.MoveDictionaryRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.ImportDictionaryController", "Method": "Import", "RelativePath": "umbraco/management/api/v1/dictionary/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "importDictionaryRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Dictionary.ImportDictionaryRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.CreateDocumentController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.CreateDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.CreateDocumentBlueprintController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document-blueprint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentBlueprint.CreateDocumentBlueprintRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.ByKeyDocumentBlueprintController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-blueprint/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentBlueprint.DocumentBlueprintResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.DeleteDocumentBlueprintController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document-blueprint/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.UpdateDocumentBlueprintController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document-blueprint/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UpdateDocumentBlueprintRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.MoveDocumentBlueprintController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/document-blueprint/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentBlueprint.MoveDocumentBlueprintRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Folder.CreateDocumentBlueprintFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document-blueprint/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createFolderRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.CreateFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Folder.ByKeyDocumentBlueprintFolderController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-blueprint/folder/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.FolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Folder.DeleteDocumentBlueprintFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document-blueprint/folder/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Folder.UpdateDocumentBlueprintFolderController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document-blueprint/folder/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateFolderResponseModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.UpdateFolderResponseModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.CreateDocumentBlueprintFromDocumentController", "Method": "CreateFromDocument", "RelativePath": "umbraco/management/api/v1/document-blueprint/from-document", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentBlueprint.CreateDocumentBlueprintFromDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.CreateDocumentTypeController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document-type", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.CreateDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.ByKeyDocumentTypeController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.DocumentTypeResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.DeleteDocumentTypeController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document-type/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.UpdateDocumentTypeController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document-type/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.UpdateDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.AllowedChildrenDocumentTypeController", "Method": "Allowed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-type/{id}/allowed-children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.AllowedDocumentType, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.DocumentBlueprintForDocumentTypeController", "Method": "DocumentBlueprintByDocumentTypeKey", "RelativePath": "umbraco/management/api/v1/document-type/{id}/blueprint", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.DocumentTypeBlueprintItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.CompositionReferenceDocumentTypeController", "Method": "CompositionReferences", "RelativePath": "umbraco/management/api/v1/document-type/{id}/composition-references", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.DocumentTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.CopyDocumentTypeController", "Method": "Copy", "RelativePath": "umbraco/management/api/v1/document-type/{id}/copy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "copyDocumentTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.CopyDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.ExportDocumentTypeController", "Method": "Export", "RelativePath": "umbraco/management/api/v1/document-type/{id}/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.FileContentResult", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.ImportExistingDocumentTypeController", "Method": "Import", "RelativePath": "umbraco/management/api/v1/document-type/{id}/import", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.ImportDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.MoveDocumentTypeController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/document-type/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDocumentTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.MoveDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.AllowedAtRootDocumentTypeController", "Method": "AllowedAtRoot", "RelativePath": "umbraco/management/api/v1/document-type/allowed-at-root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.AllowedDocumentType, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.AvailableCompositionDocumentTypeController", "Method": "AvailableCompositions", "RelativePath": "umbraco/management/api/v1/document-type/available-compositions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "compositionModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.DocumentTypeCompositionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.AvailableDocumentTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.ConfigurationDocumentTypeController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/document-type/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.DocumentTypeConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Folder.CreateDocumentTypeFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document-type/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createFolderRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.CreateFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Folder.ByKeyDocumentTypeFolderController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-type/folder/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.FolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Folder.DeleteDocumentTypeFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document-type/folder/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Folder.UpdateDocumentTypeFolderController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document-type/folder/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateFolderResponseModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.UpdateFolderResponseModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.ImportNewDocumentTypeController", "Method": "Import", "RelativePath": "umbraco/management/api/v1/document-type/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.DocumentType.ImportDocumentTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentVersion.AllDocumentVersionController", "Method": "All", "RelativePath": "umbraco/management/api/v1/document-version", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "culture", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Document.DocumentVersionItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentVersion.ByKeyDocumentVersionController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document-version/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Document.DocumentVersionResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentVersion.UpdatePreventCleanupDocumentVersionController", "Method": "Set", "RelativePath": "umbraco/management/api/v1/document-version/{id}/prevent-cleanup", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "preventCleanup", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentVersion.RollbackDocumentVersionController", "Method": "Rollback", "RelativePath": "umbraco/management/api/v1/document-version/{id}/rollback", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "culture", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ByKeyDocumentController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/document/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Document.DocumentResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.DeleteDocumentController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.UpdateDocumentController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UpdateDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.GetAuditLogDocumentController", "Method": "GetAuditLog", "RelativePath": "umbraco/management/api/v1/document/{id}/audit-log", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "sinceDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.AuditLog.AuditLogResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.CopyDocumentController", "Method": "Copy", "RelativePath": "umbraco/management/api/v1/document/{id}/copy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "copyDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.CopyDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.DomainsController", "Method": "Domains", "RelativePath": "umbraco/management/api/v1/document/{id}/domains", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Document.DomainsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.UpdateDomainsController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document/{id}/domains", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UpdateDomainsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 409}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.MoveDocumentController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/document/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.MoveDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.MoveToRecycleBinDocumentController", "Method": "MoveToRecycleBin", "RelativePath": "umbraco/management/api/v1/document/{id}/move-to-recycle-bin", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.NotificationsController", "Method": "Notifications", "RelativePath": "umbraco/management/api/v1/document/{id}/notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Document.DocumentNotificationResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.UpdateNotificationsController", "Method": "UpdateNotifications", "RelativePath": "umbraco/management/api/v1/document/{id}/notifications", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UpdateDocumentNotificationsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.CreatePublicAccessDocumentController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/document/{id}/public-access", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "publicAccessRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PublicAccess.PublicAccessRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.DeletePublicAccessDocumentController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/document/{id}/public-access", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.GetPublicAccessDocumentController", "Method": "GetPublicAccess", "RelativePath": "umbraco/management/api/v1/document/{id}/public-access", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.PublicAccess.PublicAccessResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.UpdatePublicAccessDocumentController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/document/{id}/public-access", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PublicAccess.PublicAccessRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.PublishDocumentController", "Method": "Publish", "RelativePath": "umbraco/management/api/v1/document/{id}/publish", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.PublishDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.PublishDocumentWithDescendantsController", "Method": "PublishWithDescendants", "RelativePath": "umbraco/management/api/v1/document/{id}/publish-with-descendants", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.PublishDocumentWithDescendantsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ByKeyPublishedDocumentController", "Method": "ByKeyPublished", "RelativePath": "umbraco/management/api/v1/document/{id}/published", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Document.PublishedDocumentResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.References.ReferencedByDocumentController", "Method": "ReferencedBy", "RelativePath": "umbraco/management/api/v1/document/{id}/referenced-by", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.TrackedReferences.IReferenceResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.References.ReferencedDescendantsDocumentController", "Method": "ReferencedDescendants", "RelativePath": "umbraco/management/api/v1/document/{id}/referenced-descendants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.UnpublishDocumentController", "Method": "Unpublish", "RelativePath": "umbraco/management/api/v1/document/{id}/unpublish", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UnpublishDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ValidateUpdateDocumentController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/document/{id}/validate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.UpdateDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.References.AreReferencedDocumentController", "Method": "GetPagedReferencedItems", "RelativePath": "umbraco/management/api/v1/document/are-referenced", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ConfigurationDocumentController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/document/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Document.DocumentConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.SortDocumentController", "Method": "Sort", "RelativePath": "umbraco/management/api/v1/document/sort", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "sortingRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Sorting.SortingRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.DocumentUrlController", "Method": "GetUrls", "RelativePath": "umbraco/management/api/v1/document/urls", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Document.DocumentUrlInfoResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.ValidateCreateDocumentController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/document/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Document.CreateDocumentRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DynamicRoot.GetRootsController", "Method": "GetRoots", "RelativePath": "umbraco/management/api/v1/dynamic-root/query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.DynamicRoot.DynamicRootRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.DynamicRoot.DynamicRootResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DynamicRoot.GetQueryStepsController", "Method": "GetQuerySteps", "RelativePath": "umbraco/management/api/v1/dynamic-root/steps", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Filter.FilterDataTypeFilterController", "Method": "Filter", "RelativePath": "umbraco/management/api/v1/filter/data-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.DataType.Item.DataTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.Filter.FilterMemberFilterController", "Method": "Filter", "RelativePath": "umbraco/management/api/v1/filter/member", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "memberTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "memberGroupName", "Type": "System.String", "IsRequired": false}, {"Name": "isApproved", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isLockedOut", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Member.MemberResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Filter.FilterUserFilterController", "Method": "Filter", "RelativePath": "umbraco/management/api/v1/filter/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "userGroupIds", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "userStates", "Type": "System.Collections.Generic.HashSet`1[[Umbraco.Cms.Core.Models.Membership.UserState, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.User.UserResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Filter.FilterUserGroupFilterController", "Method": "Filter", "RelativePath": "umbraco/management/api/v1/filter/user-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.UserGroup.UserGroupResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.HealthCheck.Group.AllHealthCheckGroupController", "Method": "All", "RelativePath": "umbraco/management/api/v1/health-check-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.HealthCheck.HealthCheckGroupResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.HealthCheck.Group.ByNameHealthCheckGroupController", "Method": "ByName", "RelativePath": "umbraco/management/api/v1/health-check-group/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.HealthCheck.HealthCheckGroupPresentationModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.HealthCheck.Group.CheckHealthCheckGroupController", "Method": "ByNameWithResult", "RelativePath": "umbraco/management/api/v1/health-check-group/{name}/check", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.HealthCheck.HealthCheckGroupWithResultResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.HealthCheck.ExecuteActionHealthCheckController", "Method": "ExecuteAction", "RelativePath": "umbraco/management/api/v1/health-check/execute-action", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "Umbraco.Cms.Api.Management.ViewModels.HealthCheck.HealthCheckActionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.HealthCheck.HealthCheckResultResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Help.GetHelpController", "Method": "Get", "RelativePath": "umbraco/management/api/v1/help", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "section", "Type": "System.String", "IsRequired": false}, {"Name": "tree", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "baseUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Help.HelpPageResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UrlSegment.ResizeImagingController", "Method": "Urls", "RelativePath": "umbraco/management/api/v1/imaging/resize/urls", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "height", "Type": "System.Int32", "IsRequired": false}, {"Name": "width", "Type": "System.Int32", "IsRequired": false}, {"Name": "mode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Media.MediaUrlInfoResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Import.AnalyzeImportController", "Method": "Analyze", "RelativePath": "umbraco/management/api/v1/import/analyze", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "temporaryFileId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Import.EntityImportAnalysisResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Indexer.AllIndexerController", "Method": "All", "RelativePath": "umbraco/management/api/v1/indexer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Indexer.IndexResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Indexer.DetailsIndexerController", "Method": "Details", "RelativePath": "umbraco/management/api/v1/indexer/{indexName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "indexName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.Indexer.IndexResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Indexer.RebuildIndexerController", "Method": "Rebuild", "RelativePath": "umbraco/management/api/v1/indexer/{indexName}/rebuild", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "indexName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 409}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Install.SettingsInstallController", "Method": "Settings", "RelativePath": "umbraco/management/api/v1/install/settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 428}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.Installer.InstallSettingsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController", "Method": "Setup", "RelativePath": "umbraco/management/api/v1/install/setup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "installData", "Type": "Umbraco.Cms.Api.Management.ViewModels.Installer.InstallRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 428}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Install.ValidateDatabaseInstallController", "Method": "ValidateDatabase", "RelativePath": "umbraco/management/api/v1/install/validate-database", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "viewModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Installer.DatabaseInstallRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Item.ItemDatatypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/data-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.DataType.Item.DataTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Item.SearchDataTypeItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/data-type/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.DataType.Item.DataTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.Item.ItemDictionaryItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/dictionary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Dictionary.Item.DictionaryItemItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Item.ItemDocumentItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/document", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Document.Item.DocumentItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Item.ItemDocumentBlueprintController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/document-blueprint", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentBlueprint.Item.DocumentBlueprintItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Item.ItemDocumentTypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/document-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.Item.DocumentTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Item.SearchDocumentTypeItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/document-type/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.DocumentType.Item.DocumentTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Item.SearchDocumentItemController", "Method": "SearchFromParent", "RelativePath": "umbraco/management/api/v1/item/document/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "parentId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.Document.Item.DocumentItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.Item.ItemLanguageItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/language", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "isoCode", "Type": "System.Collections.Generic.HashSet`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Language.Item.LanguageItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.Item.DefaultLanguageEntityController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/language/default", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Language.Item.LanguageItemResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Item.ItemMediaItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/media", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Media.Item.MediaItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Item.ItemMediaTypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/media-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.Item.MediaTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Item.AllowedMediaTypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/media-type/allowed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileExtension", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.Item.MediaTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Item.FolderMediaTypeItemController", "Method": "Folders", "RelativePath": "umbraco/management/api/v1/item/media-type/folders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.Item.MediaTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Item.SearchMediaTypeItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/media-type/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.Item.MediaTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Item.SearchMediaItemController", "Method": "SearchFromParent", "RelativePath": "umbraco/management/api/v1/item/media/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "parentId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.Media.Item.MediaItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.Item.ItemMemberItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/member", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Member.Item.MemberItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.Item.ItemMemberGroupItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/member-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MemberGroup.Item.MemberGroupItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.Item.ItemMemberTypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/member-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MemberType.Item.MemberTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.Item.SearchMemberTypeItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/member-type/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.MemberType.Item.MemberTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.Item.SearchMemberItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/member/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.Member.Item.MemberItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Item.ItemPartialViewItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/partial-view", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.Collections.Generic.HashSet`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.PartialView.Item.PartialViewItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RelationType.Item.ItemRelationTypeItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/relation-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.RelationType.Item.RelationTypeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Item.ItemScriptItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/script", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.Collections.Generic.HashSet`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Script.Item.ScriptItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.StaticFile.Item.ItemStaticFileItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/static-file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.Collections.Generic.HashSet`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.StaticFile.Item.StaticFileItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Item.ItemStylesheetItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/stylesheet", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.Collections.Generic.HashSet`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Stylesheet.Item.StylesheetItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Item.ItemTemplateItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Template.Item.TemplateItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Item.SearchTemplateItemController", "Method": "Search", "RelativePath": "umbraco/management/api/v1/item/template/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Models.PagedModel`1[[Umbraco.Cms.Api.Management.ViewModels.Template.Item.TemplateItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Item.ItemUserItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.User.Item.UserItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.Item.ItemUserGroupItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/user-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.UserGroup.Item.UserGroupItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.Item.ItemWebhookItemController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/item/webhook", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Webhook.Item.WebhookItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.AllLanguageController", "Method": "All", "RelativePath": "umbraco/management/api/v1/language", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Language.LanguageResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.CreateLanguageController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/language", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createLanguageRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Language.CreateLanguageRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.ByIsoCodeLanguageController", "Method": "ByIsoCode", "RelativePath": "umbraco/management/api/v1/language/{isoCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "isoCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.Language.LanguageResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.DeleteLanguageController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/language/{isoCode}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "isoCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Language.UpdateLanguageController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/language/{isoCode}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "isoCode", "Type": "System.String", "IsRequired": true}, {"Name": "updateLanguageRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Language.UpdateLanguageRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.AllSinkLevelLogViewerController", "Method": "AllLogLevels", "RelativePath": "umbraco/management/api/v1/log-viewer/level", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.LogViewer.LoggerResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.LogLevelCountLogViewerController", "Method": "LogLevelCounts", "RelativePath": "umbraco/management/api/v1/log-viewer/level-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.LogViewer.LogLevelCountsReponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.AllLogViewerController", "Method": "AllLogs", "RelativePath": "umbraco/management/api/v1/log-viewer/log", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "filterExpression", "Type": "System.String", "IsRequired": false}, {"Name": "logLevel", "Type": "Umbraco.Cms.Core.Logging.LogLevel[]", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.LogViewer.LogMessageResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.AllMessageTemplateLogViewerController", "Method": "AllMessageTemplates", "RelativePath": "umbraco/management/api/v1/log-viewer/message-template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.LogViewer.LogTemplateResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.SavedSearch.AllSavedSearchLogViewerController", "Method": "AllSavedSearches", "RelativePath": "umbraco/management/api/v1/log-viewer/saved-search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.LogViewer.SavedLogSearchResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.SavedSearch.CreateSavedSearchLogViewerController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/log-viewer/saved-search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "savedSearch", "Type": "Umbraco.Cms.Api.Management.ViewModels.LogViewer.SavedLogSearchRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.SavedSearch.ByNameSavedSearchLogViewerController", "Method": "ByName", "RelativePath": "umbraco/management/api/v1/log-viewer/saved-search/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.LogViewer.SavedLogSearchResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.SavedSearch.DeleteSavedSearchLogViewerController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/log-viewer/saved-search/{name}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.LogViewer.ValidateLogFileSizeLogViewerController", "Method": "CanViewLogs", "RelativePath": "umbraco/management/api/v1/log-viewer/validate-logs-size", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Manifest.AllManifestController", "Method": "AllManifests", "RelativePath": "umbraco/management/api/v1/manifest/manifest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Manifest.ManifestResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Manifest.PrivateManifestManifestController", "Method": "PrivateManifests", "RelativePath": "umbraco/management/api/v1/manifest/manifest/private", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Manifest.ManifestResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Manifest.PublicManifestManifestController", "Method": "PublicManifests", "RelativePath": "umbraco/management/api/v1/manifest/manifest/public", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Manifest.ManifestResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.CreateMediaController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/media", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.CreateMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.CreateMediaTypeController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/media-type", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.CreateMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.ByKeyMediaTypeController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/media-type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.MediaTypeResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.DeleteMediaTypeController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/media-type/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.UpdateMediaTypeController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/media-type/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.UpdateMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.AllowedChildrenMediaTypeController", "Method": "Allowed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/media-type/{id}/allowed-children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.AllowedMediaType, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.CompositionReferenceMediaTypeController", "Method": "CompositionReferences", "RelativePath": "umbraco/management/api/v1/media-type/{id}/composition-references", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.MediaTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.CopyMediaTypeController", "Method": "Copy", "RelativePath": "umbraco/management/api/v1/media-type/{id}/copy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "copyMediaTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.CopyMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.ExportMediaTypeController", "Method": "Export", "RelativePath": "umbraco/management/api/v1/media-type/{id}/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.FileContentResult", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.ImportExistingMediaTypeController", "Method": "Import", "RelativePath": "umbraco/management/api/v1/media-type/{id}/import", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.ImportMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.MoveMediaTypeController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/media-type/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveMediaTypeRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.MoveMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.AllowedAtRootMediaTypeController", "Method": "AllowedAtRoot", "RelativePath": "umbraco/management/api/v1/media-type/allowed-at-root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.AllowedMediaType, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.AvailableCompositionMediaTypeController", "Method": "AvailableCompositions", "RelativePath": "umbraco/management/api/v1/media-type/available-compositions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "compositionModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.MediaTypeCompositionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MediaType.AvailableMediaTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.ConfigurationMediaTypeController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/media-type/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.MediaTypeConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Folder.CreateMediaTypeFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/media-type/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createFolderRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.CreateFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Folder.ByKeyMediaTypeFolderController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/media-type/folder/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.FolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Folder.DeleteMediaTypeFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/media-type/folder/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Folder.UpdateMediaTypeFolderController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/media-type/folder/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateFolderResponseModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Folder.UpdateFolderResponseModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.ImportNewMediaTypeController", "Method": "Import", "RelativePath": "umbraco/management/api/v1/media-type/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.MediaType.ImportMediaTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.ByKeyMediaController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/media/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Media.MediaResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.DeleteMediaController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/media/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.UpdateMediaController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/media/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.UpdateMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.GetAuditLogMediaController", "Method": "GetAuditLog", "RelativePath": "umbraco/management/api/v1/media/{id}/audit-log", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "orderDirection", "Type": "System.String", "IsRequired": false}, {"Name": "sinceDate", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.AuditLog.AuditLogResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.MoveMediaController", "Method": "Move", "RelativePath": "umbraco/management/api/v1/media/{id}/move", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.MoveMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.MoveToRecycleBinMediaController", "Method": "MoveToRecycleBin", "RelativePath": "umbraco/management/api/v1/media/{id}/move-to-recycle-bin", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.References.ReferencedByMediaController", "Method": "ReferencedBy", "RelativePath": "umbraco/management/api/v1/media/{id}/referenced-by", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.TrackedReferences.IReferenceResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.References.ReferencedDescendantsMediaController", "Method": "ReferencedDescendants", "RelativePath": "umbraco/management/api/v1/media/{id}/referenced-descendants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.ValidateUpdateMediaController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/media/{id}/validate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.UpdateMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.References.AreReferencedMediaController", "Method": "GetPagedReferencedItems", "RelativePath": "umbraco/management/api/v1/media/are-referenced", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.ConfigurationMediaController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/media/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Media.MediaConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.SortMediaController", "Method": "Sort", "RelativePath": "umbraco/management/api/v1/media/sort", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "sortingRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Sorting.SortingRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.MediaUrlController", "Method": "GetUrls", "RelativePath": "umbraco/management/api/v1/media/urls", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Media.MediaUrlInfoResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.ValidateCreateMediaController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/media/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.CreateMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.CreateMemberController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/member", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Member.CreateMemberRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.AllMemberGroupController", "Method": "All", "RelativePath": "umbraco/management/api/v1/member-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.MemberGroup.MemberGroupResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.CreateMemberGroupController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/member-group", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.MemberGroup.CreateMemberGroupRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.ByKeyMemberGroupController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/member-group/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.MemberGroup.MemberGroupResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.DeleteMemberGroupController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/member-group/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.UpdateMemberGroupController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/member-group/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.MemberGroup.UpdateMemberGroupRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.CreateMemberTypeController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/member-type", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MemberType.CreateMemberTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.ByKeyMemberTypeController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/member-type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.MemberType.MemberTypeResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.DeleteMemberTypeController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/member-type/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.UpdateMemberTypeController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/member-type/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MemberType.UpdateMemberTypeRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.CompositionReferenceMemberTypeController", "Method": "CompositionReferences", "RelativePath": "umbraco/management/api/v1/member-type/{id}/composition-references", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MemberType.MemberTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.CopyMemberTypeController", "Method": "Copy", "RelativePath": "umbraco/management/api/v1/member-type/{id}/copy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.AvailableCompositionMemberTypeController", "Method": "AvailableCompositions", "RelativePath": "umbraco/management/api/v1/member-type/available-compositions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "compositionModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.MemberType.MemberTypeCompositionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.MemberType.AvailableMemberTypeCompositionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.ConfigurationMemberTypeController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/member-type/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.MemberType.MemberTypeConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.ByKeyMemberController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/member/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Member.MemberResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.DeleteMemberController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/member/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.UpdateMemberController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/member/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Member.UpdateMemberRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.ValidateUpdateMemberController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/member/{id}/validate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Member.UpdateMemberRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.ConfigurationMemberController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/member/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Member.MemberConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Member.ValidateCreateMemberController", "Method": "Validate", "RelativePath": "umbraco/management/api/v1/member/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Member.CreateMemberRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.ModelsBuilder.BuildModelsBuilderController", "Method": "BuildModels", "RelativePath": "umbraco/management/api/v1/models-builder/build", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 428}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.ModelsBuilder.GetModelsBuilderController", "Method": "GetDashboard", "RelativePath": "umbraco/management/api/v1/models-builder/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.ModelsBuilderDashboard.ModelsBuilderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.ModelsBuilder.StatusModelsBuilderController", "Method": "GetModelsOutOfDateStatus", "RelativePath": "umbraco/management/api/v1/models-builder/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.ModelsBuilderDashboard.OutOfDateStatusResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.ObjectTypes.AllowedObjectTypesController", "Method": "Allowed", "RelativePath": "umbraco/management/api/v1/object-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.RelationType.ObjectTypeResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.OEmbed.QueryOEmbedController", "Method": "Query", "RelativePath": "umbraco/management/api/v1/oembed/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "url", "Type": "System.Uri", "IsRequired": false}, {"Name": "max<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxHeight", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.OEmbed.OEmbedResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.RunMigrationPackageController", "Method": "RunMigrations", "RelativePath": "umbraco/management/api/v1/package/{name}/run-migration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 409}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.ConfigurationPackageController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/package/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Package.PackageConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.AllCreatedPackageController", "Method": "All", "RelativePath": "umbraco/management/api/v1/package/created", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Package.PackageDefinitionResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.CreateCreatedPackageController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/package/created", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createPackageRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Package.CreatePackageRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.ByKeyCreatedPackageController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/package/created/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.Package.PackageDefinitionResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.DeleteCreatedPackageController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/package/created/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.UpdateCreatedPackageController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/package/created/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updatePackageRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Package.UpdatePackageRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.Created.DownloadCreatedPackageController", "Method": "Download", "RelativePath": "umbraco/management/api/v1/package/created/{id}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.FileContentResult", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Package.AllMigrationStatusPackageController", "Method": "AllMigrationStatuses", "RelativePath": "umbraco/management/api/v1/package/migration-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Package.PackageMigrationStatusResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.CreatePartialViewController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/partial-view", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.CreatePartialViewRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.ByPathPartialViewController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/partial-view/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.PartialViewResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.DeletePartialViewController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/partial-view/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.UpdatePartialViewController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/partial-view/{path}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "updateViewModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.UpdatePartialViewRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.RenamePartialViewController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/partial-view/{path}/rename", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.RenamePartialViewRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Folder.CreatePartialViewFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/partial-view/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.Folder.CreatePartialViewFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Folder.ByPathPartialViewFolderController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/partial-view/folder/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.Folder.PartialViewFolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Folder.DeletePartialViewFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/partial-view/folder/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Snippet.GetAllController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/partial-view/snippet", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.PartialView.Snippets.PartialViewSnippetItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Snippet.ByIdController", "Method": "GetById", "RelativePath": "umbraco/management/api/v1/partial-view/snippet/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.PartialView.Snippets.PartialViewSnippetResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Preview.EndPreviewController", "Method": "End", "RelativePath": "umbraco/management/api/v1/preview", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Preview.EnterPreviewController", "Method": "Enter", "RelativePath": "umbraco/management/api/v1/preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Profiling.GetStatusProfilingController", "Method": "Status", "RelativePath": "umbraco/management/api/v1/profiling/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Profiling.ProfilingStatusResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Profiling.UpdateStatusProfilingController", "Method": "Status", "RelativePath": "umbraco/management/api/v1/profiling/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.Profiling.ProfilingStatusRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PropertyType.IsUsedPropertyTypeController", "Method": "Get", "RelativePath": "umbraco/management/api/v1/property-type/is-used", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contentTypeId", "Type": "System.Guid", "IsRequired": false}, {"Name": "propertyAlias", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Boolean", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PublishedCache.CollectPublishedCacheController", "Method": "Collect", "RelativePath": "umbraco/management/api/v1/published-cache/collect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 501}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PublishedCache.RebuildPublishedCacheController", "Method": "Rebuild", "RelativePath": "umbraco/management/api/v1/published-cache/rebuild", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PublishedCache.ReloadPublishedCacheController", "Method": "Reload", "RelativePath": "umbraco/management/api/v1/published-cache/reload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PublishedCache.StatusPublishedCacheController", "Method": "Status", "RelativePath": "umbraco/management/api/v1/published-cache/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 501}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.EmptyDocumentRecycleBinController", "Method": "EmptyRecycleBin", "RelativePath": "umbraco/management/api/v1/recycle-bin/document", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.DeleteDocumentRecycleBinController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/recycle-bin/document/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.OriginalParentDocumentRecycleBinController", "Method": "OriginalParent", "RelativePath": "umbraco/management/api/v1/recycle-bin/document/{id}/original-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.RestoreDocumentRecycleBinController", "Method": "Rest<PERSON>", "RelativePath": "umbraco/management/api/v1/recycle-bin/document/{id}/restore", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.MoveMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.ChildrenDocumentRecycleBinController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/recycle-bin/document/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Document.RecycleBin.DocumentRecycleBinItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.RootDocumentRecycleBinController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/recycle-bin/document/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Document.RecycleBin.DocumentRecycleBinItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.RecycleBin.EmptyMediaRecycleBinController", "Method": "EmptyRecycleBin", "RelativePath": "umbraco/management/api/v1/recycle-bin/media", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.RecycleBin.DeleteMediaRecycleBinController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/recycle-bin/media/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.RecycleBin.OriginalParentMediaRecycleBinController", "Method": "OriginalParent", "RelativePath": "umbraco/management/api/v1/recycle-bin/media/{id}/original-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.RecycleBin.RestoreMediaRecycleBinController", "Method": "Rest<PERSON>", "RelativePath": "umbraco/management/api/v1/recycle-bin/media/{id}/restore", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "moveDocumentRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Media.MoveMediaRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.RecycleBin.ChildrenMediaRecycleBinController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/recycle-bin/media/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Media.RecycleBin.MediaRecycleBinItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.RecycleBin.RootMediaRecycleBinController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/recycle-bin/media/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Media.RecycleBin.MediaRecycleBinItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RedirectUrlManagement.GetAllRedirectUrlManagementController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/redirect-management", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.RedirectUrlManagement.RedirectUrlResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RedirectUrlManagement.ByKeyRedirectUrlManagementController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/redirect-management/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.RedirectUrlManagement.RedirectUrlResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RedirectUrlManagement.DeleteByKeyRedirectUrlManagementController", "Method": "DeleteByKey", "RelativePath": "umbraco/management/api/v1/redirect-management/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RedirectUrlManagement.GetStatusRedirectUrlManagementController", "Method": "GetStatus", "RelativePath": "umbraco/management/api/v1/redirect-management/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.RedirectUrlManagement.RedirectUrlStatusResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RedirectUrlManagement.SetStatusRedirectUrlManagementController", "Method": "SetStatus", "RelativePath": "umbraco/management/api/v1/redirect-management/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RelationType.AllRelationTypeController", "Method": "Get", "RelativePath": "umbraco/management/api/v1/relation-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.RelationType.RelationTypeResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.RelationType.ByKeyRelationTypeController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/relation-type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.RelationType.RelationTypeResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Relation.ByRelationTypeKeyRelationController", "Method": "ByRelationTypeKey", "RelativePath": "umbraco/management/api/v1/relation/type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Relation.RelationResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Microsoft.AspNetCore.Mvc.ProblemDetails, Microsoft.AspNetCore.Http.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}], "EndpointName": "GetRelationByRelationTypeId"}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.CreateScriptController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/script", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Script.CreateScriptRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.ByPathScriptController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/script/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Script.ScriptResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.DeleteScriptController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/script/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.UpdateScriptController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/script/{path}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Script.UpdateScriptRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.RenameScriptController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/script/{path}/rename", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Script.RenameScriptRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Folder.CreateScriptFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/script/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Script.Folder.CreateScriptFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Folder.ByPathScriptFolderController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/script/folder/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Script.Folder.ScriptFolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Folder.DeleteScriptFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/script/folder/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Searcher.AllSearcherController", "Method": "All", "RelativePath": "umbraco/management/api/v1/searcher", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Searcher.SearcherResponse, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Searcher.QuerySearcherController", "Method": "Query", "RelativePath": "umbraco/management/api/v1/searcher/{searcherName}/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcherName", "Type": "System.String", "IsRequired": true}, {"Name": "term", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Searcher.SearchResultResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Security.ConfigurationSecurityController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/security/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Security.SecurityConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Security.ResetPasswordController", "Method": "RequestPasswordReset", "RelativePath": "umbraco/management/api/v1/security/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.Security.ResetPasswordRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Security.ResetPasswordTokenController", "Method": "ResetPasswordToken", "RelativePath": "umbraco/management/api/v1/security/forgot-password/reset", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.Security.ResetPasswordTokenRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Umbraco.Cms.Api.Common.Builders.ProblemDetailsBuilder", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Common.Builders.ProblemDetailsBuilder", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Security.VerifyResetPasswordTokenController", "Method": "VerifyResetPasswordToken", "RelativePath": "umbraco/management/api/v1/security/forgot-password/verify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.Security.VerifyResetPasswordTokenRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Security.VerifyResetPasswordResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Umbraco.Cms.Api.Common.Builders.ProblemDetailsBuilder", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Common.Builders.ProblemDetailsBuilder", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Segment.AllSegmentController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/segment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Segment.SegmentResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Server.ConfigurationServerController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/server/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Server.ServerConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Server.InformationServerController", "Method": "Information", "RelativePath": "umbraco/management/api/v1/server/information", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Server.ServerInformationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Server.StatusServerController", "Method": "Get", "RelativePath": "umbraco/management/api/v1/server/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.Server.ServerStatusResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Server.TroubleshootingServerController", "Method": "GetTroubleshooting", "RelativePath": "umbraco/management/api/v1/server/troubleshooting", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Server.ServerTroubleshootingResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Server.UpgradeCheckServerController", "Method": "UpgradeCheck", "RelativePath": "umbraco/management/api/v1/server/upgrade-check", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Server.UpgradeCheckResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.CreateStylesheetController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/stylesheet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.CreateStylesheetRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.ByPathStylesheetController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/stylesheet/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.StylesheetResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.DeleteStylesheetController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/stylesheet/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.UpdateStylesheetController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/stylesheet/{path}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.UpdateStylesheetRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.RenameStylesheetController", "Method": "<PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/stylesheet/{path}/rename", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.RenameStylesheetRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Folder.CreateStylesheetFolderController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/stylesheet/folder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.Folder.CreateStylesheetFolderRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Folder.ByPathStylesheetFolderController", "Method": "ByPath", "RelativePath": "umbraco/management/api/v1/stylesheet/folder/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Stylesheet.Folder.StylesheetFolderResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Folder.DeleteStylesheetFolderController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/stylesheet/folder/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Tag.ByQueryTagController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/tag", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "tagGroup", "Type": "System.String", "IsRequired": false}, {"Name": "culture", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tag.TagResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Telemetry.AllTelemetryController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/telemetry", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Telemetry.TelemetryResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Telemetry.GetTelemetryController", "Method": "Get", "RelativePath": "umbraco/management/api/v1/telemetry/level", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Telemetry.TelemetryResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Telemetry.SetTelemetryController", "Method": "SetConsentLevel", "RelativePath": "umbraco/management/api/v1/telemetry/level", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "telemetryRepresentationBase", "Type": "Umbraco.Cms.Api.Management.ViewModels.Telemetry.TelemetryRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.CreateTemplateController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/template", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Template.CreateTemplateRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.ByKeyTemplateController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/template/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Template.TemplateResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.DeleteTemplateController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/template/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.UpdateTemplateController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/template/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Template.UpdateTemplateRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.ConfigurationTemplateController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/template/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Template.TemplateConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Query.ExecuteTemplateQueryController", "Method": "Execute", "RelativePath": "umbraco/management/api/v1/template/query/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "Umbraco.Cms.Api.Management.ViewModels.Template.Query.TemplateQueryExecuteModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Template.Query.TemplateQueryResultResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Query.SettingsTemplateQueryController", "Method": "Settings", "RelativePath": "umbraco/management/api/v1/template/query/settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Template.Query.TemplateQuerySettingsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.TemporaryFile.CreateTemporaryFileController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/temporary-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Guid", "IsRequired": false}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.TemporaryFile.ByKeyTemporaryFileController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/temporary-file/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Umbraco.Cms.Api.Management.ViewModels.TemporaryFile.TemporaryFileResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.TemporaryFile.DeleteTemporaryFileController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/temporary-file/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.TemporaryFile.ConfigurationTemporaryFileController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/temporary-file/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.TemporaryFile.TemporaryFileConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Tree.AncestorsDataTypeTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/data-type/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DataTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Tree.ChildrenDataTypeTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/data-type/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DataTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DataType.Tree.RootDataTypeTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/data-type/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DataTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.Tree.AncestorsDictionaryTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/dictionary/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.Tree.ChildrenDictionaryTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/dictionary/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Dictionary.Tree.RootDictionaryTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/dictionary/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Tree.AncestorsDocumentBlueprintTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/document-blueprint/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentBlueprintTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Tree.ChildrenDocumentBlueprintTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/document-blueprint/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentBlueprintTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentBlueprint.Tree.RootDocumentBlueprintTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/document-blueprint/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentBlueprintTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Tree.AncestorsDocumentTypeTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/document-type/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Tree.ChildrenDocumentTypeTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/document-type/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.DocumentType.Tree.RootDocumentTypeTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/document-type/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Tree.AncestorsDocumentTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/document/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Tree.ChildrenDocumentTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/document/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Document.Tree.RootDocumentTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/document/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.DocumentTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Tree.AncestorsMediaTypeTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/media-type/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Tree.ChildrenMediaTypeTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/media-type/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MediaType.Tree.RootMediaTypeTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/media-type/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "foldersOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Tree.AncestorsMediaTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/media/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Tree.ChildrenMediaTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/media/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Media.Tree.RootMediaTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/media/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "dataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MediaTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberGroup.Tree.RootMemberGroupTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/member-group/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.MemberType.Tree.RootMemberTypeTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/member-type/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.MemberTypeTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Tree.AncestorsPartialViewTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/partial-view/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Tree.ChildrenPartialViewTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/partial-view/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentPath", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.PartialView.Tree.RootPartialViewTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/partial-view/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Tree.AncestorsScriptTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/script/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Tree.ChildrenScriptTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/script/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentPath", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Script.Tree.RootScriptTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/script/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.StaticFile.Tree.AncestorsStaticFileTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/static-file/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.StaticFile.Tree.ChildrenStaticFileTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/static-file/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentPath", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.StaticFile.Tree.RootStaticFileTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/static-file/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Tree.AncestorsStylesheetTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/stylesheet/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Tree.ChildrenStylesheetTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/stylesheet/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentPath", "Type": "System.String", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Stylesheet.Tree.RootStylesheetTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/stylesheet/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.FileSystemTreeItemPresentationModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Tree.AncestorsTemplateTreeController", "Method": "Ancestors", "RelativePath": "umbraco/management/api/v1/tree/template/ancestors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "descendantId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Tree.ChildrenTemplateTreeController", "Method": "Children", "RelativePath": "umbraco/management/api/v1/tree/template/children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Guid", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Template.Tree.RootTemplateTreeController", "Method": "Root", "RelativePath": "umbraco/management/api/v1/tree/template/root", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Tree.NamedEntityTreeItemResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Upgrade.AuthorizeUpgradeController", "Method": "Authorize", "RelativePath": "umbraco/management/api/v1/upgrade/authorize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 428}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 500}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Upgrade.SettingsUpgradeController", "Method": "Settings", "RelativePath": "umbraco/management/api/v1/upgrade/settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Installer.UpgradeSettingsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 428}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.BulkDeleteUserController", "Method": "DeleteUsers", "RelativePath": "umbraco/management/api/v1/user", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.DeleteUsersRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.CreateUserController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.CreateUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.GetAllUserController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.User.UserResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserData.CreateUserDataController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/user-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.UserData.CreateUserDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Umbraco.Cms.Core.Services.OperationStatus.UserDataOperationStatus", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Core.Services.OperationStatus.UserDataOperationStatus", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserData.GetUserDataController", "Method": "GetUserData", "RelativePath": "umbraco/management/api/v1/user-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groups", "Type": "System.String[]", "IsRequired": false}, {"Name": "identifiers", "Type": "System.String[]", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.UserData.UserDataResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserData.UpdateUserDataController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/user-data", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.UserData.UpdateUserDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Umbraco.Cms.Core.Services.OperationStatus.UserDataOperationStatus", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Umbraco.Cms.Core.Services.OperationStatus.UserDataOperationStatus", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserData.ByKeyUserDataController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/user-data/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.UserData.UserDataViewModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.BulkDeleteUserGroupsController", "Method": "BulkDelete", "RelativePath": "umbraco/management/api/v1/user-group", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.UserGroup.DeleteUserGroupsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.CreateUserGroupController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/user-group", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUserGroupRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.UserGroup.CreateUserGroupRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.GetAllUserGroupController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/user-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.UserGroup.UserGroupResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.ByKeyUserGroupController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/user-group/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.UserGroup.UserGroupResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.DeleteUserGroupController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/user-group/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.UpdateUserGroupController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/user-group/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateUserGroupRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.UserGroup.UpdateUserGroupRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.AddUsersToUserGroupController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/user-group/{id}/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userIds", "Type": "Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.UserGroup.RemoveUsersFromUserGroupController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/user-group/{id}/users", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userIds", "Type": "Umbraco.Cms.Api.Management.ViewModels.ReferenceByIdModel[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ByKeyUserController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/user/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.UserResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.DeleteUserController", "Method": "DeleteUser", "RelativePath": "umbraco/management/api/v1/user/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.UpdateUserController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/user/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.UpdateUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ListTwoFactorProvidersUserController", "Method": "ListTwoFactorProviders", "RelativePath": "umbraco/management/api/v1/user/{id}/2fa", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.UserTwoFactorProviderModel, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.DisableTwoFactorProviderUserController", "Method": "DisableTwoFactorProvider", "RelativePath": "umbraco/management/api/v1/user/{id}/2fa/{providerName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "providerName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.CalculatedStartNodesUserController", "Method": "CalculatedStartNodes", "RelativePath": "umbraco/management/api/v1/user/{id}/calculate-start-nodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.CalculatedUserStartNodesResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ChangePasswordUserController", "Method": "ChangePassword", "RelativePath": "umbraco/management/api/v1/user/{id}/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.ChangePasswordUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ClientCredentials.CreateClientCredentialsUserController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/user/{id}/client-credentials", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.ClientCredentials.CreateUserClientCredentialsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ClientCredentials.GetAllClientCredentialsUserController", "Method": "GetAll", "RelativePath": "umbraco/management/api/v1/user/{id}/client-credentials", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ClientCredentials.DeleteClientCredentialsUserController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/user/{id}/client-credentials/{clientId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "clientId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ResetPasswordUserController", "Method": "ResetPassword", "RelativePath": "umbraco/management/api/v1/user/{id}/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.ResetPasswordUserResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ClearAvatarUserController", "Method": "ClearAvatar", "RelativePath": "umbraco/management/api/v1/user/avatar/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.SetAvatarUserController", "Method": "Set<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/user/avatar/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.SetAvatarRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ConfigurationUserController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/user/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.UserConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.GetCurrentUserController", "Method": "GetCurrentUser", "RelativePath": "umbraco/management/api/v1/user/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.CurrentUserResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.ListTwoFactorProvidersCurrentUserController", "Method": "ListTwoFactorProvidersForCurrentUser", "RelativePath": "umbraco/management/api/v1/user/current/2fa", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.UserTwoFactorProviderModel, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.DisableTwoFactorProviderCurrentUserController", "Method": "DisableTwoFactorProvider", "RelativePath": "umbraco/management/api/v1/user/current/2fa/{providerName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "providerName", "Type": "System.String", "IsRequired": true}, {"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.EnableTwoFactorProviderCurrentUserController", "Method": "EnableTwoFactorProvider", "RelativePath": "umbraco/management/api/v1/user/current/2fa/{providerName}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "providerName", "Type": "System.String", "IsRequired": true}, {"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.EnableTwoFactorRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Security.ISetupTwoFactorModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.GetTwoFactorSetupForProviderCurrentUserController", "Method": "GetTwoFactorProviderByName", "RelativePath": "umbraco/management/api/v1/user/current/2fa/{providerName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "providerName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Core.Security.ISetupTwoFactorModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.SetAvatarCurrentUserController", "Method": "Set<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/user/current/avatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.SetAvatarRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.ChangePasswordCurrentUserController", "Method": "ChangePassword", "RelativePath": "umbraco/management/api/v1/user/current/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.ChangePasswordCurrentUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.ConfigurationCurrentUserController", "Method": "Configuration", "RelativePath": "umbraco/management/api/v1/user/current/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.CurrenUserConfigurationResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.ListExternalLoginProvidersCurrentUserController", "Method": "ListTwoFactorProvidersForCurrentUser", "RelativePath": "umbraco/management/api/v1/user/current/login-providers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Core.Models.UserExternalLoginProviderModel, Umbraco.Core, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.GetPermissionsCurrentUserController", "Method": "GetPermissions", "RelativePath": "umbraco/management/api/v1/user/current/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.UserPermissionsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.GetDocumentPermissionsCurrentUserController", "Method": "GetPermissions", "RelativePath": "umbraco/management/api/v1/user/current/permissions/document", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Umbraco.Cms.Api.Management.ViewModels.User.Current.UserPermissionsResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.Current.GetMediaPermissionsCurrentUserController", "Method": "GetPermissions", "RelativePath": "umbraco/management/api/v1/user/current/permissions/media", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Collections.Generic.HashSet`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.Current.UserPermissionsResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.DisableUserController", "Method": "DisableUsers", "RelativePath": "umbraco/management/api/v1/user/disable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.DisableUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.EnableUserController", "Method": "EnableUsers", "RelativePath": "umbraco/management/api/v1/user/enable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.EnableUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.InviteUserController", "Method": "Invite", "RelativePath": "umbraco/management/api/v1/user/invite", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.InviteUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.CreateInitialPasswordUserController", "Method": "CreateInitialPassword", "RelativePath": "umbraco/management/api/v1/user/invite/create-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.CreateInitialPasswordUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.ResendInviteUserController", "Method": "ResendInvite", "RelativePath": "umbraco/management/api/v1/user/invite/resend", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.ResendInviteUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.VerifyInviteUserController", "Method": "Invite", "RelativePath": "umbraco/management/api/v1/user/invite/verify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.VerifyInviteUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.User.VerifyInviteUserResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.UpdateUserGroupsUserController", "Method": "UpdateUserGroups", "RelativePath": "umbraco/management/api/v1/user/set-user-groups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.UpdateUserGroupsOnUserRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.User.UnlockUserController", "Method": "UnlockUsers", "RelativePath": "umbraco/management/api/v1/user/unlock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Umbraco.Cms.Api.Management.ViewModels.User.UnlockUsersRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.AllWebhookController", "Method": "All", "RelativePath": "umbraco/management/api/v1/webhook", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Webhook.WebhookResponseModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.CreateWebhookController", "Method": "Create", "RelativePath": "umbraco/management/api/v1/webhook", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createWebhookRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Webhook.CreateWebhookRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.ByKeyWebhookController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "umbraco/management/api/v1/webhook/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Management.ViewModels.Webhook.WebhookResponseModel", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.DeleteWebhookController", "Method": "Delete", "RelativePath": "umbraco/management/api/v1/webhook/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.UpdateWebhookController", "Method": "Update", "RelativePath": "umbraco/management/api/v1/webhook/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateWebhookRequestModel", "Type": "Umbraco.Cms.Api.Management.ViewModels.Webhook.UpdateWebhookRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json", "application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Umbraco.Cms.Api.Management.Controllers.Webhook.EventsWebhookController", "Method": "All", "RelativePath": "umbraco/management/api/v1/webhook/events", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Umbraco.Cms.Api.Common.ViewModels.Pagination.PagedViewModel`1[[Umbraco.Cms.Api.Management.ViewModels.Webhook.WebhookEventViewModel, Umbraco.Cms.Api.Management, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json", "application/json"], "StatusCode": 200}]}]