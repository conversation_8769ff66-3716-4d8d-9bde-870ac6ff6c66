{"@t":"2025-07-20T11:03:57.4719911Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:57.4786781Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2663796Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2696398Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2726766Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2730570Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2732414Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2733756Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2735300Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2736709Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2738062Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2739935Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2741616Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2742815Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2744688Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.2745240Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.4153800Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.4156318Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.4157165Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:03:58.4157380Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:04:36.6309808Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6331227Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6397860Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6398325Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6399101Z","@mt":"Skipping {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6399185Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:36.6399636Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.0351697Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.0352436Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.0353129Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.0396972Z","@mt":"Database configuration status: Started","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1072661Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUser\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userDisabled\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userDisabled\" DEFAULT ('0')\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_key\" DEFAULT (NEWID())\r\n, \"userNoConsole\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userNoConsole\" DEFAULT ('0')\r\n, \"userName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLogin\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userPassword\" TEXT COLLATE NOCASE NOT NULL\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"userEmail\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLanguage\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"failedLoginAttempts\" INTEGER NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"invitedDate\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_updateDate\" DEFAULT (DATE())\r\n, \"kind\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_kind\" DEFAULT ('0')\r\n, \"avatar\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_user UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1085877Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUser_userKey\" ON \"umbracoUser\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1087501Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUser_userLogin\" ON \"umbracoUser\" (\"userLogin\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1092262Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1222035Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1223535Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1240754Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_uniqueId\" DEFAULT (NEWID())\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"level\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, \"trashed\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoNode_trashed\" DEFAULT ('0')\r\n, \"nodeUser\" INTEGER NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"nodeObjectType\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoNode_umbracoNode_id FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoNode_umbracoUser_id FOREIGN KEY (\"nodeUser\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1244022Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoNode_UniqueId\" ON \"umbracoNode\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1245778Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_parentId_nodeObjectType\" ON \"umbracoNode\" (\"parentID\",\"nodeObjectType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1247871Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_Level\" ON \"umbracoNode\" (\"level\",\"parentId\",\"sortOrder\",\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1249881Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_Path\" ON \"umbracoNode\" (\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1251342Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType_trashed_sorted\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\",\"sortOrder\",\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1252583Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_Trashed\" ON \"umbracoNode\" (\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1254541Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1255920Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1356863Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1357437Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1360073Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsContentType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"thumbnail\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsContentType_thumbnail\" DEFAULT ('folder.png')\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"listView\" TEXT NULL\r\n, \"isElement\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_isElement\" DEFAULT ('0')\r\n, \"allowAtRoot\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_allowAtRoot\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_variations\" DEFAULT ('1')\r\n, CONSTRAINT PK_cmsContentType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsContentType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1362020Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsContentType\" ON \"cmsContentType\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1362983Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsContentType_icon\" ON \"cmsContentType\" (\"icon\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1363686Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1445616Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1446070Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1449406Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsTemplate\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_cmsTemplate UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsTemplate_umbracoNode FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1454810Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsTemplate_nodeId\" ON \"cmsTemplate\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1460404Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1460977Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1461193Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1464194Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoContent\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoContent PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoContent_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContent_cmsContentType_NodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"NodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1465615Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1465829Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1466268Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1469329Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoContentVersion\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"versionDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoContentVersion_versionDate\" DEFAULT (DATE())\r\n, \"userId\" INTEGER NULL\r\n, \"current\" INTEGER NOT NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"preventCleanup\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoContentVersion_preventCleanup\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoContentVersion UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersion_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentVersion_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1471131Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_NodeId\" ON \"umbracoContentVersion\" (\"nodeId\",\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1472290Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_Current\" ON \"umbracoContentVersion\" (\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1472867Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1472971Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1473028Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1475280Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoMediaVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoMediaVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoMediaVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1477022Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoMediaVersion\" ON \"umbracoMediaVersion\" (\"id\",\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1478576Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1479262Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1479443Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1481090Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDocument\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocument PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDocument_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1482784Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoDocument_Published\" ON \"umbracoDocument\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1483816Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1484046Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1484149Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1485854Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsDocumentType\r\n(\r\n \"contentTypeNodeId\" INTEGER NOT NULL\r\n, \"templateNodeId\" INTEGER NOT NULL\r\n, \"IsDefault\" INTEGER NOT NULL CONSTRAINT \"DF_cmsDocumentType_IsDefault\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsDocumentType PRIMARY KEY (\"contentTypeNodeId\", \"templateNodeId\")\r\n, CONSTRAINT FK_cmsDocumentType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsDocumentType_umbracoNode_id FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsDocumentType_cmsTemplate_nodeId FOREIGN KEY (\"templateNodeId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1487338Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1487528Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1487662Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1490061Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDataType\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"propertyEditorAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"propertyEditorUiAlias\" TEXT COLLATE NOCASE NULL\r\n, \"dbType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"config\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDataType PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDataType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1491292Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1554730Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1555153Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1557621Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsDictionary\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"id\" TEXT NOT NULL\r\n, \"parent\" TEXT NULL\r\n, \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsDictionary UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsDictionary_cmsDictionary_id FOREIGN KEY (\"parent\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1561146Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_id\" ON \"cmsDictionary\" (\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1562944Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsDictionary_Parent\" ON \"cmsDictionary\" (\"parent\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1565055Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_key\" ON \"cmsDictionary\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1566760Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1566993Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1567413Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1573933Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoLanguage\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageISOCode\" TEXT COLLATE NOCASE NULL\r\n, \"languageCultureName\" TEXT COLLATE NOCASE NULL\r\n, \"isDefaultVariantLang\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_isDefaultVariantLang\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_mandatory\" DEFAULT ('0')\r\n, \"fallbackLanguageId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoLanguage UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLanguage_umbracoLanguage_id FOREIGN KEY (\"fallbackLanguageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1577093Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoLanguage_languageISOCode\" ON \"umbracoLanguage\" (\"languageISOCode\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1578754Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoLanguage_fallbackLanguageId\" ON \"umbracoLanguage\" (\"fallbackLanguageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1581261Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1613102Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1613563Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1616020Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsLanguageText\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"UniqueId\" TEXT NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsLanguageText UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsLanguageText_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_cmsLanguageText_cmsDictionary_id FOREIGN KEY (\"UniqueId\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1617822Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsLanguageText_languageId\" ON \"cmsLanguageText\" (\"languageId\",\"UniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1618666Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1618754Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1618815Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1620195Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDomain\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"domainDefaultLanguage\" INTEGER NULL\r\n, \"domainRootStructureID\" INTEGER NULL\r\n, \"domainName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDomain UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDomain_umbracoNode_id FOREIGN KEY (\"domainRootStructureID\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1621301Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1621398Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1621450Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1623146Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NULL\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"entityType\" TEXT COLLATE NOCASE NULL\r\n, \"Datestamp\" TEXT NOT NULL CONSTRAINT \"DF_umbracoLog_Datestamp\" DEFAULT (DATE())\r\n, \"logHeader\" TEXT COLLATE NOCASE NOT NULL\r\n, \"logComment\" TEXT COLLATE NOCASE NULL\r\n, \"parameters\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoLog UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLog_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1624161Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoLog\" ON \"umbracoLog\" (\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1624977Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp\" ON \"umbracoLog\" (\"Datestamp\",\"userId\",\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1625605Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp_logheader\" ON \"umbracoLog\" (\"Datestamp\",\"logHeader\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1626101Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1626433Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1626493Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1628182Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsMemberType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"propertytypeId\" INTEGER NOT NULL\r\n, \"memberCanEdit\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_memberCanEdit\" DEFAULT ('0')\r\n, \"viewOnProfile\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_viewOnProfile\" DEFAULT ('0')\r\n, \"isSensitive\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_isSensitive\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsMemberType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsMemberType_umbracoNode_id FOREIGN KEY (\"NodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsMemberType_cmsContentType_nodeId FOREIGN KEY (\"NodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1629263Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1629352Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1629418Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1631406Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsMember\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"Email\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Email\" DEFAULT ('''')\r\n, \"LoginName\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_LoginName\" DEFAULT ('''')\r\n, \"Password\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Password\" DEFAULT ('''')\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"failedPasswordAttempts\" INTEGER NULL\r\n, \"isLockedOut\" INTEGER NULL CONSTRAINT \"DF_cmsMember_isLockedOut\" DEFAULT ('0')\r\n, \"isApproved\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMember_isApproved\" DEFAULT ('1')\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, CONSTRAINT PK_cmsMember PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_cmsMember_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1632740Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsMember_LoginName\" ON \"cmsMember\" (\"LoginName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1633381Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1633453Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1633514Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1634475Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsMember2MemberGroup\r\n(\r\n \"Member\" INTEGER NOT NULL\r\n, \"MemberGroup\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsMember2MemberGroup PRIMARY KEY (\"Member\", \"MemberGroup\")\r\n, CONSTRAINT FK_cmsMember2MemberGroup_cmsMember_nodeId FOREIGN KEY (\"Member\") REFERENCES \"cmsMember\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsMember2MemberGroup_umbracoNode_id FOREIGN KEY (\"MemberGroup\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1635330Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1635404Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1635461Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1636578Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsPropertyTypeGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_uniqueID\" DEFAULT (NEWID())\r\n, \"contenttypeNodeId\" INTEGER NOT NULL\r\n, \"type\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_type\" DEFAULT ('0')\r\n, \"text\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortorder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsPropertyTypeGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyTypeGroup_cmsContentType_nodeId FOREIGN KEY (\"contenttypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1637489Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeGroupUniqueID\" ON \"cmsPropertyTypeGroup\" (\"uniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1638070Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1674064Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1674527Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1677372Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsPropertyType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"dataTypeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, \"propertyTypeGroupId\" INTEGER NULL\r\n, \"Alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Name\" TEXT COLLATE NOCASE NULL\r\n, \"sortOrder\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_sortOrder\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_mandatory\" DEFAULT ('0')\r\n, \"mandatoryMessage\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExp\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExpMessage\" TEXT COLLATE NOCASE NULL\r\n, \"Description\" TEXT COLLATE NOCASE NULL\r\n, \"labelOnTop\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_labelOnTop\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_variations\" DEFAULT ('1')\r\n, \"UniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyType_UniqueID\" DEFAULT (NEWID())\r\n, CONSTRAINT PK_cmsPropertyType UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyType_umbracoDataType_nodeId FOREIGN KEY (\"dataTypeId\") REFERENCES \"umbracoDataType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsPropertyTypeGroup_id FOREIGN KEY (\"propertyTypeGroupId\") REFERENCES \"cmsPropertyTypeGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1679288Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsPropertyTypeAlias\" ON \"cmsPropertyType\" (\"Alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1680224Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeUniqueID\" ON \"cmsPropertyType\" (\"UniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1680836Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1737368Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1738044Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1742458Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoPropertyData\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"segment\" TEXT COLLATE NOCASE NULL\r\n, \"intValue\" INTEGER NULL\r\n, \"decimalValue\" TEXT NULL\r\n, \"dateValue\" TEXT NULL\r\n, \"varcharValue\" TEXT COLLATE NOCASE NULL\r\n, \"textValue\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoPropertyData UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoPropertyData_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_cmsPropertyType_id FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1744346Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoPropertyData_VersionId\" ON \"umbracoPropertyData\" (\"versionId\",\"propertyTypeId\",\"languageId\",\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1745250Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_PropertyTypeId\" ON \"umbracoPropertyData\" (\"propertyTypeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1745867Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_LanguageId\" ON \"umbracoPropertyData\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1746736Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_Segment\" ON \"umbracoPropertyData\" (\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1747378Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1747540Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1747603Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1749160Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoRelationType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"typeUniqueId\" TEXT NOT NULL\r\n, \"dual\" INTEGER NOT NULL\r\n, \"parentObjectType\" TEXT NULL\r\n, \"childObjectType\" TEXT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isDependency\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoRelationType_isDependency\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoRelationType UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1750406Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_UniqueId\" ON \"umbracoRelationType\" (\"typeUniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1751273Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_name\" ON \"umbracoRelationType\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1751821Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_alias\" ON \"umbracoRelationType\" (\"alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1752243Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1775724Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1776031Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1777979Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoRelation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"childId\" INTEGER NOT NULL\r\n, \"relType\" INTEGER NOT NULL\r\n, \"datetime\" TEXT NOT NULL CONSTRAINT \"DF_umbracoRelation_datetime\" DEFAULT (DATE())\r\n, \"comment\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRelation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoRelation_umbracoNode FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoNode1 FOREIGN KEY (\"childId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoRelationType_id FOREIGN KEY (\"relType\") REFERENCES \"umbracoRelationType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1780614Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelation_parentChildType\" ON \"umbracoRelation\" (\"parentId\",\"childId\",\"relType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1785368Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1787111Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1787446Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1792469Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsTags\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"tag\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsTags UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsTags_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1794135Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsTags_languageId_group\" ON \"cmsTags\" (\"languageId\",\"group\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1795098Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsTags_LanguageId\" ON \"cmsTags\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1795718Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_cmsTags\" ON \"cmsTags\" (\"group\",\"tag\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1796253Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1796323Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1796380Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1798015Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsTagRelationship\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"tagId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsTagRelationship PRIMARY KEY (\"nodeId\", \"propertyTypeId\", \"tagId\")\r\n, CONSTRAINT FK_cmsTagRelationship_cmsContent FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsTags_id FOREIGN KEY (\"tagId\") REFERENCES \"cmsTags\" (\"id\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsPropertyType FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1799235Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsTagRelationship_tagId_nodeId\" ON \"cmsTagRelationship\" (\"tagId\",\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1800047Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1800208Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1800265Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1801500Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsContentType2ContentType\r\n(\r\n \"parentContentTypeId\" INTEGER NOT NULL\r\n, \"childContentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsContentType2ContentType PRIMARY KEY (\"parentContentTypeId\", \"childContentTypeId\")\r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_parent FOREIGN KEY (\"parentContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_child FOREIGN KEY (\"childContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1802564Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1802738Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1802838Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1805747Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsContentTypeAllowedContentType\r\n(\r\n \"Id\" INTEGER NOT NULL\r\n, \"AllowedId\" INTEGER NOT NULL\r\n, \"SortOrder\" INTEGER NOT NULL CONSTRAINT df_cmsContentTypeAllowedContentType_sortOrder DEFAULT ('0')\r\n, CONSTRAINT PK_cmsContentTypeAllowedContentType PRIMARY KEY (\"Id\", \"AllowedId\")\r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType FOREIGN KEY (\"Id\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType1 FOREIGN KEY (\"AllowedId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1807057Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1840316Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1840616Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1842253Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUser2NodeNotify\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2NodeNotify PRIMARY KEY (\"userId\", \"nodeId\", \"action\")\r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1843643Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1843851Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1843996Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1845563Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUser2ClientId\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"clientId\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2ClientId PRIMARY KEY (\"userId\", \"clientId\")\r\n, CONSTRAINT FK_umbracoUser2ClientId_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1846757Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1846953Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1847083Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1848634Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoServer\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"address\" TEXT COLLATE NOCASE NOT NULL\r\n, \"computerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"registeredDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoServer_registeredDate\" DEFAULT (DATE())\r\n, \"lastNotifiedDate\" TEXT NOT NULL\r\n, \"isActive\" INTEGER NOT NULL\r\n, \"isSchedulingPublisher\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoServer UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1850183Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_computerName\" ON \"umbracoServer\" (\"computerName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1851225Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoServer_isActive\" ON \"umbracoServer\" (\"isActive\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1852168Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1852335Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1852401Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1854106Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoAccess\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"loginNodeId\" INTEGER NOT NULL\r\n, \"noAccessNodeId\" INTEGER NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccess PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id1 FOREIGN KEY (\"loginNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id2 FOREIGN KEY (\"noAccessNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1855356Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccess_nodeId\" ON \"umbracoAccess\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1856222Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1856405Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1856542Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1858522Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoAccessRule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"accessId\" TEXT NOT NULL\r\n, \"ruleValue\" TEXT COLLATE NOCASE NOT NULL\r\n, \"ruleType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccessRule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccessRule_umbracoAccess_id FOREIGN KEY (\"accessId\") REFERENCES \"umbracoAccess\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1859604Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccessRule\" ON \"umbracoAccessRule\" (\"ruleValue\",\"ruleType\",\"accessId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1860367Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1860492Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1860599Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1862015Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoCacheInstruction\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"utcStamp\" TEXT NOT NULL\r\n, \"jsonInstruction\" TEXT COLLATE NOCASE NOT NULL\r\n, \"originated\" TEXT COLLATE NOCASE NOT NULL\r\n, \"instructionCount\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoCacheInstruction_instructionCount\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoCacheInstruction UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1863014Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1863190Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1863327Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1865067Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoExternalLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"loginProvider\" TEXT COLLATE NOCASE NOT NULL\r\n, \"providerKey\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLogin_createDate\" DEFAULT (DATE())\r\n, \"userData\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoExternalLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1865989Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_userOrMemberKey\" ON \"umbracoExternalLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1866559Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLogin_LoginProvider\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1867125Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_ProviderKey\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"providerKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1867683Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1867744Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1867795Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1869285Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoExternalLoginToken\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"externalLoginId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLoginToken_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoExternalLoginToken UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoExternalLoginToken_umbracoExternalLogin_id FOREIGN KEY (\"externalLoginId\") REFERENCES \"umbracoExternalLogin\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1870382Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLoginToken_Name\" ON \"umbracoExternalLoginToken\" (\"externalLoginId\",\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1871018Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1871087Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1871424Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1872894Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoTwoFactorLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"providerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"secret\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoTwoFactorLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1873945Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoTwoFactorLogin_userOrMemberKey\" ON \"umbracoTwoFactorLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1874680Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoTwoFactorLogin_ProviderName\" ON \"umbracoTwoFactorLogin\" (\"providerName\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1875352Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1875481Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1875584Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1877654Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoRedirectUrl\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"contentKey\" TEXT NOT NULL\r\n, \"createDateUtc\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"culture\" TEXT COLLATE NOCASE NULL\r\n, \"urlHash\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRedirectUrl PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoRedirectUrl_umbracoNode_uniqueID FOREIGN KEY (\"contentKey\") REFERENCES \"umbracoNode\" (\"uniqueID\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1878847Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoRedirectUrl_culture_hash\" ON \"umbracoRedirectUrl\" (\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1880104Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRedirectUrl\" ON \"umbracoRedirectUrl\" (\"urlHash\",\"contentKey\",\"culture\",\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1880910Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1881024Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1881084Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1882318Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoLock\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"value\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLock PRIMARY KEY (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1883119Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1894863Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1895166Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1897969Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_key\" DEFAULT (NEWID())\r\n, \"userGroupAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupDefaultPermissions\" TEXT COLLATE NOCASE NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_updateDate\" DEFAULT (DATE())\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"hasAccessToAllLanguages\" INTEGER NOT NULL\r\n, \"startContentId\" INTEGER NULL\r\n, \"startMediaId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoUserGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_startContentId_umbracoNode_id FOREIGN KEY (\"startContentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_startMediaId_umbracoNode_id FOREIGN KEY (\"startMediaId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1899703Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupKey\" ON \"umbracoUserGroup\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1900717Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupAlias\" ON \"umbracoUserGroup\" (\"userGroupAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1901591Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupName\" ON \"umbracoUserGroup\" (\"userGroupName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1902535Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1934205Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1934602Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1937860Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUser2UserGroup\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"userGroupId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_user2userGroup PRIMARY KEY (\"userId\", \"userGroupId\")\r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1940068Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1963349Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1963862Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1968718Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserGroup2App\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"app\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2App PRIMARY KEY (\"userGroupId\", \"app\")\r\n, CONSTRAINT FK_umbracoUserGroup2App_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1970822Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1984761Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1985071Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1986928Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserGroup2Permission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2Permission UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2Permission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1988398Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2Permission_userGroupKey\" ON \"umbracoUserGroup2Permission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.1989514Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2032158Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2032589Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2035072Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserGroup2GranularPermission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"uniqueId\" TEXT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserGroup2GranularPermissionDto UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2036849Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UserGroupKey_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2038068Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2039059Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2039186Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2039247Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2041067Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserStartNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NOT NULL\r\n, \"startNode\" INTEGER NOT NULL\r\n, \"startNodeType\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userStartNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoNode_id FOREIGN KEY (\"startNode\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2042281Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserStartNode_startNodeType\" ON \"umbracoUserStartNode\" (\"startNodeType\",\"startNode\",\"userId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2043189Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2043271Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2043328Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2045208Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE cmsContentNu\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"data\" TEXT COLLATE NOCASE NULL\r\n, \"rv\" INTEGER NOT NULL\r\n, \"dataRaw\" BLOB NULL\r\n, CONSTRAINT PK_cmsContentNu PRIMARY KEY (\"nodeId\", \"published\")\r\n, CONSTRAINT FK_cmsContentNu_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2046439Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_cmsContentNu_published\" ON \"cmsContentNu\" (\"published\",\"nodeId\",\"rv\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2047349Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2047507Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2047566Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2049251Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDocumentVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"templateId\" INTEGER NULL\r\n, \"published\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocumentVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentVersion_cmsTemplate_nodeId FOREIGN KEY (\"templateId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2050396Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_id_published\" ON \"umbracoDocumentVersion\" (\"id\",\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2051769Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_published\" ON \"umbracoDocumentVersion\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2052622Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2052767Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2052870Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2055132Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDocumentUrl\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL\r\n, \"isDraft\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"urlSegment\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isPrimary\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoDocumentUrl_isPrimary\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoDocumentUrl UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2056360Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentUrl\" ON \"umbracoDocumentUrl\" (\"uniqueId\",\"languageId\",\"isDraft\",\"urlSegment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2057239Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2057433Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2057492Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2059043Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoKeyValue\r\n(\r\n \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NULL\r\n, \"updated\" TEXT NOT NULL CONSTRAINT \"DF_umbracoKeyValue_updated\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoKeyValue PRIMARY KEY (\"key\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2059930Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2101314Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2101827Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2104291Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserLogin\r\n(\r\n \"sessionId\" TEXT NOT NULL\r\n, \"userId\" INTEGER NOT NULL\r\n, \"loggedInUtc\" TEXT NOT NULL\r\n, \"lastValidatedUtc\" TEXT NOT NULL\r\n, \"loggedOutUtc\" TEXT NULL\r\n, \"ipAddress\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoUserLogin PRIMARY KEY (\"sessionId\")\r\n, CONSTRAINT FK_umbracoUserLogin_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2106187Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUserLogin_lastValidatedUtc\" ON \"umbracoUserLogin\" (\"lastValidatedUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2107300Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2107466Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2107577Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2109452Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoConsent\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"current\" INTEGER NOT NULL\r\n, \"source\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoConsent_createDate\" DEFAULT (DATE())\r\n, \"state\" INTEGER NOT NULL\r\n, \"comment\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoConsent UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2113057Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2113866Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2115540Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2118181Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoAudit\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"performingUserId\" INTEGER NOT NULL\r\n, \"performingDetails\" TEXT COLLATE NOCASE NULL\r\n, \"performingIp\" TEXT COLLATE NOCASE NULL\r\n, \"eventDateUtc\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAudit_eventDateUtc\" DEFAULT (DATE())\r\n, \"affectedUserId\" INTEGER NOT NULL\r\n, \"affectedDetails\" TEXT COLLATE NOCASE NULL\r\n, \"eventType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventDetails\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoAudit UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2119705Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2119850Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2119909Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2123943Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoContentVersionCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"availableUserId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoContentVersionCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoUser_id FOREIGN KEY (\"availableUserId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2125833Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoContentVersionCultureVariation_VersionId\" ON \"umbracoContentVersionCultureVariation\" (\"versionId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2127163Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoContentVersionCultureVariation_LanguageId\" ON \"umbracoContentVersionCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2128110Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2128313Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2128379Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2130547Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoDocumentCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, \"available\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDocumentCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2132009Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentCultureVariation_NodeId\" ON \"umbracoDocumentCultureVariation\" (\"nodeId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2133020Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoDocumentCultureVariation_LanguageId\" ON \"umbracoDocumentCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2133778Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2133912Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2134083Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2136048Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoContentSchedule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoContentSchedule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2137484Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2137676Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2137839Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2139293Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoLogViewerQuery\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"query\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLogViewerQuery UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2140516Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_LogViewerQuery_name\" ON \"umbracoLogViewerQuery\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2141534Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2155344Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2155660Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2157823Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoContentVersionCleanupPolicy\r\n(\r\n \"contentTypeId\" INTEGER NOT NULL\r\n, \"preventCleanup\" INTEGER NOT NULL\r\n, \"keepAllVersionsNewerThanDays\" INTEGER NULL\r\n, \"keepLatestVersionPerDayForDays\" INTEGER NULL\r\n, \"updated\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoContentVersionCleanupPolicy PRIMARY KEY (\"contentTypeId\")\r\n, CONSTRAINT FK_umbracoContentVersionCleanupPolicy_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2159261Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2159467Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2159571Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2161514Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoCreatedPackageSchema\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoCreatedPackageSchema_updateDate\" DEFAULT (DATE())\r\n, \"packageId\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoCreatedPackageSchema UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2162826Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE UNIQUE INDEX \"IX_umbracoCreatedPackageSchema_Name\" ON \"umbracoCreatedPackageSchema\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2163931Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2164172Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2164276Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2166186Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserGroup2Language\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userGroup2language PRIMARY KEY (\"userGroupId\", \"languageId\")\r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  ON DELETE CASCADE \r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2167672Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2167908Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2168017Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2169850Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhook\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"enabled\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhook UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2171187Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2171446Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2171554Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2173229Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhook2ContentTypeKeys\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"entityKey\" TEXT NOT NULL\r\n, CONSTRAINT PK_webhookEntityKey2Webhook PRIMARY KEY (\"webhookId\", \"entityKey\")\r\n, CONSTRAINT FK_umbracoWebhook2ContentTypeKeys_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2174837Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2175095Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2175727Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2178052Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhook2Events\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"event\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_webhookEvent2WebhookDto PRIMARY KEY (\"webhookId\", \"event\")\r\n, CONSTRAINT FK_umbracoWebhook2Events_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2179666Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2179839Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2179904Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2181798Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhook2Headers\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"Key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_heaeders2WebhookDto PRIMARY KEY (\"webhookId\", \"key\")\r\n, CONSTRAINT FK_umbracoWebhook2Headers_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2183588Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2183684Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2183796Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2186128Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhookLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"key\" TEXT NOT NULL\r\n, \"statusCode\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, \"requestHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"exceptionOccured\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookLog UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2187770Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoWebhookLog_date\" ON \"umbracoWebhookLog\" (\"date\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2188867Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2189147Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2189264Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2190975Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoWebhookRequest\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"eventName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestObject\" TEXT COLLATE NOCASE NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookRequest UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2192082Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2192266Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2192471Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2194629Z","@mt":"Create table:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE TABLE umbracoUserData\r\n(\r\n \"key\" TEXT NOT NULL\r\n, \"userKey\" TEXT NOT NULL\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"identifier\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserDataDto PRIMARY KEY (\"key\")\r\n, CONSTRAINT FK_umbracoUserData_umbracoUser_key FOREIGN KEY (\"userKey\") REFERENCES \"umbracoUser\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2195811Z","@mt":"Create Index:\n {Sql}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","Sql":"CREATE  INDEX \"IX_umbracoUserDataDto_UserKey_Group_Identifier\" ON \"umbracoUserData\" (\"userKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2196943Z","@mt":"Creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2197106Z","@mt":"Completed creating data in {TableName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2197167Z","@mt":"New table {TableName} was created","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.2208975Z","@mt":"Database configuration status: {DbConfigStatus}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","DbConfigStatus":"<p>Installation completed!</p>","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.8776873Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.8777622Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.8917421Z","@mt":"Skipping {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.8918308Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.9136846Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:37.9765578Z","@mt":"No last synced Id found, this generally means this is a new server/install. A cold boot will be triggered.","@l":"Warning","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","SourceContext":"Umbraco.Cms.Infrastructure.Sync.SyncBootStateAccessor","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1440125Z","@mt":"Telemetry level set to {telemetryLevel} by {username}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","telemetryLevel":"Detailed","username":"<EMAIL>","SourceContext":"Umbraco.Cms.Core.Services.MetricsConsentService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1480164Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1480705Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1481343Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1664133Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1664765Z","@mt":"Checking if {StepName} requires execution","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.1665864Z","@mt":"Running {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.2390836Z","@mt":"Rebuilding all URLs.","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","SourceContext":"Umbraco.Cms.Core.Services.DocumentUrlService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.2799437Z","@mt":"Database cache was serialized using {CurrentSerializer}. Currently configured cache serializer {Serializer}. Rebuilding database cache.","@l":"Warning","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","CurrentSerializer":"0","Serializer":"MessagePack","SourceContext":"Umbraco.Cms.Infrastructure.HybridCache.DatabaseCacheRebuilder","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.2824086Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StartMessage":"Rebuilding database cache with MessagePack serializer","TimingId":"b4efda7","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.3954314Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","EndMessage":"Completed.","Duration":113,"TimingId":"b4efda7","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.4027009Z","@mt":"Finished {StepName}","@tr":"8dc22b20fdcce6a8dcfadcac9040d560","@sp":"16fa41e52c9d34b5","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"2b5bbaa2-ad39-4853-8dc8-2f36e7643403","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8S:0000002C","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"83673cda-063c-4745-8c55-f4f2f14a014e","HttpRequestNumber":1,"HttpSessionId":"7c0241c8-3caf-59bc-7edd-e75fe844a8f8"}
{"@t":"2025-07-20T11:04:38.8491385Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"60a99f913011cf88a2e34be4a13605c9","@sp":"02d71d71a5ae9643","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8S:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"bbc4f62c-7a9b-48c6-9dc7-aa3d3e53d970","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:38.8541398Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"60a99f913011cf88a2e34be4a13605c9","@sp":"02d71d71a5ae9643","Request":"{\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"k8AnJr5VSc\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"C56kjmWFipD7Dbh4IRy_JZ0JCeLLyMQaJWgCyxt5TZA\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8S:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"bbc4f62c-7a9b-48c6-9dc7-aa3d3e53d970","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:38.8790624Z","@mt":"The authorization request was successfully validated.","@tr":"60a99f913011cf88a2e34be4a13605c9","@sp":"02d71d71a5ae9643","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8S:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8S","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"bbc4f62c-7a9b-48c6-9dc7-aa3d3e53d970","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:47.7470695Z","@mt":"Revoking active tokens for user with ID {id}","@tr":"f64ff47cf7a5c70f748f8dc6cec6ddd8","@sp":"83f4dfb28c64bd45","id":-1,"SourceContext":"Umbraco.Cms.Api.Management.Handlers.RevokeUserAuthenticationTokensNotificationHandler","ActionId":"277a1553-8594-4873-ad66-6472b04beaaf","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Login (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000001","RequestPath":"/umbraco/management/api/v1/security/back-office/login","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"6e0ab5a9-deda-4f57-bbf2-8c939539452b","HttpRequestNumber":3,"HttpSessionId":"08d16385-c1e4-0015-79e2-6ce5dea84827"}
{"@t":"2025-07-20T11:04:47.8070673Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"41e273706318498bb439984ec145938c","@sp":"7bf87d73496d3ac7","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000002","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"483321ad-7062-416b-ad84-8bbd575ddcf1","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:47.8075000Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"41e273706318498bb439984ec145938c","@sp":"7bf87d73496d3ac7","Request":"{\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"k8AnJr5VSc\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"C56kjmWFipD7Dbh4IRy_JZ0JCeLLyMQaJWgCyxt5TZA\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000002","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"483321ad-7062-416b-ad84-8bbd575ddcf1","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:47.8115361Z","@mt":"The authorization request was successfully validated.","@tr":"41e273706318498bb439984ec145938c","@sp":"7bf87d73496d3ac7","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000002","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"483321ad-7062-416b-ad84-8bbd575ddcf1","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:47.9067790Z","@mt":"An ad hoc authorization was automatically created and associated with the '{ClientId}' application: {Identifier}.","@tr":"41e273706318498bb439984ec145938c","@sp":"7bf87d73496d3ac7","ClientId":"umbraco-back-office","Identifier":"c7e91940-5b6d-4e45-ab1e-d98191e59de9","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"64b136e3-8789-475a-af84-5243b80838d2","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000002","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"483321ad-7062-416b-ad84-8bbd575ddcf1","HttpRequestNumber":4,"HttpSessionId":"c88733a2-cde8-0445-06c8-c1124b12c201"}
{"@t":"2025-07-20T11:04:47.9759528Z","@mt":"The authorization response was successfully returned to '{RedirectUri}' using the query response mode: {Response}.","@tr":"41e273706318498bb439984ec145938c","@sp":"7bf87d73496d3ac7","RedirectUri":"http://localhost:5000/umbraco/oauth_complete","Response":"{\r\n  \"code\": \"[redacted]\",\r\n  \"state\": \"k8AnJr5VSc\",\r\n  \"iss\": \"http://localhost:5000/\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"64b136e3-8789-475a-af84-5243b80838d2","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000002","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"483321ad-7062-416b-ad84-8bbd575ddcf1","HttpRequestNumber":4,"HttpSessionId":"c88733a2-cde8-0445-06c8-c1124b12c201"}
{"@t":"2025-07-20T11:04:48.1152308Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"e5979f028bda082c3da15fd026088ba0","@sp":"7de1d4a43154fe6c","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000007","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"68ad712a-b182-4df5-91d5-0614df554515","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.1192491Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"e5979f028bda082c3da15fd026088ba0","@sp":"7de1d4a43154fe6c","Request":"{\r\n  \"grant_type\": \"authorization_code\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"code\": \"[redacted]\",\r\n  \"code_verifier\": \"eXUs8HK20MCGSOZc820vi5NcyH1OsxcreqdOIAXRDc4wiYmbDsqr8B4UtX8yztVUWyns43DXLbdGGuxtsjrhinhnLeR2EBfg37LHcppAuQqUYnCu1Gf53YqZu4c3L1KH\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000007","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"68ad712a-b182-4df5-91d5-0614df554515","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.1879384Z","@mt":"The token request was successfully validated.","@tr":"e5979f028bda082c3da15fd026088ba0","@sp":"7de1d4a43154fe6c","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000007","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"68ad712a-b182-4df5-91d5-0614df554515","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.2046043Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"e5979f028bda082c3da15fd026088ba0","@sp":"7de1d4a43154fe6c","Identifier":"812cfa12-e0d4-4fa6-9f70-a713db9c48cd","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000007","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"68ad712a-b182-4df5-91d5-0614df554515","HttpRequestNumber":5,"HttpSessionId":"7f300dcf-0dbc-0a16-2b53-98a691b35708"}
{"@t":"2025-07-20T11:04:48.2210216Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"e5979f028bda082c3da15fd026088ba0","@sp":"7de1d4a43154fe6c","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000007","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":26,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"68ad712a-b182-4df5-91d5-0614df554515","HttpRequestNumber":5,"HttpSessionId":"7f300dcf-0dbc-0a16-2b53-98a691b35708"}
{"@t":"2025-07-20T11:04:48.2299889Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"5264ecf0182f8963949a15f86307ac30","@sp":"f5168ba51d975211","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e748a85-8eb7-418f-8a83-464d1259b03a","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.2304898Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"5264ecf0182f8963949a15f86307ac30","@sp":"f5168ba51d975211","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e748a85-8eb7-418f-8a83-464d1259b03a","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.2376265Z","@mt":"The token request was successfully validated.","@tr":"5264ecf0182f8963949a15f86307ac30","@sp":"f5168ba51d975211","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U8V:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e748a85-8eb7-418f-8a83-464d1259b03a","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:04:48.2428781Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"5264ecf0182f8963949a15f86307ac30","@sp":"f5168ba51d975211","Identifier":"fb078942-34e1-4799-9e2c-794a919bdf5d","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e748a85-8eb7-418f-8a83-464d1259b03a","HttpRequestNumber":6,"HttpSessionId":"9f4dfed1-42c4-6b90-aab5-283c281d09f4"}
{"@t":"2025-07-20T11:04:48.2539885Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"5264ecf0182f8963949a15f86307ac30","@sp":"f5168ba51d975211","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U8V:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U8V","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":24,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e748a85-8eb7-418f-8a83-464d1259b03a","HttpRequestNumber":6,"HttpSessionId":"9f4dfed1-42c4-6b90-aab5-283c281d09f4"}
{"@t":"2025-07-20T11:05:17.0575242Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"520a3b6cd3c43d3e02721f4f75eae1f2","@sp":"4925f3483767a166","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9B:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9B","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":23,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4503ba07-8589-470d-8582-dc62dcece006","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:05:17.0586009Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"520a3b6cd3c43d3e02721f4f75eae1f2","@sp":"4925f3483767a166","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9B:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9B","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":23,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4503ba07-8589-470d-8582-dc62dcece006","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:05:17.0666271Z","@mt":"The token request was successfully validated.","@tr":"520a3b6cd3c43d3e02721f4f75eae1f2","@sp":"4925f3483767a166","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9B:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9B","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":23,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4503ba07-8589-470d-8582-dc62dcece006","HttpRequestNumber":7,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:05:17.0701175Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"520a3b6cd3c43d3e02721f4f75eae1f2","@sp":"4925f3483767a166","Identifier":"c6a90222-77ba-4e2d-a936-c71373cac07a","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U9B:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9B","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":23,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4503ba07-8589-470d-8582-dc62dcece006","HttpRequestNumber":7,"HttpSessionId":"9adcf786-e26d-8e8b-5535-f4bcab112f38"}
{"@t":"2025-07-20T11:05:17.0820065Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"520a3b6cd3c43d3e02721f4f75eae1f2","@sp":"4925f3483767a166","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U9B:00000008","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9B","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":23,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4503ba07-8589-470d-8582-dc62dcece006","HttpRequestNumber":7,"HttpSessionId":"9adcf786-e26d-8e8b-5535-f4bcab112f38"}
{"@t":"2025-07-20T11:05:38.9841908Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":12,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T11:07:26.4309949Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"d9b9ce7fdef748a7f22126910c6f79ba","@sp":"96fb4f48003a5893","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9H:00000005","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9H","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":28,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e1d0d03-e6b7-468d-a07d-ddfed0c866b1","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:07:26.4311297Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"d9b9ce7fdef748a7f22126910c6f79ba","@sp":"96fb4f48003a5893","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9H:00000005","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9H","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":28,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e1d0d03-e6b7-468d-a07d-ddfed0c866b1","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:07:26.4336567Z","@mt":"The token request was successfully validated.","@tr":"d9b9ce7fdef748a7f22126910c6f79ba","@sp":"96fb4f48003a5893","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7FKL53U9H:00000005","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9H","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":28,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e1d0d03-e6b7-468d-a07d-ddfed0c866b1","HttpRequestNumber":8,"HttpSessionId":"0"}
{"@t":"2025-07-20T11:07:26.4372195Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"d9b9ce7fdef748a7f22126910c6f79ba","@sp":"96fb4f48003a5893","Identifier":"d7a1d64a-5b89-474f-a0c0-ca3d5681c5a6","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U9H:00000005","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9H","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":28,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e1d0d03-e6b7-468d-a07d-ddfed0c866b1","HttpRequestNumber":8,"HttpSessionId":"0b776186-eb94-3804-66d3-4534dbbc052a"}
{"@t":"2025-07-20T11:07:26.4421510Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"d9b9ce7fdef748a7f22126910c6f79ba","@sp":"96fb4f48003a5893","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"33a869f4-13c6-4b9e-b60f-0fafea4d3a17","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7FKL53U9H:00000005","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7FKL53U9H","ProcessId":7044,"ProcessName":"MDDPlus","ThreadId":28,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"9e1d0d03-e6b7-468d-a07d-ddfed0c866b1","HttpRequestNumber":8,"HttpSessionId":"0b776186-eb94-3804-66d3-4534dbbc052a"}
