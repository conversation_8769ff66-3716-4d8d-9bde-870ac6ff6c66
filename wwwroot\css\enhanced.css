/* Enhanced MDD Plus Styles - Professional Fintech Design */

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Statistics Section */
.statistics {
    position: relative;
    overflow: hidden;
}

.statistics::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    pointer-events: none;
}

.stat-item {
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-delay: calc(var(--delay, 0) * 0.1s);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(44, 90, 160, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-item:hover::before {
    left: 100%;
}

/* Enhanced Services Section */
.services {
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(44, 90, 160, 0.02) 50%, transparent 51%);
    pointer-events: none;
}

.service-item {
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    animation: slideInLeft 0.6s ease-out;
    animation-delay: calc(var(--delay, 0) * 0.2s);
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.service-item:hover::before {
    opacity: 0.05;
}

.service-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.service-item:hover .service-icon::before {
    width: 100%;
    height: 100%;
}

/* Enhanced Contact Section */
.contact {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
    pointer-events: none;
}

.contact-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    animation: slideInRight 0.6s ease-out;
    animation-delay: calc(var(--delay, 0) * 0.15s);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.contact-item a {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255, 215, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-item a:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: scale(1.05);
}

/* Enhanced Button Styles */
.btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced Theme Toggles */
.theme-toggle,
.language-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.theme-toggle::before,
.language-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.theme-toggle:hover,
.language-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-2px);
}

.theme-toggle:hover::before,
.language-toggle:hover::before {
    left: 100%;
}

/* Page Loading Overlay */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin: 0 auto 1rem;
}

.loader-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 1rem;
}

/* Enhanced Dark Theme */
[data-theme="dark"] body {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(44, 90, 160, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(30, 61, 114, 0.1) 0%, transparent 50%);
}

[data-theme="dark"] .stat-item,
[data-theme="dark"] .service-item {
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .stat-item:hover,
[data-theme="dark"] .service-item:hover {
    border-color: var(--primary-color);
}

[data-theme="dark"] .navbar {
    background: rgba(15, 15, 15, 0.95);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .contact-item {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .contact-item:hover {
    background: rgba(37, 37, 37, 0.9);
    border-color: rgba(44, 90, 160, 0.5);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: #1a1a1a;
}

/* Selection Styles */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* Accessibility Improvements */
.btn:focus-visible,
.theme-toggle:focus-visible,
.language-toggle:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(44, 90, 160, 0.2);
}

/* Utility Classes */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
}

[data-theme="dark"] .glass {
    background: rgba(26, 26, 26, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.floating {
    animation: float 3s ease-in-out infinite;
}

.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

[data-theme="dark"] .loading-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
}

/* Message States */
.error-message {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.success-message {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

/* Performance Optimizations */
.stat-item,
.service-item,
.contact-item,
.btn {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating,
    .loading-skeleton {
        animation: none;
    }
    
    .stat-item::before,
    .service-item::before,
    .btn::before,
    .theme-toggle::before,
    .language-toggle::before {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .stat-item,
    .service-item {
        border: 2px solid currentColor;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
    
    .hero {
        background: #000;
        color: #fff;
    }
    
    .statistics {
        background: #fff;
        color: #000;
    }
    
    .contact {
        background: #000;
        color: #fff;
    }
}

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
    .btn,
    .theme-toggle,
    .language-toggle,
    .nav-links a {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stat-item,
    .service-item,
    .contact-item {
        animation-delay: 0s;
    }
}

/* Ultra-wide Screen Support */
@media (min-width: 1920px) {
    .stats-grid,
    .services-grid {
        gap: 3rem;
    }
}

/* Print Optimizations */
@media print {
    .page-loader,
    .theme-toggle,
    .language-toggle {
        display: none !important;
    }
    
    .stat-item,
    .service-item {
        break-inside: avoid;
        border: 1px solid #ddd !important;
    }
}

/* Font Smoothing */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Image Optimization */
img {
    max-width: 100%;
    height: auto;
}

/* Z-index Management */
.navbar {
    z-index: 1000;
}

.page-loader {
    z-index: 9999;
}
