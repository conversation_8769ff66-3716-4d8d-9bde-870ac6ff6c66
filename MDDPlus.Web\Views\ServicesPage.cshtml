@using MDDPlus.Web.Models
@model ServicesPage
@{
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
    ViewBag.ActivePage = "services";
    Layout = "_Layout";
}

<!-- Page Header -->
<section class="hero" style="padding: var(--space-16) 0; background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));">
    <div class="container">
        <div class="text-center">
            <h1 class="animate-fade-in-up">@Model.PageTitle</h1>
            <p class="animate-fade-in-up" style="animation-delay: 0.2s; font-size: var(--text-xl); margin-bottom: 0;">
                @Model.PageSubtitle
            </p>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="grid grid-cols-2" style="gap: var(--space-12);">
            <!-- Individual Services -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    </div>
                    <h2>@Model.IndividualServicesTitle</h2>
                    <p class="text-secondary">@Model.IndividualServicesDescription</p>
                </div>
                <div class="card-body">
                    <!-- Investment Service -->
                    <div class="mb-8">
                        <h4 class="mb-3">@Model.InvestmentTitle</h4>
                        <p class="text-secondary mb-4">@Model.InvestmentDescription</p>
                        <div class="grid grid-cols-2" style="gap: var(--space-2);">
                            @foreach (var feature in Model.InvestmentFeatures.Split(','))
                            {
                                <div class="flex items-center" style="gap: var(--space-2);">
                                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                                    <span class="text-sm">@feature.Trim()</span>
                                </div>
                            }
                        </div>
                    </div>
                    
                    <!-- Auto Investment Service -->
                    <div class="mb-8">
                        <h4 class="mb-3">@Model.AutoInvestmentTitle</h4>
                        <p class="text-secondary mb-4">@Model.AutoInvestmentDescription</p>
                        <div class="grid grid-cols-2" style="gap: var(--space-2);">
                            @foreach (var feature in Model.AutoInvestmentFeatures.Split(','))
                            {
                                <div class="flex items-center" style="gap: var(--space-2);">
                                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                                    <span class="text-sm">@feature.Trim()</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/investors" class="btn btn-primary w-full">ابدأ الاستثمار</a>
                </div>
            </div>
            
            <!-- Business Services -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <h2>@Model.BusinessServicesTitle</h2>
                    <p class="text-secondary">@Model.BusinessServicesDescription</p>
                </div>
                <div class="card-body">
                    <!-- Invoice Financing -->
                    <div class="mb-6">
                        <h5 class="mb-2">@Model.InvoiceFinancingTitle</h5>
                        <p class="text-secondary text-sm mb-3">@Model.InvoiceFinancingDescription</p>
                        <div class="text-xs text-primary">@Model.InvoiceFinancingFeatures</div>
                    </div>
                    
                    <!-- Working Capital -->
                    <div class="mb-6">
                        <h5 class="mb-2">@Model.WorkingCapitalTitle</h5>
                        <p class="text-secondary text-sm mb-3">@Model.WorkingCapitalDescription</p>
                        <div class="text-xs text-primary">@Model.WorkingCapitalFeatures</div>
                    </div>
                    
                    <!-- Supply Chain -->
                    <div class="mb-6">
                        <h5 class="mb-2">@Model.SupplyChainTitle</h5>
                        <p class="text-secondary text-sm mb-3">@Model.SupplyChainDescription</p>
                        <div class="text-xs text-primary">@Model.SupplyChainFeatures</div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/business-funding" class="btn btn-secondary w-full">اطلب التمويل</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Detailed Services -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>خدماتنا التفصيلية</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                نقدم مجموعة شاملة من الخدمات التمويلية المتخصصة
            </p>
        </div>
        
        <div class="grid grid-cols-3" style="gap: var(--space-8);">
            <!-- Invoice Financing -->
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                </div>
                <h4>@Model.InvoiceFinancingTitle</h4>
                <p class="text-secondary">@Model.InvoiceFinancingDescription</p>
                <div class="mt-4">
                    <a href="/invoice-financing" class="btn btn-outline btn-sm">اعرف المزيد</a>
                </div>
            </div>
            
            <!-- Purchase Order -->
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                    </svg>
                </div>
                <h4>@Model.PurchaseOrderTitle</h4>
                <p class="text-secondary">@Model.PurchaseOrderDescription</p>
                <div class="mt-4">
                    <a href="/purchase-order" class="btn btn-outline btn-sm">اعرف المزيد</a>
                </div>
            </div>
            
            <!-- Letter of Guarantee -->
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h4>@Model.LetterGuaranteeTitle</h4>
                <p class="text-secondary">@Model.LetterGuaranteeDescription</p>
                <div class="mt-4">
                    <a href="/letter-guarantee" class="btn btn-outline btn-sm">اعرف المزيد</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How We Work -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.ProcessTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                @Model.ProcessDescription
            </p>
        </div>
        
        <div class="grid grid-cols-4" style="gap: var(--space-8);">
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">1</span>
                </div>
                <h5 class="mb-3">التسجيل</h5>
                <p class="text-secondary text-sm">
                    أنشئ حسابك وأكمل عملية التحقق بخطوات بسيطة
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">2</span>
                </div>
                <h5 class="mb-3">اختر الخدمة</h5>
                <p class="text-secondary text-sm">
                    حدد نوع الخدمة التي تحتاجها من قائمة خدماتنا المتنوعة
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">3</span>
                </div>
                <h5 class="mb-3">قدم الطلب</h5>
                <p class="text-secondary text-sm">
                    املأ النموذج وأرفق المستندات المطلوبة
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">4</span>
                </div>
                <h5 class="mb-3">احصل على التمويل</h5>
                <p class="text-secondary text-sm">
                    بعد الموافقة، احصل على التمويل في حسابك خلال 24 ساعة
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Benefits -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.BenefitsTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                @Model.BenefitsDescription
            </p>
        </div>
        
        <div class="grid grid-cols-2" style="gap: var(--space-8);">
            <div class="flex items-start" style="gap: var(--space-4);">
                <div class="feature-icon" style="width: 60px; height: 60px;">
                    <svg width="30" height="30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                </div>
                <div>
                    <h4 class="mb-3">سرعة في المعالجة</h4>
                    <p class="text-secondary">
                        معالجة سريعة للطلبات خلال 24 ساعة مع استخدام أحدث التقنيات
                    </p>
                </div>
            </div>
            
            <div class="flex items-start" style="gap: var(--space-4);">
                <div class="feature-icon" style="width: 60px; height: 60px;">
                    <svg width="30" height="30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <h4 class="mb-3">أسعار تنافسية</h4>
                    <p class="text-secondary">
                        أسعار تمويل تنافسية تبدأ من 0.5% شهرياً مع شفافية كاملة في التكاليف
                    </p>
                </div>
            </div>
            
            <div class="flex items-start" style="gap: var(--space-4);">
                <div class="feature-icon" style="width: 60px; height: 60px;">
                    <svg width="30" height="30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <h4 class="mb-3">متوافق مع الشريعة</h4>
                    <p class="text-secondary">
                        جميع خدماتنا معتمدة من لجنة شرعية مستقلة ومتوافقة مع أحكام الشريعة الإسلامية
                    </p>
                </div>
            </div>
            
            <div class="flex items-start" style="gap: var(--space-4);">
                <div class="feature-icon" style="width: 60px; height: 60px;">
                    <svg width="30" height="30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                </div>
                <div>
                    <h4 class="mb-3">أمان متقدم</h4>
                    <p class="text-secondary">
                        أنظمة حماية متعددة الطبقات وتشفير متقدم لضمان أمان بياناتك وأموالك
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center">
            <h2 class="mb-6">ابدأ مع مدد بلس اليوم</h2>
            <p class="text-xl text-secondary mb-8 max-w-2xl mx-auto">
                اختر الخدمة التي تناسبك واستفد من حلولنا التمويلية المتطورة
            </p>
            <div class="flex justify-center" style="gap: var(--space-4);">
                <a href="/register" class="btn btn-primary btn-lg">
                    ابدأ الآن
                </a>
                <a href="/contact" class="btn btn-outline btn-lg">
                    تحدث مع خبير
                </a>
            </div>
        </div>
    </div>
</section>
