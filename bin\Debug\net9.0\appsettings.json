{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "7b61c545-b8d2-4756-ad46-3ad2944b58de", "SanitizeTinyMce": true}, "Content": {"AllowEditInvariantFromNonDefault": true, "ContentVersionCleanupPolicy": {"EnableCleanup": true}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"AllowConcurrentLogins": false}}}}