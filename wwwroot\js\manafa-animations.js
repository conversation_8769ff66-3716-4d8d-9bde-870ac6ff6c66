// MDD Plus - Advanced Animations & Interactions (Manafa.sa Inspired)

class MDDPlusAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollAnimations();
        this.setupCounterAnimations();
        this.setupParallaxEffects();
        this.setupNavbarEffects();
        this.setupHoverEffects();
        this.setupLoadingAnimations();
        this.setupSmoothScrolling();
        this.setupIntersectionObserver();
        this.setupPerformanceOptimizations();
        this.setupAccessibilityFeatures();
    }

    // Advanced Scroll Animations
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const animationType = element.dataset.animation || 'fadeInUp';
                    const delay = element.dataset.delay || 0;

                    setTimeout(() => {
                        element.classList.add('in-view');
                        this.triggerAnimation(element, animationType);
                    }, delay);

                    observer.unobserve(element);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll, .stat-card, .feature-card, .service-card').forEach(el => {
            observer.observe(el);
        });
    }

    triggerAnimation(element, type) {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
        
        switch(type) {
            case 'fadeInLeft':
                element.style.animation = 'fadeInLeft 0.8s ease-out';
                break;
            case 'fadeInRight':
                element.style.animation = 'fadeInRight 0.8s ease-out';
                break;
            case 'scaleIn':
                element.style.animation = 'scaleIn 0.8s ease-out';
                break;
            case 'slideInUp':
                element.style.animation = 'slideInUp 0.8s ease-out';
                break;
            default:
                element.style.animation = 'fadeInUp 0.8s ease-out';
        }
    }

    // Advanced Counter Animations
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.stat-number');
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }

    animateCounter(element) {
        const target = this.parseNumber(element.textContent);
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            element.textContent = this.formatNumber(Math.floor(current), element.textContent);
        }, 16);

        // Add pulsing effect
        element.style.animation = 'pulse 0.5s ease-in-out';
    }

    parseNumber(text) {
        return parseInt(text.replace(/[^\d]/g, '')) || 0;
    }

    formatNumber(num, originalText) {
        if (originalText.includes('%')) {
            return num + '%';
        } else if (originalText.includes(',')) {
            return num.toLocaleString();
        }
        return num.toString();
    }

    // Parallax Effects
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.hero::before, .floating-card');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        });

        // Floating cards animation
        this.animateFloatingCards();
    }

    animateFloatingCards() {
        const cards = document.querySelectorAll('.floating-card');
        cards.forEach((card, index) => {
            const delay = index * 2000;
            const duration = 6000 + (index * 1000);
            
            card.style.animationDelay = `${delay}ms`;
            card.style.animationDuration = `${duration}ms`;
        });
    }

    // Navbar Effects
    setupNavbarEffects() {
        const navbar = document.querySelector('.navbar');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Hide/show navbar on scroll
            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollY = currentScrollY;
        });
    }

    // Advanced Hover Effects
    setupHoverEffects() {
        // Card tilt effect
        this.setupCardTiltEffect();
        
        // Button ripple effect
        this.setupButtonRippleEffect();
        
        // Magnetic effect for buttons
        this.setupMagneticEffect();
    }

    setupCardTiltEffect() {
        const cards = document.querySelectorAll('.stat-card, .feature-card, .service-card');
        
        cards.forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
            });
        });
    }

    setupButtonRippleEffect() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    setupMagneticEffect() {
        const magneticElements = document.querySelectorAll('.btn-primary');
        
        magneticElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translate(0, 0)';
            });
        });
    }

    // Loading Animations
    setupLoadingAnimations() {
        // Stagger animation for grid items
        const grids = document.querySelectorAll('.stats-grid, .features-grid, .services-grid');
        
        grids.forEach(grid => {
            const items = grid.children;
            Array.from(items).forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // Progressive image loading
        this.setupProgressiveImageLoading();
    }

    setupProgressiveImageLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Smooth Scrolling
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                
                if (target) {
                    const headerHeight = document.querySelector('.navbar').offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Advanced Intersection Observer
    setupIntersectionObserver() {
        // Section highlighting in navigation
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-links a[href^="#"]');
        
        const sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.getAttribute('id');
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${id}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        }, { threshold: 0.3 });

        sections.forEach(section => {
            sectionObserver.observe(section);
        });
    }

    // Performance Optimizations
    setupPerformanceOptimizations() {
        // Throttle scroll events
        let ticking = false;
        
        const throttledScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', throttledScroll, { passive: true });

        // Preload critical resources
        this.preloadCriticalResources();
    }

    handleScroll() {
        const scrolled = window.pageYOffset;
        
        // Update parallax elements
        document.documentElement.style.setProperty('--scroll', scrolled);
        
        // Update progress indicator if exists
        const progressBar = document.querySelector('.scroll-progress');
        if (progressBar) {
            const winHeight = window.innerHeight;
            const docHeight = document.documentElement.scrollHeight;
            const progress = scrolled / (docHeight - winHeight);
            progressBar.style.transform = `scaleX(${progress})`;
        }
    }

    preloadCriticalResources() {
        // Preload fonts
        const fonts = [
            'Inter',
            'Tajawal'
        ];

        fonts.forEach(font => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
            link.href = `https://fonts.googleapis.com/css2?family=${font}:wght@400;500;600;700;800&display=swap`;
            document.head.appendChild(link);
        });
    }

    // Accessibility Features
    setupAccessibilityFeatures() {
        // Keyboard navigation
        this.setupKeyboardNavigation();
        
        // Focus management
        this.setupFocusManagement();
        
        // Reduced motion support
        this.setupReducedMotionSupport();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
            
            if (e.key === 'Escape') {
                // Close any open modals or dropdowns
                document.querySelectorAll('.modal, .dropdown').forEach(el => {
                    el.classList.remove('open');
                });
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    setupFocusManagement() {
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        
        document.querySelectorAll(focusableElements).forEach(element => {
            element.addEventListener('focus', () => {
                element.classList.add('focused');
            });
            
            element.addEventListener('blur', () => {
                element.classList.remove('focused');
            });
        });
    }

    setupReducedMotionSupport() {
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        
        if (prefersReducedMotion.matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.01ms');
            document.documentElement.style.setProperty('--transition-duration', '0.01ms');
        }
    }

    // Utility Methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Performance Monitoring
    logPerformanceMetrics() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');
                    
                    console.group('🚀 MDD Plus Performance Metrics');
                    console.log(`📊 DOM Content Loaded: ${navigation.domContentLoadedEventEnd.toFixed(2)}ms`);
                    console.log(`🎨 First Paint: ${paint[0]?.startTime.toFixed(2)}ms`);
                    console.log(`🖼️ First Contentful Paint: ${paint[1]?.startTime.toFixed(2)}ms`);
                    console.log(`⚡ Load Complete: ${navigation.loadEventEnd.toFixed(2)}ms`);
                    console.groupEnd();
                }, 1000);
            });
        }
    }
}

// Advanced Theme Manager
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
        this.setupSystemThemeDetection();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.updateThemeColor(theme);
    }

    updateThemeColor(theme) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        const color = theme === 'dark' ? '#1a202c' : '#1a365d';
        
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', color);
        }
    }

    setupThemeToggle() {
        const toggle = document.querySelector('.theme-toggle');
        if (toggle) {
            toggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
    }

    setupSystemThemeDetection() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                this.currentTheme = e.matches ? 'dark' : 'light';
                this.applyTheme(this.currentTheme);
            }
        });
    }
}

// Language Manager
class LanguageManager {
    constructor() {
        this.currentLang = localStorage.getItem('language') || 'ar';
        this.init();
    }

    init() {
        this.applyLanguage(this.currentLang);
        this.setupLanguageToggle();
    }

    applyLanguage(lang) {
        document.documentElement.setAttribute('lang', lang);
        document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
        
        // Update content based on language
        this.updateContent(lang);
    }

    updateContent(lang) {
        const elements = document.querySelectorAll('[data-en][data-ar]');
        elements.forEach(element => {
            element.textContent = element.getAttribute(`data-${lang}`);
        });
    }

    setupLanguageToggle() {
        const toggle = document.querySelector('.language-toggle');
        if (toggle) {
            toggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
    }

    toggleLanguage() {
        this.currentLang = this.currentLang === 'ar' ? 'en' : 'ar';
        this.applyLanguage(this.currentLang);
        localStorage.setItem('language', this.currentLang);
    }
}

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const animations = new MDDPlusAnimations();
    const themeManager = new ThemeManager();
    const languageManager = new LanguageManager();
    
    // Log performance metrics
    animations.logPerformanceMetrics();
    
    console.log('🎉 MDD Plus Advanced Animations Initialized');
});

// Export for global access
window.MDDPlus = {
    animations: MDDPlusAnimations,
    themeManager: ThemeManager,
    languageManager: LanguageManager
};
