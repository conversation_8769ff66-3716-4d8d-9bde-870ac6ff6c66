{"@t":"2025-07-17T11:51:08.4220077Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:09.5810419Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:09.6017899Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:10.6053305Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:11.6114064Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:12.6212681Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:13.6323028Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:14.0713054Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0722478Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0750477Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0752576Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0753758Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0755050Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0755975Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0756898Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0758086Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0759752Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0760782Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0761759Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0762527Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0762802Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1715271Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:51:14.1727489Z","@mt":"BackgroundService failed","@l":"Error","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:51:14.1728559Z","@mt":"The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.","@l":"Fatal","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"FATAL"}
{"@t":"2025-07-17T11:51:14.1730826Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1735085Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1745750Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1762205Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 36\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:24.0997508Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:24.1039451Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:24.1238247Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:25.1357090Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:26.1494991Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:27.1603655Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:28.1757255Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:28.6220984Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6230806Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6252370Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6254459Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6255525Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6256770Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6257896Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6258977Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6259749Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6261515Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6263014Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6264030Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6264998Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6265422Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6566348Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6579246Z","@mt":"BackgroundService failed","@l":"Error","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6580264Z","@mt":"The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.","@l":"Fatal","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"FATAL"}
{"@t":"2025-07-17T11:52:28.6582495Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6587038Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6597169Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 36\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6597874Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
