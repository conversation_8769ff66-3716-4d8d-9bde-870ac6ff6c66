{"@t":"2025-07-17T11:51:08.4220077Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:09.5810419Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:09.6017899Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:10.6053305Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:11.6114064Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:12.6212681Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:13.6323028Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:51:14.0713054Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0722478Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0750477Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0752576Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0753758Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0755050Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0755975Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0756898Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0758086Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0759752Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0760782Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0761759Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0762527Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.0762802Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1715271Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:51:14.1727489Z","@mt":"BackgroundService failed","@l":"Error","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:51:14.1728559Z","@mt":"The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.","@l":"Fatal","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"FATAL"}
{"@t":"2025-07-17T11:51:14.1730826Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1735085Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1745750Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":35728,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:51:14.1762205Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 36\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":35728,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:24.0997508Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:24.1039451Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:24.1238247Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:25.1357090Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:26.1494991Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:27.1603655Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:28.1757255Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:52:28.6220984Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6230806Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6252370Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6254459Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6255525Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6256770Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6257896Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6258977Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6259749Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6261515Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6263014Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6264030Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6264998Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6265422Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6566348Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6579246Z","@mt":"BackgroundService failed","@l":"Error","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6580264Z","@mt":"The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.","@l":"Fatal","@x":"System.OperationCanceledException: The operation was canceled.\r\n   at System.Threading.CancellationToken.ThrowOperationCanceledException()\r\n   at System.Threading.SemaphoreSlim.WaitUntilCountOrTimeoutAsync(TaskNode asyncWaiter, Int32 millisecondsTimeout, CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.BackgroundTaskQueue.DequeueAsync(CancellationToken cancellationToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.BackgroundProcessing(CancellationToken stoppingToken)\r\n   at Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService.ExecuteAsync(CancellationToken stoppingToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"FATAL"}
{"@t":"2025-07-17T11:52:28.6582495Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6587038Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:52:28.6597169Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 36\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":40092,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:52:28.6597874Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":40092,"ProcessName":"dotnet","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:21.7762424Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:21.7817095Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:21.8031830Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:15:22.8052849Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:15:23.8141325Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:15:24.8227222Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:15:25.8304509Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:15:26.2734906Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2760656Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2784034Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2787328Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2788650Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2789454Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2790184Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2790921Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2791662Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2793689Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2795031Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2795910Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2796640Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.2796953Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.3190639Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.3191734Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.3191961Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:15:26.3192039Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":17796,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:56.7030660Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:56.7079199Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1554086Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1578307Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1601034Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1603472Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1604533Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1605284Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1605972Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1606985Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1608269Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1609811Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1610898Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1611768Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1613459Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.1613800Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.2034295Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.2035114Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.2035318Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:16:57.2035384Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:17:41.7702021Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"a455ecb9a4b6a737520c41532224e5c4","@sp":"c7ca5283b3e3de2d","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNE55FMQ3O74:00000001","RequestPath":"/","ConnectionId":"0HNE55FMQ3O74","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":7,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T12:19:17.2019033Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2046530Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2291309Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"FilePermissionsStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2292900Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2296266Z","@mt":"Skipping {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"TelemetryIdentifierStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2297146Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.2299393Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.5935852Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseConfigureStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.5938217Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.5940535Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.6054786Z","@mt":"Database configuration status: Started","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.7865352Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUser\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userDisabled\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userDisabled\" DEFAULT ('0')\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_key\" DEFAULT (NEWID())\r\n, \"userNoConsole\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userNoConsole\" DEFAULT ('0')\r\n, \"userName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLogin\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userPassword\" TEXT COLLATE NOCASE NOT NULL\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"userEmail\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLanguage\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"failedLoginAttempts\" INTEGER NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"invitedDate\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_updateDate\" DEFAULT (DATE())\r\n, \"kind\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_kind\" DEFAULT ('0')\r\n, \"avatar\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_user UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.7896671Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUser_userKey\" ON \"umbracoUser\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.7898657Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUser_userLogin\" ON \"umbracoUser\" (\"userLogin\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.7903480Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8098036Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8099337Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8126346Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_uniqueId\" DEFAULT (NEWID())\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"level\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, \"trashed\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoNode_trashed\" DEFAULT ('0')\r\n, \"nodeUser\" INTEGER NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"nodeObjectType\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoNode_umbracoNode_id FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoNode_umbracoUser_id FOREIGN KEY (\"nodeUser\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8133273Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoNode_UniqueId\" ON \"umbracoNode\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8136392Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_parentId_nodeObjectType\" ON \"umbracoNode\" (\"parentID\",\"nodeObjectType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8138922Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_Level\" ON \"umbracoNode\" (\"level\",\"parentId\",\"sortOrder\",\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8142476Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_Path\" ON \"umbracoNode\" (\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8144898Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType_trashed_sorted\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\",\"sortOrder\",\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8146933Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_Trashed\" ON \"umbracoNode\" (\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8148779Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8151154Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8463543Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8464372Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8473728Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsContentType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"thumbnail\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsContentType_thumbnail\" DEFAULT ('folder.png')\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"listView\" TEXT NULL\r\n, \"isElement\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_isElement\" DEFAULT ('0')\r\n, \"allowAtRoot\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_allowAtRoot\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_variations\" DEFAULT ('1')\r\n, CONSTRAINT PK_cmsContentType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsContentType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8480331Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsContentType\" ON \"cmsContentType\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8484262Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsContentType_icon\" ON \"cmsContentType\" (\"icon\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8487568Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8772637Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8774712Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8780449Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsTemplate\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_cmsTemplate UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsTemplate_umbracoNode FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8784339Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsTemplate_nodeId\" ON \"cmsTemplate\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8786612Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8787184Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8787471Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8791758Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoContent\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoContent PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoContent_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContent_cmsContentType_NodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"NodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8794738Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8795196Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8795490Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8803549Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoContentVersion\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"versionDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoContentVersion_versionDate\" DEFAULT (DATE())\r\n, \"userId\" INTEGER NULL\r\n, \"current\" INTEGER NOT NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"preventCleanup\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoContentVersion_preventCleanup\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoContentVersion UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersion_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentVersion_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8808163Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_NodeId\" ON \"umbracoContentVersion\" (\"nodeId\",\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8811109Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_Current\" ON \"umbracoContentVersion\" (\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8813439Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8813877Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8814178Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8818771Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoMediaVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoMediaVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoMediaVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8821873Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoMediaVersion\" ON \"umbracoMediaVersion\" (\"id\",\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8824432Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8824914Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8825251Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8829652Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDocument\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocument PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDocument_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8832773Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoDocument_Published\" ON \"umbracoDocument\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8835913Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8836499Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8836803Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8843603Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsDocumentType\r\n(\r\n \"contentTypeNodeId\" INTEGER NOT NULL\r\n, \"templateNodeId\" INTEGER NOT NULL\r\n, \"IsDefault\" INTEGER NOT NULL CONSTRAINT \"DF_cmsDocumentType_IsDefault\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsDocumentType PRIMARY KEY (\"contentTypeNodeId\", \"templateNodeId\")\r\n, CONSTRAINT FK_cmsDocumentType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsDocumentType_umbracoNode_id FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsDocumentType_cmsTemplate_nodeId FOREIGN KEY (\"templateNodeId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8847559Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8848106Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8848416Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8855244Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDataType\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"propertyEditorAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"propertyEditorUiAlias\" TEXT COLLATE NOCASE NULL\r\n, \"dbType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"config\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDataType PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDataType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.8858414Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9062772Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9063465Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9070303Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsDictionary\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"id\" TEXT NOT NULL\r\n, \"parent\" TEXT NULL\r\n, \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsDictionary UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsDictionary_cmsDictionary_id FOREIGN KEY (\"parent\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9076002Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_id\" ON \"cmsDictionary\" (\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9081667Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsDictionary_Parent\" ON \"cmsDictionary\" (\"parent\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9084263Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_key\" ON \"cmsDictionary\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9086524Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9086907Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9087441Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9093026Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoLanguage\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageISOCode\" TEXT COLLATE NOCASE NULL\r\n, \"languageCultureName\" TEXT COLLATE NOCASE NULL\r\n, \"isDefaultVariantLang\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_isDefaultVariantLang\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_mandatory\" DEFAULT ('0')\r\n, \"fallbackLanguageId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoLanguage UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLanguage_umbracoLanguage_id FOREIGN KEY (\"fallbackLanguageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9096094Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoLanguage_languageISOCode\" ON \"umbracoLanguage\" (\"languageISOCode\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9098647Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoLanguage_fallbackLanguageId\" ON \"umbracoLanguage\" (\"fallbackLanguageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9100585Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9180640Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9181462Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9190914Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsLanguageText\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"UniqueId\" TEXT NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsLanguageText UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsLanguageText_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_cmsLanguageText_cmsDictionary_id FOREIGN KEY (\"UniqueId\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9200972Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsLanguageText_languageId\" ON \"cmsLanguageText\" (\"languageId\",\"UniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9204623Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9205141Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9205603Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9211621Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDomain\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"domainDefaultLanguage\" INTEGER NULL\r\n, \"domainRootStructureID\" INTEGER NULL\r\n, \"domainName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDomain UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDomain_umbracoNode_id FOREIGN KEY (\"domainRootStructureID\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9215164Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9215690Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9215989Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9222911Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NULL\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"entityType\" TEXT COLLATE NOCASE NULL\r\n, \"Datestamp\" TEXT NOT NULL CONSTRAINT \"DF_umbracoLog_Datestamp\" DEFAULT (DATE())\r\n, \"logHeader\" TEXT COLLATE NOCASE NOT NULL\r\n, \"logComment\" TEXT COLLATE NOCASE NULL\r\n, \"parameters\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoLog UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLog_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9230629Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoLog\" ON \"umbracoLog\" (\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9234827Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp\" ON \"umbracoLog\" (\"Datestamp\",\"userId\",\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9238256Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp_logheader\" ON \"umbracoLog\" (\"Datestamp\",\"logHeader\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9240662Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9241071Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9241375Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9247033Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsMemberType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"propertytypeId\" INTEGER NOT NULL\r\n, \"memberCanEdit\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_memberCanEdit\" DEFAULT ('0')\r\n, \"viewOnProfile\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_viewOnProfile\" DEFAULT ('0')\r\n, \"isSensitive\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_isSensitive\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsMemberType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsMemberType_umbracoNode_id FOREIGN KEY (\"NodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsMemberType_cmsContentType_nodeId FOREIGN KEY (\"NodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9252637Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9253683Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9254019Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9262184Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsMember\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"Email\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Email\" DEFAULT ('''')\r\n, \"LoginName\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_LoginName\" DEFAULT ('''')\r\n, \"Password\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Password\" DEFAULT ('''')\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"failedPasswordAttempts\" INTEGER NULL\r\n, \"isLockedOut\" INTEGER NULL CONSTRAINT \"DF_cmsMember_isLockedOut\" DEFAULT ('0')\r\n, \"isApproved\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMember_isApproved\" DEFAULT ('1')\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, CONSTRAINT PK_cmsMember PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_cmsMember_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9266080Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsMember_LoginName\" ON \"cmsMember\" (\"LoginName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9268386Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9269048Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9270093Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9276734Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsMember2MemberGroup\r\n(\r\n \"Member\" INTEGER NOT NULL\r\n, \"MemberGroup\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsMember2MemberGroup PRIMARY KEY (\"Member\", \"MemberGroup\")\r\n, CONSTRAINT FK_cmsMember2MemberGroup_cmsMember_nodeId FOREIGN KEY (\"Member\") REFERENCES \"cmsMember\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsMember2MemberGroup_umbracoNode_id FOREIGN KEY (\"MemberGroup\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9282634Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9283440Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9283759Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9294455Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsPropertyTypeGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_uniqueID\" DEFAULT (NEWID())\r\n, \"contenttypeNodeId\" INTEGER NOT NULL\r\n, \"type\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_type\" DEFAULT ('0')\r\n, \"text\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortorder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsPropertyTypeGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyTypeGroup_cmsContentType_nodeId FOREIGN KEY (\"contenttypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9299049Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeGroupUniqueID\" ON \"cmsPropertyTypeGroup\" (\"uniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9302290Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9479430Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9480472Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9490387Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsPropertyType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"dataTypeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, \"propertyTypeGroupId\" INTEGER NULL\r\n, \"Alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Name\" TEXT COLLATE NOCASE NULL\r\n, \"sortOrder\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_sortOrder\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_mandatory\" DEFAULT ('0')\r\n, \"mandatoryMessage\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExp\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExpMessage\" TEXT COLLATE NOCASE NULL\r\n, \"Description\" TEXT COLLATE NOCASE NULL\r\n, \"labelOnTop\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_labelOnTop\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_variations\" DEFAULT ('1')\r\n, \"UniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyType_UniqueID\" DEFAULT (NEWID())\r\n, CONSTRAINT PK_cmsPropertyType UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyType_umbracoDataType_nodeId FOREIGN KEY (\"dataTypeId\") REFERENCES \"umbracoDataType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsPropertyTypeGroup_id FOREIGN KEY (\"propertyTypeGroupId\") REFERENCES \"cmsPropertyTypeGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9499014Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsPropertyTypeAlias\" ON \"cmsPropertyType\" (\"Alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9504682Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeUniqueID\" ON \"cmsPropertyType\" (\"UniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9507292Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9774364Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9776396Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9785391Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoPropertyData\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"segment\" TEXT COLLATE NOCASE NULL\r\n, \"intValue\" INTEGER NULL\r\n, \"decimalValue\" TEXT NULL\r\n, \"dateValue\" TEXT NULL\r\n, \"varcharValue\" TEXT COLLATE NOCASE NULL\r\n, \"textValue\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoPropertyData UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoPropertyData_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_cmsPropertyType_id FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9792133Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoPropertyData_VersionId\" ON \"umbracoPropertyData\" (\"versionId\",\"propertyTypeId\",\"languageId\",\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9795576Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_PropertyTypeId\" ON \"umbracoPropertyData\" (\"propertyTypeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9798245Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_LanguageId\" ON \"umbracoPropertyData\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9802302Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_Segment\" ON \"umbracoPropertyData\" (\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9804857Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9805312Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9805845Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9823988Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoRelationType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"typeUniqueId\" TEXT NOT NULL\r\n, \"dual\" INTEGER NOT NULL\r\n, \"parentObjectType\" TEXT NULL\r\n, \"childObjectType\" TEXT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isDependency\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoRelationType_isDependency\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoRelationType UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9828220Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_UniqueId\" ON \"umbracoRelationType\" (\"typeUniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9830418Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_name\" ON \"umbracoRelationType\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9832555Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_alias\" ON \"umbracoRelationType\" (\"alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9834854Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9929065Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9930183Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9938593Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoRelation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"childId\" INTEGER NOT NULL\r\n, \"relType\" INTEGER NOT NULL\r\n, \"datetime\" TEXT NOT NULL CONSTRAINT \"DF_umbracoRelation_datetime\" DEFAULT (DATE())\r\n, \"comment\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRelation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoRelation_umbracoNode FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoNode1 FOREIGN KEY (\"childId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoRelationType_id FOREIGN KEY (\"relType\") REFERENCES \"umbracoRelationType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9943628Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelation_parentChildType\" ON \"umbracoRelation\" (\"parentId\",\"childId\",\"relType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9946694Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9947126Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9947593Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9955548Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsTags\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"tag\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsTags UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsTags_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9960971Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsTags_languageId_group\" ON \"cmsTags\" (\"languageId\",\"group\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9965619Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsTags_LanguageId\" ON \"cmsTags\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9971248Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_cmsTags\" ON \"cmsTags\" (\"group\",\"tag\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9974292Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9974726Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9975047Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9982483Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsTagRelationship\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"tagId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsTagRelationship PRIMARY KEY (\"nodeId\", \"propertyTypeId\", \"tagId\")\r\n, CONSTRAINT FK_cmsTagRelationship_cmsContent FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsTags_id FOREIGN KEY (\"tagId\") REFERENCES \"cmsTags\" (\"id\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsPropertyType FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9988019Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsTagRelationship_tagId_nodeId\" ON \"cmsTagRelationship\" (\"tagId\",\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9992927Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9993477Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:17.9993793Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0002942Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsContentType2ContentType\r\n(\r\n \"parentContentTypeId\" INTEGER NOT NULL\r\n, \"childContentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsContentType2ContentType PRIMARY KEY (\"parentContentTypeId\", \"childContentTypeId\")\r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_parent FOREIGN KEY (\"parentContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_child FOREIGN KEY (\"childContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0007342Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0007902Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0008208Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0016721Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsContentTypeAllowedContentType\r\n(\r\n \"Id\" INTEGER NOT NULL\r\n, \"AllowedId\" INTEGER NOT NULL\r\n, \"SortOrder\" INTEGER NOT NULL CONSTRAINT df_cmsContentTypeAllowedContentType_sortOrder DEFAULT ('0')\r\n, CONSTRAINT PK_cmsContentTypeAllowedContentType PRIMARY KEY (\"Id\", \"AllowedId\")\r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType FOREIGN KEY (\"Id\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType1 FOREIGN KEY (\"AllowedId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0020147Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0146619Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0147604Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0154769Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUser2NodeNotify\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2NodeNotify PRIMARY KEY (\"userId\", \"nodeId\", \"action\")\r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0157387Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0157751Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0158722Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0163130Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUser2ClientId\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"clientId\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2ClientId PRIMARY KEY (\"userId\", \"clientId\")\r\n, CONSTRAINT FK_umbracoUser2ClientId_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0164938Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0165145Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0165353Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2ClientId","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0167747Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoServer\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"address\" TEXT COLLATE NOCASE NOT NULL\r\n, \"computerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"registeredDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoServer_registeredDate\" DEFAULT (DATE())\r\n, \"lastNotifiedDate\" TEXT NOT NULL\r\n, \"isActive\" INTEGER NOT NULL\r\n, \"isSchedulingPublisher\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoServer UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0169917Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_computerName\" ON \"umbracoServer\" (\"computerName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0171355Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoServer_isActive\" ON \"umbracoServer\" (\"isActive\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0172312Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0172546Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0172677Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0175137Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoAccess\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"loginNodeId\" INTEGER NOT NULL\r\n, \"noAccessNodeId\" INTEGER NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccess PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id1 FOREIGN KEY (\"loginNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id2 FOREIGN KEY (\"noAccessNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0176766Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccess_nodeId\" ON \"umbracoAccess\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0177813Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0177977Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0178189Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0180561Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoAccessRule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"accessId\" TEXT NOT NULL\r\n, \"ruleValue\" TEXT COLLATE NOCASE NOT NULL\r\n, \"ruleType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccessRule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccessRule_umbracoAccess_id FOREIGN KEY (\"accessId\") REFERENCES \"umbracoAccess\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0183814Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccessRule\" ON \"umbracoAccessRule\" (\"ruleValue\",\"ruleType\",\"accessId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0186205Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0186577Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0187035Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0191802Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoCacheInstruction\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"utcStamp\" TEXT NOT NULL\r\n, \"jsonInstruction\" TEXT COLLATE NOCASE NOT NULL\r\n, \"originated\" TEXT COLLATE NOCASE NOT NULL\r\n, \"instructionCount\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoCacheInstruction_instructionCount\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoCacheInstruction UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0194618Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0194991Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0195396Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0202505Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoExternalLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"loginProvider\" TEXT COLLATE NOCASE NOT NULL\r\n, \"providerKey\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLogin_createDate\" DEFAULT (DATE())\r\n, \"userData\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoExternalLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0207996Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_userOrMemberKey\" ON \"umbracoExternalLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0210836Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLogin_LoginProvider\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0216812Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_ProviderKey\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"providerKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0222385Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0227855Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0228481Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0237759Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoExternalLoginToken\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"externalLoginId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLoginToken_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoExternalLoginToken UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoExternalLoginToken_umbracoExternalLogin_id FOREIGN KEY (\"externalLoginId\") REFERENCES \"umbracoExternalLogin\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0245531Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLoginToken_Name\" ON \"umbracoExternalLoginToken\" (\"externalLoginId\",\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0252754Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0253761Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0254108Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0261377Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoTwoFactorLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"providerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"secret\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoTwoFactorLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0265819Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoTwoFactorLogin_userOrMemberKey\" ON \"umbracoTwoFactorLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0268946Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoTwoFactorLogin_ProviderName\" ON \"umbracoTwoFactorLogin\" (\"providerName\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0273005Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0273809Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0274118Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0281163Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoRedirectUrl\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"contentKey\" TEXT NOT NULL\r\n, \"createDateUtc\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"culture\" TEXT COLLATE NOCASE NULL\r\n, \"urlHash\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRedirectUrl PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoRedirectUrl_umbracoNode_uniqueID FOREIGN KEY (\"contentKey\") REFERENCES \"umbracoNode\" (\"uniqueID\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0285078Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoRedirectUrl_culture_hash\" ON \"umbracoRedirectUrl\" (\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0288874Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRedirectUrl\" ON \"umbracoRedirectUrl\" (\"urlHash\",\"contentKey\",\"culture\",\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0291095Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0291759Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0292070Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0296199Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoLock\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"value\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLock PRIMARY KEY (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0302131Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0354228Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0355116Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0366972Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_key\" DEFAULT (NEWID())\r\n, \"userGroupAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupDefaultPermissions\" TEXT COLLATE NOCASE NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_updateDate\" DEFAULT (DATE())\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"hasAccessToAllLanguages\" INTEGER NOT NULL\r\n, \"startContentId\" INTEGER NULL\r\n, \"startMediaId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoUserGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_startContentId_umbracoNode_id FOREIGN KEY (\"startContentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_startMediaId_umbracoNode_id FOREIGN KEY (\"startMediaId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0372383Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupKey\" ON \"umbracoUserGroup\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0375020Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupAlias\" ON \"umbracoUserGroup\" (\"userGroupAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0377500Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupName\" ON \"umbracoUserGroup\" (\"userGroupName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0379823Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0505374Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0506496Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0511998Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUser2UserGroup\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"userGroupId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_user2userGroup PRIMARY KEY (\"userId\", \"userGroupId\")\r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0517133Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0571891Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0574041Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0579805Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserGroup2App\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"app\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2App PRIMARY KEY (\"userGroupId\", \"app\")\r\n, CONSTRAINT FK_umbracoUserGroup2App_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0583294Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0637123Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0637902Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0643435Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserGroup2Permission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2Permission UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2Permission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0648704Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2Permission_userGroupKey\" ON \"umbracoUserGroup2Permission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0653774Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0813880Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0814810Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Permission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0822099Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserGroup2GranularPermission\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupKey\" TEXT NOT NULL\r\n, \"uniqueId\" TEXT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserGroup2GranularPermissionDto UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoUserGroup_key FOREIGN KEY (\"userGroupKey\") REFERENCES \"umbracoUserGroup\" (\"key\")  \r\n, CONSTRAINT FK_umbracoUserGroup2GranularPermission_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0826212Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UserGroupKey_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"userGroupKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0831538Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2GranularPermissionDto_UniqueId\" ON \"umbracoUserGroup2GranularPermission\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0836488Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0837215Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0837534Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2GranularPermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0846567Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserStartNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NOT NULL\r\n, \"startNode\" INTEGER NOT NULL\r\n, \"startNodeType\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userStartNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoNode_id FOREIGN KEY (\"startNode\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0850615Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserStartNode_startNodeType\" ON \"umbracoUserStartNode\" (\"startNodeType\",\"startNode\",\"userId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0853466Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0854130Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0854428Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0861128Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE cmsContentNu\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"data\" TEXT COLLATE NOCASE NULL\r\n, \"rv\" INTEGER NOT NULL\r\n, \"dataRaw\" BLOB NULL\r\n, CONSTRAINT PK_cmsContentNu PRIMARY KEY (\"nodeId\", \"published\")\r\n, CONSTRAINT FK_cmsContentNu_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0864643Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_cmsContentNu_published\" ON \"cmsContentNu\" (\"published\",\"nodeId\",\"rv\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0867092Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0867596Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0868859Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0875483Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDocumentVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"templateId\" INTEGER NULL\r\n, \"published\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocumentVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentVersion_cmsTemplate_nodeId FOREIGN KEY (\"templateId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0879720Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_id_published\" ON \"umbracoDocumentVersion\" (\"id\",\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0886465Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_published\" ON \"umbracoDocumentVersion\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0892040Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0892850Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0893201Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0902991Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDocumentUrl\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL\r\n, \"isDraft\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"urlSegment\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isPrimary\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoDocumentUrl_isPrimary\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoDocumentUrl UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoNode_uniqueId FOREIGN KEY (\"uniqueId\") REFERENCES \"umbracoNode\" (\"uniqueId\")  \r\n, CONSTRAINT FK_umbracoDocumentUrl_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0908061Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentUrl\" ON \"umbracoDocumentUrl\" (\"uniqueId\",\"languageId\",\"isDraft\",\"urlSegment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0911834Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0912409Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0912962Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0919383Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoKeyValue\r\n(\r\n \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NULL\r\n, \"updated\" TEXT NOT NULL CONSTRAINT \"DF_umbracoKeyValue_updated\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoKeyValue PRIMARY KEY (\"key\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.0927281Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1045070Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1046046Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1060188Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserLogin\r\n(\r\n \"sessionId\" TEXT NOT NULL\r\n, \"userId\" INTEGER NOT NULL\r\n, \"loggedInUtc\" TEXT NOT NULL\r\n, \"lastValidatedUtc\" TEXT NOT NULL\r\n, \"loggedOutUtc\" TEXT NULL\r\n, \"ipAddress\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoUserLogin PRIMARY KEY (\"sessionId\")\r\n, CONSTRAINT FK_umbracoUserLogin_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1067506Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUserLogin_lastValidatedUtc\" ON \"umbracoUserLogin\" (\"lastValidatedUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1073976Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1074805Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1075188Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1083767Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoConsent\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"current\" INTEGER NOT NULL\r\n, \"source\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoConsent_createDate\" DEFAULT (DATE())\r\n, \"state\" INTEGER NOT NULL\r\n, \"comment\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoConsent UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1085505Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1085712Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1085961Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1088857Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoAudit\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"performingUserId\" INTEGER NOT NULL\r\n, \"performingDetails\" TEXT COLLATE NOCASE NULL\r\n, \"performingIp\" TEXT COLLATE NOCASE NULL\r\n, \"eventDateUtc\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAudit_eventDateUtc\" DEFAULT (DATE())\r\n, \"affectedUserId\" INTEGER NOT NULL\r\n, \"affectedDetails\" TEXT COLLATE NOCASE NULL\r\n, \"eventType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventDetails\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoAudit UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1090504Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1090703Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1090830Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1094324Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoContentVersionCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"availableUserId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoContentVersionCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoUser_id FOREIGN KEY (\"availableUserId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1096042Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoContentVersionCultureVariation_VersionId\" ON \"umbracoContentVersionCultureVariation\" (\"versionId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1097476Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoContentVersionCultureVariation_LanguageId\" ON \"umbracoContentVersionCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1098722Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1098979Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1099104Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1101944Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoDocumentCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, \"available\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDocumentCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1103664Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentCultureVariation_NodeId\" ON \"umbracoDocumentCultureVariation\" (\"nodeId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1108279Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoDocumentCultureVariation_LanguageId\" ON \"umbracoDocumentCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1111308Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1111746Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1112136Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1118224Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoContentSchedule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoContentSchedule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1123523Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1124164Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1127258Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1133826Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoLogViewerQuery\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"query\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLogViewerQuery UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1139601Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_LogViewerQuery_name\" ON \"umbracoLogViewerQuery\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1144454Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1193617Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1194455Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1205861Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoContentVersionCleanupPolicy\r\n(\r\n \"contentTypeId\" INTEGER NOT NULL\r\n, \"preventCleanup\" INTEGER NOT NULL\r\n, \"keepAllVersionsNewerThanDays\" INTEGER NULL\r\n, \"keepLatestVersionPerDayForDays\" INTEGER NULL\r\n, \"updated\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoContentVersionCleanupPolicy PRIMARY KEY (\"contentTypeId\")\r\n, CONSTRAINT FK_umbracoContentVersionCleanupPolicy_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1211643Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1212551Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1214744Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1221430Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoCreatedPackageSchema\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoCreatedPackageSchema_updateDate\" DEFAULT (DATE())\r\n, \"packageId\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoCreatedPackageSchema UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1234327Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE UNIQUE INDEX \"IX_umbracoCreatedPackageSchema_Name\" ON \"umbracoCreatedPackageSchema\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1236139Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1236464Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1236608Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1242340Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserGroup2Language\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userGroup2language PRIMARY KEY (\"userGroupId\", \"languageId\")\r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  ON DELETE CASCADE \r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1245635Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1245895Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1246246Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1249420Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhook\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"enabled\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhook UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1251569Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1251801Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1252005Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1254518Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhook2ContentTypeKeys\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"entityKey\" TEXT NOT NULL\r\n, CONSTRAINT PK_webhookEntityKey2Webhook PRIMARY KEY (\"webhookId\", \"entityKey\")\r\n, CONSTRAINT FK_umbracoWebhook2ContentTypeKeys_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1256181Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1257254Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1257552Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1261444Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhook2Events\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"event\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_webhookEvent2WebhookDto PRIMARY KEY (\"webhookId\", \"event\")\r\n, CONSTRAINT FK_umbracoWebhook2Events_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1265559Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1266410Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1268904Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1279239Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhook2Headers\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"Key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_heaeders2WebhookDto PRIMARY KEY (\"webhookId\", \"key\")\r\n, CONSTRAINT FK_umbracoWebhook2Headers_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1285459Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1286134Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1286687Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1294700Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhookLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"key\" TEXT NOT NULL\r\n, \"statusCode\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, \"requestHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"exceptionOccured\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookLog UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1298049Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoWebhookLog_date\" ON \"umbracoWebhookLog\" (\"date\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1301178Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1301596Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1302332Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1307404Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoWebhookRequest\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"eventName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestObject\" TEXT COLLATE NOCASE NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookRequest UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1310391Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1310885Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1311166Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1317215Z","@mt":"Create table:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE TABLE umbracoUserData\r\n(\r\n \"key\" TEXT NOT NULL\r\n, \"userKey\" TEXT NOT NULL\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"identifier\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserDataDto PRIMARY KEY (\"key\")\r\n, CONSTRAINT FK_umbracoUserData_umbracoUser_key FOREIGN KEY (\"userKey\") REFERENCES \"umbracoUser\" (\"key\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1320212Z","@mt":"Create Index:\n {Sql}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","Sql":"CREATE  INDEX \"IX_umbracoUserDataDto_UserKey_Group_Identifier\" ON \"umbracoUserData\" (\"userKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1328730Z","@mt":"Creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1329252Z","@mt":"Completed creating data in {TableName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1329567Z","@mt":"New table {TableName} was created","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","TableName":"umbracoUserData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:18.1362639Z","@mt":"Database configuration status: {DbConfigStatus}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","DbConfigStatus":"<p>Installation completed!</p>","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseBuilder","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.4367128Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseInstallStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.4367788Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.4480140Z","@mt":"Skipping {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"DatabaseUpgradeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.4480619Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.4717623Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.5559283Z","@mt":"No last synced Id found, this generally means this is a new server/install. A cold boot will be triggered.","@l":"Warning","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","SourceContext":"Umbraco.Cms.Infrastructure.Sync.SyncBootStateAccessor","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.8688897Z","@mt":"Telemetry level set to {telemetryLevel} by {username}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","telemetryLevel":"Detailed","username":"<EMAIL>","SourceContext":"Umbraco.Cms.Core.Services.MetricsConsentService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.8747881Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"CreateUserStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.8750845Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.8751865Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.9372633Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RegisterInstallCompleteStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.9374620Z","@mt":"Checking if {StepName} requires execution","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:19.9376875Z","@mt":"Running {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.2195237Z","@mt":"Rebuilding all URLs.","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","SourceContext":"Umbraco.Cms.Core.Services.DocumentUrlService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.2609585Z","@mt":"Database cache was serialized using {CurrentSerializer}. Currently configured cache serializer {Serializer}. Rebuilding database cache.","@l":"Warning","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","CurrentSerializer":"0","Serializer":"MessagePack","SourceContext":"Umbraco.Cms.Infrastructure.HybridCache.DatabaseCacheRebuilder","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.2653602Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StartMessage":"Rebuilding database cache with MessagePack serializer","TimingId":"e001e35","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.4332898Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","EndMessage":"Completed.","Duration":168,"TimingId":"e001e35","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.4483329Z","@mt":"Finished {StepName}","@tr":"da0ace33d64cc355a45f9b7f544822f6","@sp":"9232376e2e751aa1","StepName":"RestartRuntimeStep","SourceContext":"Umbraco.Cms.Core.Services.Installer.InstallService","ActionId":"25d002f8-3b88-49c7-9f96-320a872c6f99","ActionName":"Umbraco.Cms.Api.Management.Controllers.Install.SetupInstallController.Setup (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O77:0000002D","RequestPath":"/umbraco/management/api/v1/install/setup","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"59455c8c-233e-4178-9dbd-e99928b4419d","HttpRequestNumber":1,"HttpSessionId":"258f65c1-c196-9ab4-aaff-5daa7a3de9f3"}
{"@t":"2025-07-17T12:19:20.9840383Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"b6a1c903bfcc83bb1469225af22f2a34","@sp":"4452b059d526e751","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O77:0000002E","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":17,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"18ae9590-60a0-452e-8c3f-1c03b57981b9","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:20.9953807Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"b6a1c903bfcc83bb1469225af22f2a34","@sp":"4452b059d526e751","Request":"{\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"4LT5dpznC5\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"QnSa_uh4LD_ClgZLYeXowLMvDIs0KvxeVxDBp58gxMc\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O77:0000002E","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":17,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"18ae9590-60a0-452e-8c3f-1c03b57981b9","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:21.0347162Z","@mt":"The authorization request was successfully validated.","@tr":"b6a1c903bfcc83bb1469225af22f2a34","@sp":"4452b059d526e751","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O77:0000002E","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O77","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":17,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"18ae9590-60a0-452e-8c3f-1c03b57981b9","HttpRequestNumber":2,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.0595582Z","@mt":"Revoking active tokens for user with ID {id}","@tr":"7edc08b3d1a440eebd9829ec9e459432","@sp":"c84943d00755b586","id":-1,"SourceContext":"Umbraco.Cms.Api.Management.Handlers.RevokeUserAuthenticationTokensNotificationHandler","ActionId":"5a8df02e-b2d5-44cb-96e1-3481caa78009","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Login (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:0000002C","RequestPath":"/umbraco/management/api/v1/security/back-office/login","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"bdcac7a8-9012-4b78-8e86-71f2c959db66","HttpRequestNumber":3,"HttpSessionId":"f615f278-99fd-dca5-1713-46135904ade4"}
{"@t":"2025-07-17T12:19:32.2015908Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"7a9e2fa5ce2d4e53cbe8b316f88a4186","@sp":"270aa982c6866a36","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f779d738-891e-4687-991a-705d7112c5be","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.2022189Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"7a9e2fa5ce2d4e53cbe8b316f88a4186","@sp":"270aa982c6866a36","Request":"{\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"4LT5dpznC5\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"QnSa_uh4LD_ClgZLYeXowLMvDIs0KvxeVxDBp58gxMc\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f779d738-891e-4687-991a-705d7112c5be","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.2084811Z","@mt":"The authorization request was successfully validated.","@tr":"7a9e2fa5ce2d4e53cbe8b316f88a4186","@sp":"270aa982c6866a36","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f779d738-891e-4687-991a-705d7112c5be","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.4247147Z","@mt":"An ad hoc authorization was automatically created and associated with the '{ClientId}' application: {Identifier}.","@tr":"7a9e2fa5ce2d4e53cbe8b316f88a4186","@sp":"270aa982c6866a36","ClientId":"umbraco-back-office","Identifier":"fc17df28-95ef-4fa3-b7ce-5ceff79f0a4d","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"c2fdaa33-8484-41ec-9ca2-3526febb6520","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f779d738-891e-4687-991a-705d7112c5be","HttpRequestNumber":4,"HttpSessionId":"a800764d-c2f7-acea-24fe-b7208c7ccb64"}
{"@t":"2025-07-17T12:19:32.5509096Z","@mt":"The authorization response was successfully returned to '{RedirectUri}' using the query response mode: {Response}.","@tr":"7a9e2fa5ce2d4e53cbe8b316f88a4186","@sp":"270aa982c6866a36","RedirectUri":"http://localhost:5000/umbraco/oauth_complete","Response":"{\r\n  \"code\": \"[redacted]\",\r\n  \"state\": \"4LT5dpznC5\",\r\n  \"iss\": \"http://localhost:5000/\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"c2fdaa33-8484-41ec-9ca2-3526febb6520","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:0000002D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":6,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f779d738-891e-4687-991a-705d7112c5be","HttpRequestNumber":4,"HttpSessionId":"a800764d-c2f7-acea-24fe-b7208c7ccb64"}
{"@t":"2025-07-17T12:19:32.6612523Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"75d178abe71585193d34e1a9452db98b","@sp":"59492fff4b765fe9","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000032","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"683a327c-8091-4bfd-888e-f25176a65a5f","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.6644176Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"75d178abe71585193d34e1a9452db98b","@sp":"59492fff4b765fe9","Request":"{\r\n  \"grant_type\": \"authorization_code\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"code\": \"[redacted]\",\r\n  \"code_verifier\": \"VxPzFnCfjFHArm7PJqacfWDeDR1uL5zx48xA2KON4IfGbx0qHEiJuYdCKlmwga906kXbjxqGu3VO75GAuiBGDjcH4LcYlOW6tLqW2NqqVNgDKIrU0ZMLV6eGlPSYES5b\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000032","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"683a327c-8091-4bfd-888e-f25176a65a5f","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.8054161Z","@mt":"The token request was successfully validated.","@tr":"75d178abe71585193d34e1a9452db98b","@sp":"59492fff4b765fe9","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000032","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"683a327c-8091-4bfd-888e-f25176a65a5f","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.8284687Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"75d178abe71585193d34e1a9452db98b","@sp":"59492fff4b765fe9","Identifier":"6ecaeb75-11bc-4e6d-9a75-8c0dc7cfcf25","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"76a05be4-98f7-45d6-bf02-cc8aee90c334","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:00000032","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"683a327c-8091-4bfd-888e-f25176a65a5f","HttpRequestNumber":5,"HttpSessionId":"1338f5ee-d9b2-3961-97da-0a46086267e4"}
{"@t":"2025-07-17T12:19:32.8710968Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"75d178abe71585193d34e1a9452db98b","@sp":"59492fff4b765fe9","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 299,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"76a05be4-98f7-45d6-bf02-cc8aee90c334","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:00000032","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"683a327c-8091-4bfd-888e-f25176a65a5f","HttpRequestNumber":5,"HttpSessionId":"1338f5ee-d9b2-3961-97da-0a46086267e4"}
{"@t":"2025-07-17T12:19:32.8830830Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"c931fb31a0ad14fb682f657e16097924","@sp":"dc34d536b7d24202","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c9055f7b-c769-4326-bbd3-94f113f862d0","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.8844931Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"c931fb31a0ad14fb682f657e16097924","@sp":"dc34d536b7d24202","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"http://localhost:5000/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c9055f7b-c769-4326-bbd3-94f113f862d0","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.9043508Z","@mt":"The token request was successfully validated.","@tr":"c931fb31a0ad14fb682f657e16097924","@sp":"dc34d536b7d24202","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE55FMQ3O7A:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c9055f7b-c769-4326-bbd3-94f113f862d0","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-17T12:19:32.9112488Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"c931fb31a0ad14fb682f657e16097924","@sp":"dc34d536b7d24202","Identifier":"756113ce-a537-4c31-a6a1-67b327ef8161","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"76a05be4-98f7-45d6-bf02-cc8aee90c334","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c9055f7b-c769-4326-bbd3-94f113f862d0","HttpRequestNumber":6,"HttpSessionId":"91c7725e-4f07-3f32-e002-e20ff297d430"}
{"@t":"2025-07-17T12:19:32.9277997Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"c931fb31a0ad14fb682f657e16097924","@sp":"dc34d536b7d24202","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 299,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"76a05be4-98f7-45d6-bf02-cc8aee90c334","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7A:00000033","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE55FMQ3O7A","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":21,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c9055f7b-c769-4326-bbd3-94f113f862d0","HttpRequestNumber":6,"HttpSessionId":"91c7725e-4f07-3f32-e002-e20ff297d430"}
{"@t":"2025-07-17T12:20:21.2871651Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":19,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T12:21:08.0147248Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","@tr":"4ff7d06ea168b21f5ac00ada9124dd01","@sp":"30cc680b55f4b620","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ActionId":"34a49846-4257-4e29-b693-9e5e07308b77","ActionName":"Umbraco.Cms.Api.Management.Controllers.Indexer.AllIndexerController.All (Umbraco.Cms.Api.Management)","RequestId":"0HNE55FMQ3O7B:0000000D","RequestPath":"/umbraco/management/api/v1/indexer","ConnectionId":"0HNE55FMQ3O7B","ProcessId":3180,"ProcessName":"MDDPlus.Web","ThreadId":3,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"b09f1198-4f1c-4b1f-9b4c-f8269cb3daba","HttpRequestNumber":7,"HttpSessionId":"8a87b8b1-3a04-2b66-7126-5fa9f9bd9b04"}
