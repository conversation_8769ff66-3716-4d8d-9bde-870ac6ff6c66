@using MDDPlus.Models
@model HomePage
@{
    Layout = "_Layout";
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
}

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>@Model.HeroTitle</h1>
            <p>@Model.HeroSubtitle</p>
            @if (!string.IsNullOrEmpty(Model.HeroButtonText))
            {
                <a href="#services" class="btn btn-primary">@Model.HeroButtonText</a>
            }
        </div>
        @if (Model.HeroImage != null)
        {
            <div class="hero-image">
                <img src="@Model.HeroImage.Url()" alt="@Model.HeroImage.Value("altText")" />
            </div>
        }
    </div>
</section>

<!-- About Section -->
@if (!string.IsNullOrEmpty(Model.AboutText))
{
    <section class="about" id="about">
        <div class="container">
            <h2>@Model.AboutTitle</h2>
            <div class="about-content">
                @Html.Raw(Model.AboutText)
            </div>
        </div>
    </section>
}

<!-- Services Section -->
@if (Model.ServiceItems.Any())
{
    <section class="services" id="services">
        <div class="container">
            <h2>@Model.ServicesTitle</h2>
            <div class="services-grid">
                @foreach (var service in Model.ServiceItems)
                {
                    <div class="service-item">
                        <h3>@service.Name</h3>
                        <p>@service.Value("description")</p>
                        @if (service.HasValue("serviceIcon"))
                        {
                            <div class="service-icon">
                                <img src="@service.Value("serviceIcon")" alt="@service.Name" />
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </section>
}

<!-- Contact Section -->
<section class="contact" id="contact">
    <div class="container">
        <h2>@Model.ContactTitle</h2>
        <div class="contact-info">
            @if (!string.IsNullOrEmpty(Model.ContactEmail))
            {
                <div class="contact-item">
                    <strong>البريد الإلكتروني:</strong>
                    <a href="mailto:@Model.ContactEmail">@Model.ContactEmail</a>
                </div>
            }
            @if (!string.IsNullOrEmpty(Model.ContactPhone))
            {
                <div class="contact-item">
                    <strong>الهاتف:</strong>
                    <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                </div>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
}
