using Microsoft.AspNetCore.Mvc;

namespace MDDPlus.Controllers
{
    public class SetupController : Controller
    {
        [Route("setup")]
        public IActionResult Index()
        {
            return Content(@"
<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>🚀 MDD Plus Setup Guide</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .logo { font-size: 4rem; margin-bottom: 20px; }
        h1 { font-size: 3rem; margin-bottom: 20px; color: #ffd700; }
        .subtitle { font-size: 1.3rem; opacity: 0.9; margin-bottom: 30px; }
        .card { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(20px); 
            border-radius: 20px; 
            padding: 30px; 
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 30px 0;
        }
        .status-item { 
            background: rgba(255,255,255,0.05); 
            padding: 20px; 
            border-radius: 15px; 
            border-left: 4px solid #28a745;
        }
        .status-item.warning { border-left-color: #ffc107; }
        .status-item.error { border-left-color: #dc3545; }
        .status-icon { font-size: 2rem; margin-bottom: 10px; }
        .status-title { font-size: 1.2rem; font-weight: bold; color: #ffd700; margin-bottom: 10px; }
        .status-desc { opacity: 0.9; line-height: 1.6; }
        .links-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin: 30px 0;
        }
        .link-card { 
            background: rgba(255,255,255,0.1); 
            border: 2px solid rgba(255,255,255,0.3); 
            border-radius: 15px; 
            padding: 25px; 
            text-decoration: none; 
            color: white; 
            transition: all 0.3s ease;
            display: block;
            text-align: center;
        }
        .link-card:hover { 
            background: rgba(255,255,255,0.2); 
            border-color: #ffd700; 
            transform: translateY(-5px);
            color: white;
            text-decoration: none;
        }
        .link-icon { font-size: 2.5rem; margin-bottom: 15px; }
        .link-title { font-size: 1.2rem; font-weight: bold; margin-bottom: 10px; }
        .link-desc { font-size: 0.9rem; opacity: 0.8; }
        .step { 
            display: flex; 
            align-items: flex-start; 
            margin: 20px 0; 
            padding: 20px; 
            background: rgba(255,255,255,0.05); 
            border-radius: 15px;
        }
        .step-number { 
            background: #ffd700; 
            color: #333; 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: bold; 
            margin-right: 20px; 
            flex-shrink: 0;
        }
        .step-content h3 { color: #ffd700; margin-bottom: 10px; }
        .step-content p { opacity: 0.9; line-height: 1.6; }
        .btn { 
            background: linear-gradient(45deg, #28a745, #20c997); 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 25px; 
            font-size: 1.1rem; 
            font-weight: bold; 
            text-decoration: none; 
            display: inline-block; 
            transition: all 0.3s ease;
        }
        .btn:hover { 
            transform: translateY(-3px); 
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
            color: white;
            text-decoration: none;
        }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            h1 { font-size: 2rem; }
            .status-grid, .links-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>🚀</div>
            <h1>MDD Plus Setup</h1>
            <p class='subtitle'>Professional Saudi Fintech Website - Ready to Configure</p>
        </div>

        <div class='card'>
            <h2 style='color: #ffd700; margin-bottom: 20px;'>✅ Current Status</h2>
            <div class='status-grid'>
                <div class='status-item'>
                    <div class='status-icon'>✅</div>
                    <div class='status-title'>Application Running</div>
                    <div class='status-desc'>Umbraco 14 + .NET 9 running successfully on localhost:26967</div>
                </div>
                <div class='status-item'>
                    <div class='status-icon'>🎨</div>
                    <div class='status-title'>Templates Ready</div>
                    <div class='status-desc'>All Razor templates, CSS, and JavaScript files are created and working</div>
                </div>
                <div class='status-item warning'>
                    <div class='status-icon'>⚠️</div>
                    <div class='status-title'>Content Needed</div>
                    <div class='status-desc'>Document Types and content need to be created in Umbraco</div>
                </div>
                <div class='status-item'>
                    <div class='status-icon'>📱</div>
                    <div class='status-title'>Demo Available</div>
                    <div class='status-desc'>Working demo with sample content is ready to view</div>
                </div>
            </div>
        </div>

        <div class='card'>
            <h2 style='color: #ffd700; margin-bottom: 20px;'>🔗 Quick Access</h2>
            <div class='links-grid'>
                <a href='/demo' class='link-card' target='_blank'>
                    <div class='link-icon'>🌐</div>
                    <div class='link-title'>Demo Website</div>
                    <div class='link-desc'>See working demo with sample content</div>
                </a>
                <a href='/umbraco' class='link-card' target='_blank'>
                    <div class='link-icon'>🔧</div>
                    <div class='link-title'>Umbraco Admin</div>
                    <div class='link-desc'>Content management system</div>
                </a>
                <a href='/test' class='link-card' target='_blank'>
                    <div class='link-icon'>🧪</div>
                    <div class='link-title'>System Test</div>
                    <div class='link-desc'>Check system status and health</div>
                </a>
                <a href='/' class='link-card' target='_blank'>
                    <div class='link-icon'>🏠</div>
                    <div class='link-title'>Main Website</div>
                    <div class='link-desc'>View main website (needs content)</div>
                </a>
            </div>
        </div>

        <div class='card'>
            <h2 style='color: #ffd700; margin-bottom: 20px;'>📋 Setup Steps</h2>
            
            <div class='step'>
                <div class='step-number'>1</div>
                <div class='step-content'>
                    <h3>Access Umbraco Admin</h3>
                    <p>Go to <strong>/umbraco</strong> and log in to the admin panel. Create your admin account if this is the first time.</p>
                </div>
            </div>

            <div class='step'>
                <div class='step-number'>2</div>
                <div class='step-content'>
                    <h3>Create Document Types</h3>
                    <p>In Settings > Document Types, create a new Document Type called ""Home Page"" with alias ""homePage"". Add properties for hero section, about, services, statistics, and contact info.</p>
                </div>
            </div>

            <div class='step'>
                <div class='step-number'>3</div>
                <div class='step-content'>
                    <h3>Add Content</h3>
                    <p>In Content section, create a new Home Page and add your Arabic and English content. Use the sample content from the demo as a reference.</p>
                </div>
            </div>

            <div class='step'>
                <div class='step-number'>4</div>
                <div class='step-content'>
                    <h3>Publish & Test</h3>
                    <p>Save and publish your content, then test the website to ensure everything is working correctly.</p>
                </div>
            </div>
        </div>

        <div class='card' style='text-align: center;'>
            <h2 style='color: #ffd700; margin-bottom: 20px;'>🎯 What's Working Right Now</h2>
            <p style='font-size: 1.1rem; margin-bottom: 30px;'>The application is fully functional with:</p>
            <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>
                <div style='background: rgba(255,255,255,0.05); padding: 20px; border-radius: 10px;'>
                    <div style='font-size: 2rem; margin-bottom: 10px;'>🏗️</div>
                    <strong>Framework</strong><br>
                    Umbraco 14 + .NET 9
                </div>
                <div style='background: rgba(255,255,255,0.05); padding: 20px; border-radius: 10px;'>
                    <div style='font-size: 2rem; margin-bottom: 10px;'>🎨</div>
                    <strong>Design</strong><br>
                    Professional fintech styling
                </div>
                <div style='background: rgba(255,255,255,0.05); padding: 20px; border-radius: 10px;'>
                    <div style='font-size: 2rem; margin-bottom: 10px;'>🌐</div>
                    <strong>Languages</strong><br>
                    Arabic RTL + English LTR
                </div>
                <div style='background: rgba(255,255,255,0.05); padding: 20px; border-radius: 10px;'>
                    <div style='font-size: 2rem; margin-bottom: 10px;'>📱</div>
                    <strong>Responsive</strong><br>
                    Mobile-first design
                </div>
            </div>
            <a href='/demo' class='btn'>View Working Demo</a>
        </div>
    </div>

    <script>
        // Add animation effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .status-item, .link-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>", "text/html");
        }
    }
}
