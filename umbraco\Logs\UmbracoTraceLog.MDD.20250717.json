{"@t":"2025-07-17T10:51:12.9480732Z","@mt":"Getting {TypeName}: scanning assemblies.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0841921Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0842853Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.PropertyEditors.IPropertyValueConverter","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0845158Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.PropertyEditors.IPropertyValueConverter","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0895252Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0895482Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Packaging.PackageMigrationPlan","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0895807Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Packaging.PackageMigrationPlan","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0993670Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0993889Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Web.Common.Controllers.UmbracoApiController","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.0995995Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Web.Common.Controllers.UmbracoApiController","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1803722Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1803960Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Web.Website.Controllers.SurfaceController","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1804470Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Web.Website.Controllers.SurfaceController","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1813542Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1813711Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IComposer","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1814078Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IComposer","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.1883716Z","@mt":"Ordered Composers: {SortedComposerTypes}","@l":"Debug","SortedComposerTypes":["Umbraco.Cms.Persistence.SqlServer.SqlServerComposer","Umbraco.Cms.Persistence.Sqlite.SqliteComposer","Umbraco.Cms.Persistence.EFCore.SqlServer.EFCoreSqlServerComposer","Umbraco.Cms.Persistence.EFCore.Sqlite.EFCoreSqliteComposer","Umbraco.Cms.Persistence.EFCore.Composition.UmbracoEFCoreComposer","Umbraco.Cms.Imaging.ImageSharp.ImageSharpComposer","Umbraco.Cms.Api.Management.ManagementApiComposer","Umbraco.Cms.Infrastructure.Migrations.Upgrade.V_15_0_0.LocalLinks.ConvertLocalLinkComposer"],"SourceContext":"Umbraco.Cms.Core.Composing.ComposerGraph","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2158227Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2158480Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Cache.ICacheRefresher","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2158861Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Cache.ICacheRefresher","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2160060Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2160140Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.PropertyEditors.IDataEditor","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2160423Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.PropertyEditors.IDataEditor","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2161063Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2161126Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Actions.IAction","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2161336Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Actions.IAction","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2161960Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2162073Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Editors.IEditorValidator","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2162284Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.Editors.IEditorValidator","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2162856Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2162888Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.HealthChecks.HealthCheck","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2163079Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.HealthChecks.HealthCheck","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2163605Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2163674Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.HealthChecks.NotificationMethods.IHealthCheckNotificationMethod","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2163839Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.HealthChecks.NotificationMethods.IHealthCheckNotificationMethod","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2164655Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2164749Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.ISelectorHandler","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2164935Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.ISelectorHandler","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165242Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165272Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.IFilterHandler","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165482Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.IFilterHandler","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165803Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165839Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.ISortHandler","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2165970Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.ISortHandler","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2166212Z","@mt":"Getting {TypeName}: found a cached type list.","@l":"Debug","TypeName":"Umbraco.Cms.Core.Composing.IDiscoverable","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2166241Z","@mt":"Getting {TypeName}: filtering IDiscoverable.","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.IContentIndexHandler","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2166370Z","@mt":"Got {TypeName}, caching ({CacheType}).","@l":"Debug","TypeName":"Umbraco.Cms.Core.DeliveryApi.IContentIndexHandler","CacheType":"true","SourceContext":"Umbraco.Cms.Core.Composing.TypeLoader","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"a2d89d13931e0984baa1825ef55853e8c709e786","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2570846Z","@mt":"Missing connection string, defer configuration.","@l":"Debug","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.UmbracoDatabaseFactory","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2635660Z","@mt":"{StartMessage} [Timing {TimingId}]","@l":"Debug","StartMessage":"Acquiring MainDom.","TimingId":"9a940fb","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2641961Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.2650375Z","@mt":"Attempting to obtain MainDom lock file handle {lockFilePath}","@l":"Debug","lockFilePath":"D:\\project\\mdd_plus\\umbraco\\Data\\TEMP\\MainDom_c2f95252ed1f327b5395f89201a33bd279c4644c.lock","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.FileSystemMainDomLock","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2656697Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.2659971Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@l":"Debug","EndMessage":"Acquired.","Duration":2,"TimingId":"9a940fb","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2721081Z","@mt":"{StartMessage} [Timing {TimingId}]","@l":"Debug","StartMessage":"Determining runtime level.","TimingId":"e390f4b","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2724414Z","@mt":"Database is not configured, need to install Umbraco.","@l":"Debug","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.RuntimeState","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2725437Z","@mt":"Runtime level: {RuntimeLevel} - {RuntimeLevelReason}","@l":"Debug","RuntimeLevel":"Install","RuntimeLevelReason":"InstallNoDatabase","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2725586Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@l":"Debug","EndMessage":"Determined.","Duration":0,"TimingId":"e390f4b","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2931989Z","@mt":"{StartMessage} [Timing {TimingId}]","@l":"Debug","StartMessage":"Initializing. (log components when >100ms)","TimingId":"f32e488","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2933417Z","@mt":"{StartMessage} [Timing {TimingId}]","@l":"Debug","StartMessage":"Creating components. (log when >100ms)","TimingId":"b7edb70","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2933943Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@l":"Debug","EndMessage":"Created.","Duration":0,"TimingId":"b7edb70","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2934132Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@l":"Debug","EndMessage":"Initialized.","Duration":0,"TimingId":"f32e488","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.2957115Z","@mt":"Profiler is VoidProfiler, not profiling (must run debug mode to profile).","SourceContext":"Umbraco.Cms.Web.Common.Profiler.InitializeWebProfiling","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.3537166Z","@mt":"User profile is available. Using '{FullName}' as key repository and Windows DPAPI to encrypt keys at rest.","FullName":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys","EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.3639968Z","@mt":"Registered model binder providers, in the following order: {ModelBinderProviders}","@l":"Debug","ModelBinderProviders":["Asp.Versioning.ApiVersionModelBinderProvider","Umbraco.Cms.Web.Common.ModelBinders.ContentModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"],"EventId":{"Id":12,"Name":"RegisteredModelBinderProviders"},"SourceContext":"Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderFactory","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6459206Z","@mt":"Registered SignalR Protocol: {ProtocolName}, implemented by {ImplementationType}.","@l":"Debug","ProtocolName":"json","ImplementationType":"Microsoft.AspNetCore.SignalR.Protocol.JsonHubProtocol","EventId":{"Id":1,"Name":"RegisteredSignalRProtocol"},"SourceContext":"Microsoft.AspNetCore.SignalR.Internal.DefaultHubProtocolResolver","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6513048Z","@mt":"'{HubName}' hub method '{HubMethod}' is bound.","@l":"Verbose","HubName":"BackofficeHub","HubMethod":"SendPayload","EventId":{"Id":9,"Name":"HubMethodBound"},"SourceContext":"Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"ALL  "}
{"@t":"2025-07-17T10:51:13.6553530Z","@mt":"Hosting starting","@l":"Debug","EventId":{"Id":1,"Name":"Starting"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6872583Z","@mt":"Reading data from file '{FullPath}'.","@l":"Debug","FullPath":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys\\key-1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6.xml","EventId":{"Id":37,"Name":"ReadingDataFromFile"},"SourceContext":"Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6892518Z","@mt":"Reading data from file '{FullPath}'.","@l":"Debug","FullPath":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys\\key-5fdd4ba5-db37-4bed-a657-a5d6e2a83965.xml","EventId":{"Id":37,"Name":"ReadingDataFromFile"},"SourceContext":"Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6896568Z","@mt":"Reading data from file '{FullPath}'.","@l":"Debug","FullPath":"C:\\Users\\<USER>\\AppData\\Local\\ASP.NET\\DataProtection-Keys\\key-a40cb926-9485-4212-9fee-ca2da0f70690.xml","EventId":{"Id":37,"Name":"ReadingDataFromFile"},"SourceContext":"Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6910417Z","@mt":"Found key {KeyId:B}.","@r":["{1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6}"],"@l":"Debug","KeyId":"1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6","EventId":{"Id":18,"Name":"FoundKey"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6926190Z","@mt":"Found key {KeyId:B}.","@r":["{5fdd4ba5-db37-4bed-a657-a5d6e2a83965}"],"@l":"Debug","KeyId":"5fdd4ba5-db37-4bed-a657-a5d6e2a83965","EventId":{"Id":18,"Name":"FoundKey"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6926985Z","@mt":"Found key {KeyId:B}.","@r":["{a40cb926-9485-4212-9fee-ca2da0f70690}"],"@l":"Debug","KeyId":"a40cb926-9485-4212-9fee-ca2da0f70690","EventId":{"Id":18,"Name":"FoundKey"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6948722Z","@mt":"Considering key {KeyId:B} with expiration date {ExpirationDate:u} as default key.","@r":["{1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6}","2025-08-13 05:44:52Z"],"@l":"Debug","KeyId":"1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6","ExpirationDate":"2025-08-13T05:44:52.5861683+00:00","EventId":{"Id":13,"Name":"ConsideringKeyWithExpirationDateAsDefaultKey"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.DefaultKeyResolver","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6953633Z","@mt":"Forwarded activator type request from {FromType} to {ToType}","@l":"Debug","FromType":"Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","ToType":"Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60","SourceContext":"Microsoft.AspNetCore.DataProtection.TypeForwardingActivator","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6959431Z","@mt":"Decrypting secret element using Windows DPAPI.","@l":"Debug","EventId":{"Id":51,"Name":"DecryptingSecretElementUsingWindowsDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6965354Z","@mt":"Forwarded activator type request from {FromType} to {ToType}","@l":"Debug","FromType":"Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","ToType":"Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60","SourceContext":"Microsoft.AspNetCore.DataProtection.TypeForwardingActivator","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6968459Z","@mt":"Opening CNG algorithm '{EncryptionAlgorithm}' from provider '{EncryptionAlgorithmProvider}' with chaining mode CBC.","@l":"Debug","EncryptionAlgorithm":"AES","EncryptionAlgorithmProvider":null,"EventId":{"Id":4,"Name":"OpeningCNGAlgorithmFromProviderWithChainingModeCBC"},"SourceContext":"Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6971439Z","@mt":"Opening CNG algorithm '{HashAlgorithm}' from provider '{HashAlgorithmProvider}' with HMAC.","@l":"Debug","HashAlgorithm":"SHA256","HashAlgorithmProvider":null,"EventId":{"Id":3,"Name":"OpeningCNGAlgorithmFromProviderWithHMAC"},"SourceContext":"Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6974281Z","@mt":"Using key {KeyId:B} as the default key.","@r":["{1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6}"],"@l":"Debug","KeyId":"1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6","EventId":{"Id":2,"Name":"UsingKeyAsDefaultKey"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingProvider","ProcessId":39928,"ProcessName":"dotnet","ThreadId":7,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6977179Z","@mt":"Key ring with default key {KeyId:B} was loaded during application startup.","@r":["{1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6}"],"@l":"Debug","KeyId":"1cf21c36-9d5a-46b6-aeae-a84ac42d5ac6","EventId":{"Id":65,"Name":"KeyRingWasLoadedOnStartup"},"SourceContext":"Microsoft.AspNetCore.DataProtection.Internal.DataProtectionHostedService","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6981475Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.6981679Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.6984112Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7002350Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7003734Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7004609Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7005690Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7006052Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7006648Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7006943Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7007712Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7008041Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7008570Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7008867Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7009366Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7009629Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7010137Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7010520Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7011685Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7012129Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7013008Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7013360Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7013946Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7014227Z","@mt":"Creating background hosted service for {job}","@l":"Debug","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7014829Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7015218Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7281225Z","@mt":"Middleware configuration started with options: {Options}","@l":"Debug","Options":"{AllowedHosts = *, AllowEmptyHosts = True, IncludeFailureMessage = True}","EventId":{"Id":7,"Name":"MiddlewareConfigurationStarted"},"SourceContext":"Microsoft.AspNetCore.HostFiltering.HostFilteringMiddleware","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7286659Z","@mt":"Wildcard detected, all requests with hosts will be allowed.","@l":"Debug","EventId":{"Name":"WildcardDetected"},"SourceContext":"Microsoft.AspNetCore.HostFiltering.HostFilteringMiddleware","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7334281Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7334605Z","@mt":"Loaded hosting startup assembly {assemblyName}","@l":"Debug","assemblyName":"MDDPlus.Web","EventId":{"Id":13,"Name":"HostingStartupAssemblyLoaded"},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
{"@t":"2025-07-17T10:51:13.7334983Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7335063Z","@mt":"Hosting environment: {EnvName}","EnvName":"Production","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7335108Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:51:13.7340142Z","@mt":"Starting connection heartbeat.","@l":"Verbose","EventId":{"Id":9,"Name":"HeartBeatStarted"},"SourceContext":"Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"ALL  "}
{"@t":"2025-07-17T10:51:13.7362262Z","@mt":"Hosting started","@l":"Debug","EventId":{"Id":2,"Name":"Started"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","ProcessId":39928,"ProcessName":"dotnet","ThreadId":1,"ApplicationId":"f9214261ce9541239b004475680b80862329039e","MachineName":"MDD","Log4NetLevel":"DEBUG"}
