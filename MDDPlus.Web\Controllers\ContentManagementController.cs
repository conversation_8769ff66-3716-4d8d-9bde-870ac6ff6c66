using Microsoft.AspNetCore.Mvc;
using Umbraco.Cms.Core.Services;
using Umbraco.Cms.Core.Models;
using Umbraco.Cms.Web.Common.Controllers;
using Microsoft.AspNetCore.Authorization;
using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Extensions;

namespace MDDPlus.Web.Controllers
{
    [Route("admin/content")]
    public class ContentManagementController : Controller
    {
        private readonly IContentService _contentService;
        private readonly IContentTypeService _contentTypeService;
        private readonly ILogger<ContentManagementController> _logger;

        public ContentManagementController(
            IContentService contentService,
            IContentTypeService contentTypeService,
            ILogger<ContentManagementController> logger)
        {
            _contentService = contentService;
            _contentTypeService = contentTypeService;
            _logger = logger;
        }

        [HttpGet("")]
        public IActionResult Index()
        {
            try
            {
                var homePages = _contentService.GetRootContent().Where(x => x.ContentType.Alias == "homePage");
                return View("~/Views/Admin/ContentManagement.cshtml", homePages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading content management");
                return View("~/Views/Admin/ContentManagement.cshtml", new List<IContent>());
            }
        }

        [HttpGet("edit/{id:int}")]
        public IActionResult Edit(int id)
        {
            try
            {
                var content = _contentService.GetById(id);
                if (content == null)
                {
                    return NotFound();
                }

                var model = new ContentEditModel
                {
                    Id = content.Id,
                    Name = content.Name,
                    HeroTitle = content.GetValue<string>("heroTitle") ?? "",
                    HeroSubtitle = content.GetValue<string>("heroSubtitle") ?? "",
                    HeroButtonText = content.GetValue<string>("heroButtonText") ?? "",
                    TotalFunded = content.GetValue<string>("totalFunded") ?? "",
                    AnnualReturn = content.GetValue<string>("annualReturn") ?? "",
                    MetaTitle = content.GetValue<string>("metaTitle") ?? "",
                    MetaDescription = content.GetValue<string>("metaDescription") ?? ""
                };

                return View("~/Views/Admin/EditContent.cshtml", model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading content for editing");
                return RedirectToAction("Index");
            }
        }

        [HttpPost("edit/{id:int}")]
        public IActionResult Edit(int id, ContentEditModel model)
        {
            try
            {
                var content = _contentService.GetById(id);
                if (content == null)
                {
                    return NotFound();
                }

                // Update content properties
                content.SetValue("heroTitle", model.HeroTitle);
                content.SetValue("heroSubtitle", model.HeroSubtitle);
                content.SetValue("heroButtonText", model.HeroButtonText);
                content.SetValue("totalFunded", model.TotalFunded);
                content.SetValue("annualReturn", model.AnnualReturn);
                content.SetValue("metaTitle", model.MetaTitle);
                content.SetValue("metaDescription", model.MetaDescription);

                // Save and publish
                var saveResult = _contentService.Save(content);
                var publishResult = _contentService.Publish(content, new string[] { });
                var result = saveResult.Success && publishResult.Success;

                if (result)
                {
                    TempData["SuccessMessage"] = "تم حفظ المحتوى بنجاح!";
                }
                else
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء حفظ المحتوى.";
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving content");
                TempData["ErrorMessage"] = "حدث خطأ أثناء حفظ المحتوى.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost("create")]
        public IActionResult CreateHomePage()
        {
            try
            {
                var contentType = _contentTypeService.Get("homePage");
                if (contentType == null)
                {
                    TempData["ErrorMessage"] = "نوع المحتوى غير موجود. يرجى إنشاؤه أولاً.";
                    return RedirectToAction("Index");
                }

                var content = _contentService.Create("الصفحة الرئيسية", -1, "homePage");

                // Set default values
                content.SetValue("heroTitle", "منصة التمويل الجماعي بالدين الرائدة في السعودية");
                content.SetValue("heroSubtitle", "حلول تمويلية متوافقة مع الشريعة الإسلامية");
                content.SetValue("heroButtonText", "ابدأ الاستثمار الآن");
                content.SetValue("totalFunded", "500000000");
                content.SetValue("annualReturn", "18");
                content.SetValue("metaTitle", "مدد بلس - منصة التمويل الجماعي");
                content.SetValue("metaDescription", "منصة مدد بلس للتمويل الجماعي بالدين");

                var saveResult = _contentService.Save(content);
                var publishResult = _contentService.Publish(content, new string[] { });
                var result = saveResult.Success && publishResult.Success;

                if (result)
                {
                    TempData["SuccessMessage"] = "تم إنشاء الصفحة الرئيسية بنجاح!";
                }
                else
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء الصفحة.";
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating home page");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء الصفحة.";
                return RedirectToAction("Index");
            }
        }
    }

    public class ContentEditModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string HeroTitle { get; set; } = "";
        public string HeroSubtitle { get; set; } = "";
        public string HeroButtonText { get; set; } = "";
        public string TotalFunded { get; set; } = "";
        public string AnnualReturn { get; set; } = "";
        public string MetaTitle { get; set; } = "";
        public string MetaDescription { get; set; } = "";
    }
}
