<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f" Alias="contentPage" Level="1">
  <Info>
    <Name>Content Page</Name>
    <Icon>icon-document</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Generic content page for MDD Plus website</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <ListView>00000000-0000-0000-0000-000000000000</ListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Compositions />
    <DefaultTemplate>ContentPage</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="c6d7e8f9-0a1b-2c3d-4e5f-6a7b8c9d0e1f">ContentPage</Template>
    </AllowedTemplates>
  </Info>
  <Structure>
    <DocumentType Key="c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f">contentPage</DocumentType>
  </Structure>
  <GenericProperties>
    <!-- Page Content -->
    <GenericProperty>
      <Key>1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Name>Page Title</Name>
      <Alias>pageTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main page title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Name>Page Subtitle</Name>
      <Alias>pageSubtitle</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Page subtitle or summary]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Name>Page Content</Name>
      <Alias>pageContent</Alias>
      <Definition>ca90c950-0aff-4e72-b976-a30b1ac57dad</Definition>
      <Type>Umbraco.TinyMCE</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main page content]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Name>Page Image</Name>
      <Alias>pageImage</Alias>
      <Definition>ad9f0cf2-bda2-45d5-9ea1-a63cfc873fd3</Definition>
      <Type>Umbraco.MediaPicker3</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Featured page image]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Navigation -->
    <GenericProperty>
      <Key>5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Name>Hide From Navigation</Name>
      <Alias>hideFromNavigation</Alias>
      <Definition>92897bc6-a5f3-4ffe-ae27-f2e7e33dda49</Definition>
      <Type>Umbraco.TrueFalse</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hide this page from navigation menus]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="navigation">Navigation</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c</Key>
      <Name>Navigation Title</Name>
      <Alias>navigationTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Custom title for navigation (if different from page title)]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="navigation">Navigation</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- SEO Properties -->
    <GenericProperty>
      <Key>7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>8b9c0d1e-2f3a-4b5c-6d7e-8f9a0b1c2d3e</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f</Key>
      <Name>Meta Keywords</Name>
      <Alias>metaKeywords</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta keywords]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Caption>Content</Caption>
      <Alias>content</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Caption>Navigation</Caption>
      <Alias>navigation</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
