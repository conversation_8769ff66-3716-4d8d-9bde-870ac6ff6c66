/* MDD Plus - Manafa.sa Inspired Modern Design */

/* CSS Variables for Consistent Design */
:root {
    /* Colors inspired by Manafa.sa */
    --primary-color: #1a365d;
    --primary-light: #2d5a87;
    --primary-dark: #0f2a44;
    --secondary-color: #3182ce;
    --accent-color: #f6ad55;
    --accent-gold: #d69e2e;
    --success-color: #38a169;
    --warning-color: #ed8936;
    --error-color: #e53e3e;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f7fafc;
    --gray-100: #edf2f7;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e0;
    --gray-400: #a0aec0;
    --gray-500: #718096;
    --gray-600: #4a5568;
    --gray-700: #2d3748;
    --gray-800: #1a202c;
    --gray-900: #171923;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    --font-arabic: '<PERSON><PERSON><PERSON>', '<PERSON>go<PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Arabic Typography */
[lang="ar"], .arabic {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(60px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-60px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(60px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes drawLine {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Modern Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    padding: var(--space-md) 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    padding: var(--space-sm) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.navbar .logo {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.navbar .logo:hover {
    color: var(--secondary-color);
    transform: scale(1.05);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
    list-style: none;
}

.nav-links a {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all var(--transition-fast);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
}

.nav-links a:hover {
    color: var(--primary-color);
    background: var(--gray-50);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--secondary-color);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-links a:hover::after {
    width: 80%;
}

/* Hero Section - Manafa Style */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--secondary-color) 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
    animation: float 20s ease-in-out infinite;
}

.hero .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.hero-visual {
    animation: fadeInRight 1s ease-out 0.3s both;
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    color: var(--white);
    margin-bottom: var(--space-lg);
    line-height: 1.2;
    background: linear-gradient(135deg, var(--white), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-xl);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-gold));
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

/* Hero Visual Elements */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    position: absolute;
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    bottom: 20%;
    left: 10%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    top: 50%;
    right: 30%;
    animation-delay: 4s;
}

/* Statistics Section */
.statistics {
    padding: var(--space-3xl) 0;
    background: var(--gray-50);
    position: relative;
}

.statistics::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(180deg, var(--secondary-color), transparent);
    opacity: 0.1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform var(--transition-slow);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
    display: block;
    animation: countUp 1s ease-out;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    color: var(--white);
    font-size: 1.5rem;
}

/* Features Section */
.features {
    padding: var(--space-3xl) 0;
    background: var(--white);
}

.features .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-gold));
    transform: translateX(-50%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
}

.feature-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.feature-card:hover::before {
    opacity: 0.05;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    color: var(--white);
    font-size: 2rem;
    position: relative;
    z-index: 1;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--space-md);
    position: relative;
    z-index: 1;
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.7;
    position: relative;
    z-index: 1;
}

/* Services Section */
.services {
    padding: var(--space-3xl) 0;
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-2xl);
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.service-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.service-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    color: var(--white);
    font-size: 2.5rem;
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--space-md);
}

.service-description {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--space-lg);
}

.service-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all var(--transition-fast);
}

.service-link:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

/* Contact Section */
.contact {
    padding: var(--space-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
}

.contact .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    position: relative;
    z-index: 2;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
}

.contact-info h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--space-lg);
}

.contact-info p {
    font-size: 1.25rem;
    margin-bottom: var(--space-xl);
    opacity: 0.9;
    line-height: 1.7;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-lg);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(10px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
}

.contact-text {
    flex: 1;
}

.contact-label {
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.contact-value {
    opacity: 0.9;
}

.contact-value a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-value a:hover {
    color: var(--white);
}

/* Modern Contact Form */
.contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
}

.modern-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--space-lg) var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--white);
    font-size: 1rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group label {
    position: absolute;
    top: var(--space-lg);
    left: var(--space-md);
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    transition: all var(--transition-normal);
    pointer-events: none;
    background: transparent;
    padding: 0 var(--space-xs);
}

.form-group input:focus + label,
.form-group input:valid + label,
.form-group textarea:focus + label,
.form-group textarea:valid + label {
    top: -10px;
    left: var(--space-sm);
    font-size: 0.875rem;
    color: var(--accent-color);
    background: var(--primary-color);
    padding: 0 var(--space-xs);
    border-radius: var(--radius-sm);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.modern-form button {
    margin-top: var(--space-md);
    align-self: flex-start;
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-3xl) 0 var(--space-xl);
}

.footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
}

.footer-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    color: var(--accent-color);
}

.footer-section p,
.footer-section a {
    color: var(--gray-300);
    text-decoration: none;
    line-height: 1.7;
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: var(--space-lg);
    text-align: center;
    color: var(--gray-400);
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
}

.animate-fade-in {
    animation: fadeInUp 0.8s ease-out;
}

.animate-slide-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-slide-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-scale {
    animation: scaleIn 0.8s ease-out;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-visual {
        order: -1;
        margin-bottom: var(--space-xl);
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .nav-links {
        display: none;
    }

    .hero-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-lg);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a202c;
        --gray-50: #2d3748;
        --gray-100: #4a5568;
        --gray-800: #f7fafc;
        --gray-900: #ffffff;
    }
}

/* High Performance Optimizations */
.stat-card,
.feature-card,
.service-card {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Print Styles */
@media print {
    .navbar,
    .hero-buttons,
    .contact {
        display: none;
    }

    .hero {
        background: var(--white);
        color: var(--gray-900);
        min-height: auto;
        padding: var(--space-lg) 0;
    }

    .stat-card,
    .feature-card,
    .service-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.btn:focus,
.nav-links a:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Selection Styles */
::selection {
    background: var(--accent-color);
    color: var(--white);
}

::-moz-selection {
    background: var(--accent-color);
    color: var(--white);
}
