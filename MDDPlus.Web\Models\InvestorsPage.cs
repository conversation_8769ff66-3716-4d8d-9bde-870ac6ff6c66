using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// Investors Page Document Type for MDD Plus
    /// </summary>
    public class InvestorsPage : PublishedContentModel
    {
        public InvestorsPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Page Header
        public string PageTitle => this.Value<string>("pageTitle") ?? "للمستثمرين";
        public string PageSubtitle => this.Value<string>("pageSubtitle") ?? "استثمر بذكاء واحصل على عوائد تنافسية";
        public IPublishedContent? HeaderImage => this.Value<IPublishedContent>("headerImage");

        // Investment Overview
        public string InvestmentOverviewTitle => this.Value<string>("investmentOverviewTitle") ?? "نظرة عامة على الاستثمار";
        public string InvestmentOverviewContent => this.Value<string>("investmentOverviewContent") ??
            "مدد بلس تقدم فرص استثمارية متنوعة في التمويل الجماعي بالدين، مع عوائد تنافسية تصل إلى 18% سنوياً ومخاطر محسوبة.";

        // Investment Types
        public string InvestmentTypesTitle => this.Value<string>("investmentTypesTitle") ?? "أنواع الاستثمار";

        // Manual Investment
        public string ManualInvestmentTitle => this.Value<string>("manualInvestmentTitle") ?? "الاستثمار اليدوي";
        public string ManualInvestmentDescription => this.Value<string>("manualInvestmentDescription") ??
            "اختر الفرص الاستثمارية بنفسك وتحكم في محفظتك الاستثمارية بالكامل";
        public string ManualInvestmentFeatures => this.Value<string>("manualInvestmentFeatures") ??
            "تحكم كامل في الاستثمارات، اختيار الفرص المناسبة، مرونة في المبالغ، تنويع المحفظة";

        // Auto Investment
        public string AutoInvestmentTitle => this.Value<string>("autoInvestmentTitle") ?? "الاستثمار الآلي";
        public string AutoInvestmentDescription => this.Value<string>("autoInvestmentDescription") ??
            "استثمار تلقائي ذكي يوزع أموالك على أفضل الفرص المتاحة وفقاً لمعايير المخاطر المحددة";
        public string AutoInvestmentFeatures => this.Value<string>("autoInvestmentFeatures") ??
            "توزيع تلقائي للمخاطر، إعادة استثمار العوائد، توفير الوقت والجهد، إدارة محترفة";

        // Returns & Performance
        public string ReturnsTitle => this.Value<string>("returnsTitle") ?? "العوائد والأداء";
        public string ReturnsDescription => this.Value<string>("returnsDescription") ??
            "تحقق عوائد مجزية مع مستويات مخاطر متدرجة تناسب جميع أنواع المستثمرين";

        // Historical Performance
        public string HistoricalPerformanceTitle => this.Value<string>("historicalPerformanceTitle") ?? "الأداء التاريخي";
        public string AverageReturn => this.Value<string>("averageReturn") ?? "15.2";
        public string BestReturn => this.Value<string>("bestReturn") ?? "18.5";
        public string DefaultRate => this.Value<string>("defaultRate") ?? "0.2";
        public string TotalInvestments => this.Value<string>("totalInvestments") ?? "500000000";

        // Risk Management
        public string RiskManagementTitle => this.Value<string>("riskManagementTitle") ?? "إدارة المخاطر";
        public string RiskManagementDescription => this.Value<string>("riskManagementDescription") ??
            "نطبق أعلى معايير إدارة المخاطر لحماية استثماراتك وضمان عوائد مستدامة";

        // Risk Categories
        public string LowRiskTitle => this.Value<string>("lowRiskTitle") ?? "مخاطر منخفضة";
        public string LowRiskDescription => this.Value<string>("lowRiskDescription") ??
            "استثمارات آمنة بعوائد مستقرة تتراوح بين 8-12% سنوياً";
        public string LowRiskReturn => this.Value<string>("lowRiskReturn") ?? "8-12";

        public string MediumRiskTitle => this.Value<string>("mediumRiskTitle") ?? "مخاطر متوسطة";
        public string MediumRiskDescription => this.Value<string>("mediumRiskDescription") ??
            "توازن مثالي بين المخاطر والعوائد بنسبة 12-15% سنوياً";
        public string MediumRiskReturn => this.Value<string>("mediumRiskReturn") ?? "12-15";

        public string HighRiskTitle => this.Value<string>("highRiskTitle") ?? "مخاطر عالية";
        public string HighRiskDescription => this.Value<string>("highRiskDescription") ??
            "فرص استثمارية عالية العائد تصل إلى 18% سنوياً للمستثمرين المتمرسين";
        public string HighRiskReturn => this.Value<string>("highRiskReturn") ?? "15-18";

        // Investment Process
        public string ProcessTitle => this.Value<string>("processTitle") ?? "كيفية الاستثمار";
        public string ProcessDescription => this.Value<string>("processDescription") ??
            "عملية استثمار بسيطة وسريعة في خطوات قليلة";

        // Minimum Investment
        public string MinimumInvestmentTitle => this.Value<string>("minimumInvestmentTitle") ?? "الحد الأدنى للاستثمار";
        public string MinimumInvestmentAmount => this.Value<string>("minimumInvestmentAmount") ?? "1000";
        public string MinimumInvestmentDescription => this.Value<string>("minimumInvestmentDescription") ??
            "ابدأ استثمارك بمبلغ بسيط يناسب جميع فئات المستثمرين";

        // Fees & Charges
        public string FeesTitle => this.Value<string>("feesTitle") ?? "الرسوم والعمولات";
        public string FeesDescription => this.Value<string>("feesDescription") ??
            "هيكل رسوم شفاف وتنافسي بدون رسوم خفية";
        public string ManagementFee => this.Value<string>("managementFee") ?? "1";
        public string PerformanceFee => this.Value<string>("performanceFee") ?? "10";

        // Sharia Compliance
        public string ShariaComplianceTitle => this.Value<string>("shariaComplianceTitle") ?? "الامتثال الشرعي";
        public string ShariaComplianceDescription => this.Value<string>("shariaComplianceDescription") ??
            "جميع الاستثمارات معتمدة من لجنة شرعية مستقلة ومتوافقة مع أحكام الشريعة الإسلامية";

        // Investor Protection
        public string ProtectionTitle => this.Value<string>("protectionTitle") ?? "حماية المستثمرين";
        public string ProtectionDescription => this.Value<string>("protectionDescription") ??
            "نطبق أعلى معايير حماية المستثمرين وفقاً للوائح البنك المركزي السعودي";

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? "للمستثمرين - مدد بلس";
        public string MetaDescription => this.Value<string>("metaDescription") ??
            "استثمر مع مدد بلس واحصل على عوائد تصل إلى 18% سنوياً. استثمار آمن ومتوافق مع الشريعة الإسلامية مع إدارة مخاطر متقدمة.";
    }
}
