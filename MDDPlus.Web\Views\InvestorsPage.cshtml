@using MDDPlus.Web.Models
@model InvestorsPage
@{
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
    ViewBag.ActivePage = "investors";
    Layout = "_Layout";
}

<!-- Page Header -->
<section class="hero" style="padding: var(--space-16) 0; background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));">
    <div class="container">
        <div class="text-center">
            <h1 class="animate-fade-in-up">@Model.PageTitle</h1>
            <p class="animate-fade-in-up" style="animation-delay: 0.2s; font-size: var(--text-xl); margin-bottom: 0;">
                @Model.PageSubtitle
            </p>
        </div>
    </div>
</section>

<!-- Investment Overview -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="grid grid-cols-2" style="gap: var(--space-12); align-items: center;">
            <div>
                <h2 class="mb-6">@Model.InvestmentOverviewTitle</h2>
                <p class="text-lg text-secondary leading-relaxed mb-8">
                    @Model.InvestmentOverviewContent
                </p>
                
                <!-- Key Stats -->
                <div class="grid grid-cols-2" style="gap: var(--space-6);">
                    <div class="text-center p-4 bg-secondary rounded-lg">
                        <div class="text-2xl font-bold text-primary mb-2">@Model.AverageReturn%</div>
                        <div class="text-sm text-secondary">متوسط العائد السنوي</div>
                    </div>
                    <div class="text-center p-4 bg-secondary rounded-lg">
                        <div class="text-2xl font-bold text-primary mb-2">@Model.DefaultRate%</div>
                        <div class="text-sm text-secondary">معدل التعثر</div>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <div class="card p-8">
                    <div class="feature-icon mx-auto mb-6" style="width: 80px; height: 80px;">
                        <svg width="40" height="40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h4 class="mb-4">ابدأ بـ @Model.MinimumInvestmentAmount ريال فقط</h4>
                    <p class="text-secondary mb-6">
                        @Model.MinimumInvestmentDescription
                    </p>
                    <a href="/register" class="btn btn-primary w-full">ابدأ الاستثمار الآن</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Investment Types -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.InvestmentTypesTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                اختر نوع الاستثمار الذي يناسب أسلوبك وأهدافك المالية
            </p>
        </div>
        
        <div class="grid grid-cols-2" style="gap: var(--space-8);">
            <!-- Manual Investment -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                        </svg>
                    </div>
                    <h3>@Model.ManualInvestmentTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary mb-6">@Model.ManualInvestmentDescription</p>
                    <div class="space-y-3">
                        @foreach (var feature in Model.ManualInvestmentFeatures.Split(','))
                        {
                            <div class="flex items-center" style="gap: var(--space-3);">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm">@feature.Trim()</span>
                            </div>
                        }
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/manual-investment" class="btn btn-outline w-full">اعرف المزيد</a>
                </div>
            </div>
            
            <!-- Auto Investment -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3>@Model.AutoInvestmentTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary mb-6">@Model.AutoInvestmentDescription</p>
                    <div class="space-y-3">
                        @foreach (var feature in Model.AutoInvestmentFeatures.Split(','))
                        {
                            <div class="flex items-center" style="gap: var(--space-3);">
                                <div class="w-2 h-2 bg-secondary rounded-full"></div>
                                <span class="text-sm">@feature.Trim()</span>
                            </div>
                        }
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/auto-investment" class="btn btn-secondary w-full">ابدأ الآن</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Risk Categories -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.RiskManagementTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                @Model.RiskManagementDescription
            </p>
        </div>
        
        <div class="grid grid-cols-3" style="gap: var(--space-8);">
            <!-- Low Risk -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4" style="background: var(--success-color);">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h4>@Model.LowRiskTitle</h4>
                    <div class="text-2xl font-bold text-success mt-2">@Model.LowRiskReturn%</div>
                    <div class="text-sm text-secondary">عائد سنوي متوقع</div>
                </div>
                <div class="card-body">
                    <p class="text-secondary">@Model.LowRiskDescription</p>
                </div>
            </div>
            
            <!-- Medium Risk -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4" style="background: var(--warning-color);">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <h4>@Model.MediumRiskTitle</h4>
                    <div class="text-2xl font-bold text-warning mt-2">@Model.MediumRiskReturn%</div>
                    <div class="text-sm text-secondary">عائد سنوي متوقع</div>
                </div>
                <div class="card-body">
                    <p class="text-secondary">@Model.MediumRiskDescription</p>
                </div>
            </div>
            
            <!-- High Risk -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4" style="background: var(--primary-color);">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                    <h4>@Model.HighRiskTitle</h4>
                    <div class="text-2xl font-bold text-primary mt-2">@Model.HighRiskReturn%</div>
                    <div class="text-sm text-secondary">عائد سنوي متوقع</div>
                </div>
                <div class="card-body">
                    <p class="text-secondary">@Model.HighRiskDescription</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Investment Process -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.ProcessTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                @Model.ProcessDescription
            </p>
        </div>
        
        <div class="grid grid-cols-4" style="gap: var(--space-8);">
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">1</span>
                </div>
                <h5 class="mb-3">إنشاء حساب</h5>
                <p class="text-secondary text-sm">
                    سجل حسابك وأكمل عملية التحقق من الهوية
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">2</span>
                </div>
                <h5 class="mb-3">إيداع الأموال</h5>
                <p class="text-secondary text-sm">
                    أضف الأموال إلى محفظتك بطرق دفع متنوعة وآمنة
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">3</span>
                </div>
                <h5 class="mb-3">اختر الاستثمار</h5>
                <p class="text-secondary text-sm">
                    حدد نوع الاستثمار ومستوى المخاطر المناسب لك
                </p>
            </div>
            
            <div class="text-center">
                <div class="feature-icon mx-auto mb-4" style="background: var(--secondary-color);">
                    <span class="font-bold text-white">4</span>
                </div>
                <h5 class="mb-3">تابع العوائد</h5>
                <p class="text-secondary text-sm">
                    راقب أداء استثماراتك واحصل على العوائد شهرياً
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Fees & Charges -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="grid grid-cols-2" style="gap: var(--space-12);">
            <!-- Fees -->
            <div>
                <h2 class="mb-6">@Model.FeesTitle</h2>
                <p class="text-lg text-secondary mb-8">@Model.FeesDescription</p>
                
                <div class="space-y-6">
                    <div class="flex justify-between items-center p-4 bg-secondary rounded-lg">
                        <div>
                            <h5 class="mb-1">رسوم الإدارة</h5>
                            <p class="text-sm text-secondary">رسوم سنوية على إجمالي المحفظة</p>
                        </div>
                        <div class="text-2xl font-bold text-primary">@Model.ManagementFee%</div>
                    </div>
                    
                    <div class="flex justify-between items-center p-4 bg-secondary rounded-lg">
                        <div>
                            <h5 class="mb-1">رسوم الأداء</h5>
                            <p class="text-sm text-secondary">نسبة من الأرباح المحققة فقط</p>
                        </div>
                        <div class="text-2xl font-bold text-primary">@Model.PerformanceFee%</div>
                    </div>
                </div>
            </div>
            
            <!-- Sharia Compliance -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3>@Model.ShariaComplianceTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary mb-6">@Model.ShariaComplianceDescription</p>
                    
                    <div class="space-y-4">
                        <div class="flex items-center" style="gap: var(--space-3);">
                            <div class="w-2 h-2 bg-success rounded-full"></div>
                            <span class="text-sm">خالي من الربا والغرر</span>
                        </div>
                        <div class="flex items-center" style="gap: var(--space-3);">
                            <div class="w-2 h-2 bg-success rounded-full"></div>
                            <span class="text-sm">معتمد من لجنة شرعية مستقلة</span>
                        </div>
                        <div class="flex items-center" style="gap: var(--space-3);">
                            <div class="w-2 h-2 bg-success rounded-full"></div>
                            <span class="text-sm">مراجعة دورية للامتثال</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Historical Performance -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.HistoricalPerformanceTitle</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                أداء متميز وعوائد مستقرة على مدار السنوات الماضية
            </p>
        </div>
        
        <div class="grid grid-cols-4" style="gap: var(--space-8);">
            <div class="text-center">
                <div class="text-4xl font-bold text-primary mb-2">@Model.AverageReturn%</div>
                <div class="text-secondary">متوسط العائد السنوي</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl font-bold text-primary mb-2">@Model.BestReturn%</div>
                <div class="text-secondary">أفضل عائد محقق</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl font-bold text-primary mb-2">@Model.DefaultRate%</div>
                <div class="text-secondary">معدل التعثر</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl font-bold text-primary mb-2">@(Convert.ToDecimal(Model.TotalInvestments) / 1000000)م</div>
                <div class="text-secondary">إجمالي الاستثمارات</div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center">
            <h2 class="mb-6">ابدأ استثمارك اليوم</h2>
            <p class="text-xl text-secondary mb-8 max-w-2xl mx-auto">
                انضم إلى آلاف المستثمرين واستفد من فرص استثمارية متميزة بعوائد تنافسية
            </p>
            <div class="flex justify-center" style="gap: var(--space-4);">
                <a href="/register" class="btn btn-primary btn-lg">
                    ابدأ الاستثمار
                </a>
                <a href="/calculator" class="btn btn-outline btn-lg">
                    احسب عوائدك
                </a>
            </div>
        </div>
    </div>
</section>
