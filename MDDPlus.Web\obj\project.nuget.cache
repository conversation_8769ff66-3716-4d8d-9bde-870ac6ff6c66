{"version": 2, "dgSpecHash": "qNedB5RUh2c=", "success": true, "projectFilePath": "D:\\project\\mdd_plus\\MDDPlus.Web\\MDDPlus.Web.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.abstractions\\8.1.0\\asp.versioning.abstractions.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.http\\8.1.0\\asp.versioning.http.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc\\8.1.0\\asp.versioning.mvc.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc.apiexplorer\\8.1.0\\asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.44.1\\azure.core.1.44.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.13.2\\azure.identity.1.13.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.5.1\\bouncycastle.cryptography.2.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dazinator.extensions.fileproviders\\2.0.0\\dazinator.extensions.fileproviders.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dotnet.glob\\3.1.0\\dotnet.glob.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\examine\\3.7.1\\examine.3.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\examine.core\\3.7.1\\examine.core.3.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\examine.lucene\\3.7.1\\examine.lucene.3.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\htmlagilitypack\\1.12.1\\htmlagilitypack.1.12.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\j2n\\2.1.0\\j2n.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\json.more.net\\2.1.0\\json.more.net.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpatch.net\\3.3.0\\jsonpatch.net.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpointer.net\\5.2.0\\jsonpointer.net.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\k4os.compression.lz4\\1.3.8\\k4os.compression.lz4.1.3.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net\\4.8.0-beta00017\\lucene.net.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.analysis.common\\4.8.0-beta00017\\lucene.net.analysis.common.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.facet\\4.8.0-beta00017\\lucene.net.facet.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.grouping\\4.8.0-beta00017\\lucene.net.grouping.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.join\\4.8.0-beta00017\\lucene.net.join.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.queries\\4.8.0-beta00017\\lucene.net.queries.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.queryparser\\4.8.0-beta00017\\lucene.net.queryparser.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.replicator\\4.8.0-beta00017\\lucene.net.replicator.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\lucene.net.sandbox\\4.8.0-beta00017\\lucene.net.sandbox.4.8.0-beta00017.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mailkit\\4.11.0\\mailkit.4.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\markdown\\2.2.1\\markdown.2.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\3.1.3\\messagepack.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\3.1.3\\messagepack.annotations.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepackanalyzer\\3.1.3\\messagepackanalyzer.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\9.0.4\\microsoft.aspnetcore.cryptography.internal.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\9.0.4\\microsoft.aspnetcore.cryptography.keyderivation.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\8.0.4\\microsoft.aspnetcore.dataprotection.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\8.0.4\\microsoft.aspnetcore.dataprotection.abstractions.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\1.0.2\\microsoft.aspnetcore.hosting.abstractions.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\1.0.2\\microsoft.aspnetcore.hosting.server.abstractions.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\1.0.2\\microsoft.aspnetcore.http.abstractions.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\1.0.2\\microsoft.aspnetcore.http.features.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\6.0.0\\microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\9.0.4\\microsoft.aspnetcore.mvc.razor.runtimecompilation.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\6.0.0\\microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.cryptography\\9.0.0\\microsoft.bcl.cryptography.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.11.0\\microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.13.0\\microsoft.codeanalysis.common.4.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.13.0\\microsoft.codeanalysis.csharp.4.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\6.0.0\\microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\6.0.1\\microsoft.data.sqlclient.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\6.0.2\\microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite\\9.0.4\\microsoft.data.sqlite.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\9.0.4\\microsoft.data.sqlite.core.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.4\\microsoft.entityframeworkcore.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.4\\microsoft.entityframeworkcore.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.4\\microsoft.entityframeworkcore.analyzers.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.4\\microsoft.entityframeworkcore.relational.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite\\9.0.4\\microsoft.entityframeworkcore.sqlite.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.core\\9.0.4\\microsoft.entityframeworkcore.sqlite.core.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\9.0.4\\microsoft.entityframeworkcore.sqlserver.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ambientmetadata.application\\9.2.0\\microsoft.extensions.ambientmetadata.application.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.4\\microsoft.extensions.caching.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.hybrid\\9.4.0\\microsoft.extensions.caching.hybrid.9.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.4\\microsoft.extensions.caching.memory.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.compliance.abstractions\\9.2.0\\microsoft.extensions.compliance.abstractions.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.4\\microsoft.extensions.configuration.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.4\\microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.4\\microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.4\\microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.4\\microsoft.extensions.configuration.json.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.4\\microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.4\\microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.autoactivation\\9.2.0\\microsoft.extensions.dependencyinjection.autoactivation.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.4\\microsoft.extensions.dependencymodel.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.4\\microsoft.extensions.diagnostics.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.4\\microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.exceptionsummarization\\9.2.0\\microsoft.extensions.diagnostics.exceptionsummarization.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.4\\microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\9.0.4\\microsoft.extensions.fileproviders.embedded.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.4\\microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.4\\microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.4\\microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\9.0.4\\microsoft.extensions.http.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.diagnostics\\9.2.0\\microsoft.extensions.http.diagnostics.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.polly\\9.0.2\\microsoft.extensions.http.polly.9.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.resilience\\9.2.0\\microsoft.extensions.http.resilience.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\9.0.4\\microsoft.extensions.identity.core.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.stores\\9.0.4\\microsoft.extensions.identity.stores.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.4\\microsoft.extensions.logging.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.4\\microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.2\\microsoft.extensions.logging.configuration.9.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\9.0.2\\microsoft.extensions.objectpool.9.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.4\\microsoft.extensions.options.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.4\\microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.dataannotations\\9.0.4\\microsoft.extensions.options.dataannotations.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.4\\microsoft.extensions.primitives.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.resilience\\9.2.0\\microsoft.extensions.resilience.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry\\9.2.0\\microsoft.extensions.telemetry.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.telemetry.abstractions\\9.2.0\\microsoft.extensions.telemetry.abstractions.9.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime\\72.1.0.3\\microsoft.icu.icu4c.runtime.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.linux-arm64\\72.1.0.3\\microsoft.icu.icu4c.runtime.linux-arm64.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.linux-x64\\72.1.0.3\\microsoft.icu.icu4c.runtime.linux-x64.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-arm64\\72.1.0.3\\microsoft.icu.icu4c.runtime.win-arm64.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-x64\\72.1.0.3\\microsoft.icu.icu4c.runtime.win-x64.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.icu.icu4c.runtime.win-x86\\72.1.0.3\\microsoft.icu.icu4c.runtime.win-x86.72.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.67.2\\microsoft.identity.client.4.67.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.67.2\\microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\8.8.0\\microsoft.identitymodel.abstractions.8.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\8.8.0\\microsoft.identitymodel.jsonwebtokens.8.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\8.8.0\\microsoft.identitymodel.logging.8.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\8.4.0\\microsoft.identitymodel.protocols.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.5.0\\microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\8.8.0\\microsoft.identitymodel.tokens.8.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\9.0.2\\microsoft.net.http.headers.9.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.11.4\\microsoft.net.stringtools.17.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.23\\microsoft.openapi.1.6.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mimekit\\4.11.0\\mimekit.4.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.aspnetcore\\4.5.4\\miniprofiler.aspnetcore.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.aspnetcore.mvc\\4.5.4\\miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\miniprofiler.shared\\4.5.4\\miniprofiler.shared.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ncrontab\\3.3.3\\ncrontab.3.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npoco\\5.7.1\\npoco.5.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npoco.sqlserver\\5.7.1\\npoco.sqlserver.5.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict\\6.2.1\\openiddict.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.abstractions\\6.2.1\\openiddict.abstractions.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.aspnetcore\\6.2.1\\openiddict.aspnetcore.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client\\6.2.1\\openiddict.client.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client.aspnetcore\\6.2.1\\openiddict.client.aspnetcore.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client.dataprotection\\6.2.1\\openiddict.client.dataprotection.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client.systemintegration\\6.2.1\\openiddict.client.systemintegration.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client.systemnethttp\\6.2.1\\openiddict.client.systemnethttp.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.client.webintegration\\6.2.1\\openiddict.client.webintegration.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.core\\6.2.1\\openiddict.core.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.entityframeworkcore\\6.2.1\\openiddict.entityframeworkcore.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.entityframeworkcore.models\\6.2.1\\openiddict.entityframeworkcore.models.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.server\\6.2.1\\openiddict.server.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.server.aspnetcore\\6.2.1\\openiddict.server.aspnetcore.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.server.dataprotection\\6.2.1\\openiddict.server.dataprotection.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.validation\\6.2.1\\openiddict.validation.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.validation.aspnetcore\\6.2.1\\openiddict.validation.aspnetcore.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.validation.dataprotection\\6.2.1\\openiddict.validation.dataprotection.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.validation.serverintegration\\6.2.1\\openiddict.validation.serverintegration.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openiddict.validation.systemnethttp\\6.2.1\\openiddict.validation.systemnethttp.6.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly\\7.2.4\\polly.7.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.4.2\\polly.core.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions\\8.4.2\\polly.extensions.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.ratelimiting\\8.4.2\\polly.ratelimiting.8.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.2.0\\serilog.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\9.0.0\\serilog.aspnetcore.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.process\\3.0.0\\serilog.enrichers.process.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.thread\\4.0.0\\serilog.enrichers.thread.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.expressions\\5.0.0\\serilog.expressions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\9.0.0\\serilog.extensions.hosting.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\9.0.0\\serilog.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\3.0.0\\serilog.formatting.compact.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact.reader\\4.0.0\\serilog.formatting.compact.reader.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\9.0.0\\serilog.settings.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.async\\2.1.0\\serilog.sinks.async.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\3.0.0\\serilog.sinks.debug.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.map\\2.0.0\\serilog.sinks.map.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.7\\sixlabors.imagesharp.3.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp.web\\3.1.4\\sixlabors.imagesharp.web.3.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\8.1.1\\swashbuckle.aspnetcore.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\8.1.1\\swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\8.1.1\\swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\8.1.1\\swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.3.0\\system.buffers.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.1.0\\system.clientmodel.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel\\4.0.1\\system.componentmodel.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.4\\system.configuration.configurationmanager.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.4\\system.diagnostics.eventlog.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\9.0.4\\system.formats.asn1.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.5.0\\system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.2\\system.io.pipelines.9.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.async\\5.0.0\\system.linq.async.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\6.0.0\\system.memory.data.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.websockets\\4.0.0\\system.net.websockets.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.7.0\\system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\8.0.0\\system.reflection.metadata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\9.0.4\\system.runtime.caching.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.0.1\\system.security.claims.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\9.0.4\\system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.4\\system.security.cryptography.protecteddata.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\9.0.4\\system.security.cryptography.xml.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.0.1\\system.security.principal.4.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.4\\system.text.encodings.web.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.4\\system.text.json.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\8.0.0\\system.threading.accesscontrol.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.ratelimiting\\8.0.0\\system.threading.ratelimiting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms\\16.0.0\\umbraco.cms.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.api.common\\16.0.0\\umbraco.cms.api.common.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.api.delivery\\16.0.0\\umbraco.cms.api.delivery.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.api.management\\16.0.0\\umbraco.cms.api.management.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.core\\16.0.0\\umbraco.cms.core.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.examine.lucene\\16.0.0\\umbraco.cms.examine.lucene.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.imaging.imagesharp\\16.0.0\\umbraco.cms.imaging.imagesharp.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.infrastructure\\16.0.0\\umbraco.cms.infrastructure.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.persistence.efcore\\16.0.0\\umbraco.cms.persistence.efcore.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.persistence.efcore.sqlite\\16.0.0\\umbraco.cms.persistence.efcore.sqlite.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.persistence.efcore.sqlserver\\16.0.0\\umbraco.cms.persistence.efcore.sqlserver.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.persistence.sqlite\\16.0.0\\umbraco.cms.persistence.sqlite.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.persistence.sqlserver\\16.0.0\\umbraco.cms.persistence.sqlserver.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.publishedcache.hybridcache\\16.0.0\\umbraco.cms.publishedcache.hybridcache.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.staticassets\\16.0.0\\umbraco.cms.staticassets.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.targets\\16.0.0\\umbraco.cms.targets.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.web.common\\16.0.0\\umbraco.cms.web.common.16.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\umbraco.cms.web.website\\16.0.0\\umbraco.cms.web.website.16.0.0.nupkg.sha512"], "logs": []}