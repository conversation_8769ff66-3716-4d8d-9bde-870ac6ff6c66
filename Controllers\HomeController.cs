using Microsoft.AspNetCore.Mvc;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class HomeController : Controller
    {
        [Route("")]
        [Route("home")]
        public IActionResult Index()
        {
            // Create a sample model with demo data
            var model = new HomePage
            {
                HeroTitle = "منصة التمويل الجماعي بالدين الرائدة في السعودية",
                HeroSubtitle = "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. استثمر بأمان واحصل على عوائد تنافسية تصل إلى 18% سنوياً.",
                HeroButtonText = "ابدأ الاستثمار الآن",
                AboutTitle = "من نحن",
                AboutText = "<p>مدد بلس هي منصة التمويل الجماعي بالدين الرائدة في المملكة العربية السعودية. نحن نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، مما يتيح للمستثمرين الحصول على عوائد تنافسية بينما ندعم الشركات الصغيرة والمتوسطة في تحقيق أهدافها.</p><p>مرخصة من البنك المركزي السعودي، نحن ملتزمون بأعلى معايير الشفافية والأمان في جميع عملياتنا.</p>",
                ServicesTitle = "خدماتنا",
                TotalFunded = "500,000,000",
                AnnualReturn = "18",
                ActiveInvestors = "5,000",
                RepaymentRate = "99.8",
                ContactTitle = "تواصل معنا",
                ContactEmail = "<EMAIL>",
                ContactPhone = "+966 11 234 5678",
                MetaTitle = "مدد بلس - منصة التمويل الجماعي بالدين الرائدة في السعودية",
                MetaDescription = "منصة مدد بلس للتمويل الجماعي بالدين - حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. عوائد تصل إلى 18% سنوياً."
            };

            return View("HomePage", model);
        }

        [Route("demo")]
        public IActionResult Demo()
        {
            return Content(@"
<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>مدد بلس - منصة التمويل الجماعي</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif; 
            line-height: 1.6; 
            color: #333;
            direction: rtl;
            text-align: right;
        }
        .hero { 
            background: linear-gradient(135deg, #2c5aa0, #1e3d72); 
            color: white; 
            padding: 100px 0; 
            text-align: center;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        h1 { font-size: 3rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .btn { 
            background: #ffd700; 
            color: #333; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 25px; 
            font-size: 1.1rem; 
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0,0,0,0.2); }
        .stats { 
            background: #f8f9fa; 
            padding: 60px 0; 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 30px; 
            text-align: center;
        }
        .stat-item { 
            background: white; 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stat-item:hover { transform: translateY(-5px); }
        .stat-number { 
            font-size: 2.5rem; 
            font-weight: bold; 
            color: #2c5aa0; 
            margin-bottom: 10px;
        }
        .stat-label { color: #666; font-weight: 500; }
        .about { padding: 80px 0; background: white; }
        .about h2 { 
            font-size: 2.5rem; 
            color: #2c5aa0; 
            margin-bottom: 30px; 
            text-align: center;
        }
        .about-content { 
            max-width: 800px; 
            margin: 0 auto; 
            font-size: 1.1rem; 
            line-height: 1.8;
        }
        .contact { 
            background: linear-gradient(135deg, #2c5aa0, #1e3d72); 
            color: white; 
            padding: 60px 0; 
            text-align: center;
        }
        .contact h2 { margin-bottom: 30px; }
        .contact-info { 
            display: flex; 
            justify-content: center; 
            gap: 50px; 
            flex-wrap: wrap;
        }
        .contact-item { 
            background: rgba(255,255,255,0.1); 
            padding: 20px; 
            border-radius: 10px; 
            backdrop-filter: blur(10px);
        }
        .contact-item strong { display: block; margin-bottom: 10px; }
        .contact-item a { color: #ffd700; text-decoration: none; }
        .setup-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
        }
        .setup-notice h3 { color: #856404; margin-bottom: 10px; }
        .setup-notice a { color: #2c5aa0; font-weight: bold; }
    </style>
</head>
<body>
    <div class='setup-notice'>
        <h3>🚀 موقع مدد بلس جاهز للعمل!</h3>
        <p>هذا عرض تجريبي للموقع. لإدارة المحتوى، انتقل إلى <a href='/umbraco'>لوحة تحكم أمبراكو</a></p>
        <p>Demo website ready! Go to <a href='/umbraco'>Umbraco Admin</a> to manage content</p>
    </div>

    <section class='hero'>
        <div class='container'>
            <h1>منصة التمويل الجماعي بالدين الرائدة في السعودية</h1>
            <p>حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. استثمر بأمان واحصل على عوائد تنافسية تصل إلى 18% سنوياً.</p>
            <a href='#about' class='btn'>ابدأ الاستثمار الآن</a>
        </div>
    </section>

    <section class='stats'>
        <div class='container'>
            <div class='stats-grid'>
                <div class='stat-item'>
                    <div class='stat-number'>500,000,000</div>
                    <div class='stat-label'>إجمالي التمويل (ريال)</div>
                </div>
                <div class='stat-item'>
                    <div class='stat-number'>18%</div>
                    <div class='stat-label'>العائد السنوي</div>
                </div>
                <div class='stat-item'>
                    <div class='stat-number'>5,000</div>
                    <div class='stat-label'>المستثمرون النشطون</div>
                </div>
                <div class='stat-item'>
                    <div class='stat-number'>99.8%</div>
                    <div class='stat-label'>معدل السداد</div>
                </div>
            </div>
        </div>
    </section>

    <section class='about' id='about'>
        <div class='container'>
            <h2>من نحن</h2>
            <div class='about-content'>
                <p>مدد بلس هي منصة التمويل الجماعي بالدين الرائدة في المملكة العربية السعودية. نحن نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، مما يتيح للمستثمرين الحصول على عوائد تنافسية بينما ندعم الشركات الصغيرة والمتوسطة في تحقيق أهدافها.</p>
                <p>مرخصة من البنك المركزي السعودي، نحن ملتزمون بأعلى معايير الشفافية والأمان في جميع عملياتنا.</p>
            </div>
        </div>
    </section>

    <section class='contact'>
        <div class='container'>
            <h2>تواصل معنا</h2>
            <div class='contact-info'>
                <div class='contact-item'>
                    <strong>البريد الإلكتروني</strong>
                    <a href='mailto:<EMAIL>'><EMAIL></a>
                </div>
                <div class='contact-item'>
                    <strong>الهاتف</strong>
                    <a href='tel:+966112345678'>+966 11 234 5678</a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Add smooth scrolling
        document.querySelectorAll('a[href^=""#""]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Animate stats on scroll
        const observerOptions = { threshold: 0.5 };
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));
                        if (numericValue) {
                            let current = 0;
                            const increment = numericValue / 50;
                            const timer = setInterval(() => {
                                current += increment;
                                if (current >= numericValue) {
                                    current = numericValue;
                                    clearInterval(timer);
                                }
                                stat.textContent = finalValue.replace(/[0-9,]+/, Math.floor(current).toLocaleString());
                            }, 30);
                        }
                    });
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        observer.observe(document.querySelector('.stats'));
    </script>
</body>
</html>", "text/html");
        }
    }
}
