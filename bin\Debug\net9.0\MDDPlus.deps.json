{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET9_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NET9_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "13.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v9.0": {"MDDPlus/1.0.0": {"dependencies": {"Microsoft.ICU.ICU4C.Runtime": "********", "Umbraco.Cms": "16.0.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticAssets": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.VisualBasic.Core": "1*******", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives.Reference": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "*******", "System.AppContext.Reference": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel.Reference": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console.Reference": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools.Reference": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression.Reference": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile.Reference": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets.Reference": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets.Reference": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl": "*******", "System.Security.Claims.Reference": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.OpenSsl.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security": "*******", "System.Security.Principal.Reference": "*******", "System.Security.Principal.Windows": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting.Reference": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer.Reference": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument.Reference": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"MDDPlus.dll": {}}, "compile": {"MDDPlus.dll": {}}}, "Asp.Versioning.Abstractions/8.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31619"}}, "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {}}}, "Asp.Versioning.Http/8.1.0": {"dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {}}}, "Asp.Versioning.Mvc/8.1.0": {"dependencies": {"Asp.Versioning.Http": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {}}}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31636"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.4", "System.Text.Json": "9.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}, "compile": {"lib/net6.0/Azure.Core.dll": {}}}, "Azure.Identity/1.13.2": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "********", "fileVersion": "1.1300.225.6404"}}, "compile": {"lib/net8.0/Azure.Identity.dll": {}}}, "BouncyCastle.Cryptography/2.5.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.1.28965"}}, "compile": {"lib/net6.0/BouncyCastle.Cryptography.dll": {}}}, "Dazinator.Extensions.FileProviders/2.0.0": {"dependencies": {"DotNet.Glob": "3.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.2", "Microsoft.AspNetCore.Http.Abstractions": "1.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.3/Dazinator.Extensions.FileProviders.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard1.3/Dazinator.Extensions.FileProviders.dll": {}}}, "DotNet.Glob/3.1.0": {"runtime": {"lib/netcoreapp2.1/DotNet.Glob.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp2.1/DotNet.Glob.dll": {}}}, "Examine/3.7.1": {"dependencies": {"Examine.Core": "3.7.1", "Examine.Lucene": "3.7.1", "Microsoft.AspNetCore.DataProtection": "8.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "System.Formats.Asn1": "9.0.4"}, "runtime": {"lib/net8.0/Examine.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Examine.dll": {}}}, "Examine.Core/3.7.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net8.0/Examine.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Examine.Core.dll": {}}}, "Examine.Lucene/3.7.1": {"dependencies": {"Examine.Core": "3.7.1", "Lucene.Net.QueryParser": "4.8.0-beta00017", "Lucene.Net.Replicator": "4.8.0-beta00017", "System.Threading": "4.3.0", "System.Threading.AccessControl": "8.0.0"}, "runtime": {"lib/net8.0/Examine.Lucene.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Examine.Lucene.dll": {}}}, "HtmlAgilityPack/1.12.1": {"runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.12.1.0", "fileVersion": "1.12.1.0"}}, "compile": {"lib/net8.0/HtmlAgilityPack.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}, "compile": {"lib/net6.0/Humanizer.dll": {}}}, "J2N/2.1.0": {"runtime": {"lib/net8.0/J2N.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/J2N.dll": {}}}, "Json.More.Net/2.1.0": {"runtime": {"lib/net9.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Json.More.dll": {}}}, "JsonPatch.Net/3.3.0": {"dependencies": {"JsonPointer.Net": "5.2.0"}, "runtime": {"lib/net9.0/JsonPatch.Net.dll": {"assemblyVersion": "*******", "fileVersion": "3.3.0.0"}}, "compile": {"lib/net9.0/JsonPatch.Net.dll": {}}}, "JsonPointer.Net/5.2.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}, "runtime": {"lib/net9.0/JsonPointer.Net.dll": {"assemblyVersion": "*******", "fileVersion": "5.2.0.0"}}, "compile": {"lib/net9.0/JsonPointer.Net.dll": {}}}, "K4os.Compression.LZ4/1.3.8": {"runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.3.8.0", "fileVersion": "1.3.8.0"}}, "compile": {"lib/net6.0/K4os.Compression.LZ4.dll": {}}}, "Lucene.Net/4.8.0-beta00017": {"dependencies": {"J2N": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Lucene.Net.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.dll": {}}}, "Lucene.Net.Analysis.Common/4.8.0-beta00017": {"dependencies": {"Lucene.Net": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Analysis.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Analysis.Common.dll": {}}}, "Lucene.Net.Facet/4.8.0-beta00017": {"dependencies": {"Lucene.Net.Join": "4.8.0-beta00017", "Lucene.Net.Queries": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Facet.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Facet.dll": {}}}, "Lucene.Net.Grouping/4.8.0-beta00017": {"dependencies": {"Lucene.Net": "4.8.0-beta00017", "Lucene.Net.Queries": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Grouping.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Grouping.dll": {}}}, "Lucene.Net.Join/4.8.0-beta00017": {"dependencies": {"Lucene.Net.Grouping": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Join.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Join.dll": {}}}, "Lucene.Net.Queries/4.8.0-beta00017": {"dependencies": {"Lucene.Net": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Queries.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Queries.dll": {}}}, "Lucene.Net.QueryParser/4.8.0-beta00017": {"dependencies": {"Lucene.Net.Analysis.Common": "4.8.0-beta00017", "Lucene.Net.Queries": "4.8.0-beta00017", "Lucene.Net.Sandbox": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.QueryParser.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.QueryParser.dll": {}}}, "Lucene.Net.Replicator/4.8.0-beta00017": {"dependencies": {"J2N": "2.1.0", "Lucene.Net": "4.8.0-beta00017", "Lucene.Net.Facet": "4.8.0-beta00017", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Lucene.Net.Replicator.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Replicator.dll": {}}}, "Lucene.Net.Sandbox/4.8.0-beta00017": {"dependencies": {"Lucene.Net": "4.8.0-beta00017"}, "runtime": {"lib/net8.0/Lucene.Net.Sandbox.dll": {"assemblyVersion": "*******", "fileVersion": "4.8.0.0"}}, "compile": {"lib/net8.0/Lucene.Net.Sandbox.dll": {}}}, "MailKit/4.11.0": {"dependencies": {"MimeKit": "4.11.0", "System.Formats.Asn1": "9.0.4"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/MailKit.dll": {}}}, "Markdown/2.2.1": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard1.3/Markdown.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.3/Markdown.dll": {}}}, "MessagePack/3.1.3": {"dependencies": {"MessagePack.Annotations": "3.1.3", "MessagePackAnalyzer": "3.1.3", "Microsoft.NET.StringTools": "17.11.4"}, "runtime": {"lib/net9.0/MessagePack.dll": {"assemblyVersion": "3.1.3.0", "fileVersion": "3.1.3.0"}}, "compile": {"lib/net9.0/MessagePack.dll": {}}}, "MessagePack.Annotations/3.1.3": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "3.1.3.0", "fileVersion": "3.1.3.0"}}, "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {}}}, "MessagePackAnalyzer/3.1.3": {}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.4": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}}, "Microsoft.AspNetCore.DataProtection/8.0.4": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.4", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Security.Cryptography.Xml": "9.0.4"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.4": {}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.2", "Microsoft.AspNetCore.Http.Abstractions": "1.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}}, "Microsoft.AspNetCore.Http.Abstractions/1.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.2", "System.Globalization.Extensions": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encodings.Web": "9.0.4"}}, "Microsoft.AspNetCore.Http.Features/1.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4", "System.Collections": "4.3.0", "System.ComponentModel": "4.0.1", "System.Linq": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.WebSockets": "4.0.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.Principal": "4.0.1"}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Bcl.Cryptography/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {}, "Microsoft.CodeAnalysis.Common/4.13.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "4.13.0.0", "fileVersion": "4.1300.25.12011"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net9.0/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.13.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "4.13.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "4.13.0.0", "fileVersion": "4.1300.25.12011"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net9.0/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.13.0", "Microsoft.CodeAnalysis.Common": "4.13.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.Data.SqlClient/6.0.1": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Bcl.Cryptography": "9.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Cryptography.Pkcs": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}, "compile": {"ref/net9.0/Microsoft.Data.SqlClient.dll": {}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.2.0.0"}}}, "Microsoft.Data.Sqlite/9.0.4": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.4", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {}}}, "Microsoft.EntityFrameworkCore/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.4", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.4": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.4": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyModel": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.4"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.4": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.4", "Microsoft.EntityFrameworkCore.Relational": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyModel": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.4": {"dependencies": {"Microsoft.Data.SqlClient": "6.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "System.Formats.Asn1": "9.0.4", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16310"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Hybrid/9.4.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Hybrid.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Hybrid.dll": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.ObjectPool": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {}}}, "Microsoft.Extensions.DependencyModel/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Http/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.dll": {}}}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4", "Microsoft.Extensions.Telemetry": "9.2.0", "System.IO.Pipelines": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {}}}, "Microsoft.Extensions.Http.Polly/9.0.2": {"dependencies": {"Microsoft.Extensions.Http": "9.0.4", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {}}}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Http.Diagnostics": "9.2.0", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Resilience": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {}}}, "Microsoft.Extensions.Identity.Core/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {}}}, "Microsoft.Extensions.Identity.Stores/9.0.4": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.Identity.Core": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {}}}, "Microsoft.Extensions.Logging/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {}}}, "Microsoft.Extensions.ObjectPool/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}, "compile": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.Options.DataAnnotations/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.DataAnnotations.dll": {}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.Resilience/9.2.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {}}}, "Microsoft.Extensions.Telemetry/9.2.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.2.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.2.0", "Microsoft.Extensions.Logging.Configuration": "9.0.2", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Telemetry.Abstractions": "9.2.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.2.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.ObjectPool": "9.0.2", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.200.25.10506"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {}}}, "Microsoft.ICU.ICU4C.Runtime/********": {"dependencies": {"Microsoft.ICU.ICU4C.Runtime.linux-arm64": "********", "Microsoft.ICU.ICU4C.Runtime.linux-x64": "********", "Microsoft.ICU.ICU4C.Runtime.win-arm64": "********", "Microsoft.ICU.ICU4C.Runtime.win-x64": "********", "Microsoft.ICU.ICU4C.Runtime.win-x86": "********"}}, "Microsoft.ICU.ICU4C.Runtime.linux-arm64/********": {"runtimeTargets": {"runtimes/linux-arm64/native/libicudata.so.********": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libicui18n.so.********": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libicuuc.so.********": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ICU.ICU4C.Runtime.linux-x64/********": {"runtimeTargets": {"runtimes/linux-x64/native/libicudata.so.********": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libicui18n.so.********": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libicuuc.so.********": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ICU.ICU4C.Runtime.win-arm64/********": {"runtimeTargets": {"runtimes/win-arm64/native/icudt72.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-arm64/native/icuin72.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-arm64/native/icuuc72.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "********"}}}, "Microsoft.ICU.ICU4C.Runtime.win-x64/********": {"runtimeTargets": {"runtimes/win-x64/native/icudt72.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x64/native/icuin72.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x64/native/icuuc72.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}}}, "Microsoft.ICU.ICU4C.Runtime.win-x86/********": {"runtimeTargets": {"runtimes/win-x86/native/icudt72.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x86/native/icuin72.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}, "runtimes/win-x86/native/icuuc72.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "********"}}}, "Microsoft.Identity.Client/4.67.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.8.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}, "compile": {"lib/net8.0/Microsoft.Identity.Client.dll": {}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}, "compile": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.8.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.8.0.0", "fileVersion": "8.8.0.60408"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.8.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.8.0.0", "fileVersion": "8.8.0.60408"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.8.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.8.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.8.0.0", "fileVersion": "8.8.0.60408"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.8.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.60207"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.4.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.8.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.IdentityModel.Logging": "8.8.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.8.0.0", "fileVersion": "8.8.0.60408"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}, "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {}}}, "Microsoft.Net.Http.Headers/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6704"}}, "compile": {"lib/net9.0/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.NET.StringTools/17.11.4": {"runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.11.4.40609"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "MimeKit/4.11.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "9.0.4"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/MimeKit.dll": {}}}, "MiniProfiler.AspNetCore/4.5.4": {"dependencies": {"MiniProfiler.Shared": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}, "compile": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {}}}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"dependencies": {"MiniProfiler.AspNetCore": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}, "compile": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {}}}, "MiniProfiler.Shared/4.5.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}, "compile": {"lib/net8.0/MiniProfiler.Shared.dll": {}}}, "NCrontab/3.3.3": {"runtime": {"lib/netstandard2.0/NCrontab.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/NCrontab.dll": {}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "NPoco/5.7.1": {"dependencies": {"System.Linq.Async": "5.0.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.1/NPoco.dll": {"assemblyVersion": "5.7.1.0", "fileVersion": "5.7.1.0"}}, "compile": {"lib/netstandard2.1/NPoco.dll": {}}}, "NPoco.SqlServer/5.7.1": {"dependencies": {"Microsoft.Data.SqlClient": "6.0.1", "NPoco": "5.7.1", "Polly": "7.2.4"}, "runtime": {"lib/netstandard2.1/NPoco.SqlServer.dll": {"assemblyVersion": "5.7.1.0", "fileVersion": "5.7.1.0"}}, "compile": {"lib/netstandard2.1/NPoco.SqlServer.dll": {}}}, "OpenIddict/6.2.1": {"dependencies": {"OpenIddict.Abstractions": "6.2.1", "OpenIddict.Client": "6.2.1", "OpenIddict.Client.SystemIntegration": "6.2.1", "OpenIddict.Client.SystemNetHttp": "6.2.1", "OpenIddict.Client.WebIntegration": "6.2.1", "OpenIddict.Core": "6.2.1", "OpenIddict.Server": "6.2.1", "OpenIddict.Validation": "6.2.1", "OpenIddict.Validation.ServerIntegration": "6.2.1", "OpenIddict.Validation.SystemNetHttp": "6.2.1"}}, "OpenIddict.Abstractions/6.2.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Microsoft.IdentityModel.Tokens": "8.8.0"}, "runtime": {"lib/net9.0/OpenIddict.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Abstractions.dll": {}}}, "OpenIddict.AspNetCore/6.2.1": {"dependencies": {"OpenIddict": "6.2.1", "OpenIddict.Client.AspNetCore": "6.2.1", "OpenIddict.Client.DataProtection": "6.2.1", "OpenIddict.Server.AspNetCore": "6.2.1", "OpenIddict.Server.DataProtection": "6.2.1", "OpenIddict.Validation.AspNetCore": "6.2.1", "OpenIddict.Validation.DataProtection": "6.2.1"}}, "OpenIddict.Client/6.2.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.dll": {}}}, "OpenIddict.Client.AspNetCore/6.2.1": {"dependencies": {"OpenIddict.Client": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.AspNetCore.dll": {}}}, "OpenIddict.Client.DataProtection/6.2.1": {"dependencies": {"OpenIddict.Client": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.DataProtection.dll": {}}}, "OpenIddict.Client.SystemIntegration/6.2.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Net.Http.Headers": "9.0.2", "OpenIddict.Client": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.SystemIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.SystemIntegration.dll": {}}}, "OpenIddict.Client.SystemNetHttp/6.2.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Client": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.SystemNetHttp.dll": {}}}, "OpenIddict.Client.WebIntegration/6.2.1": {"dependencies": {"OpenIddict.Client": "6.2.1", "OpenIddict.Client.SystemNetHttp": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Client.WebIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Client.WebIntegration.dll": {}}}, "OpenIddict.Core/6.2.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "OpenIddict.Abstractions": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Core.dll": {}}}, "OpenIddict.EntityFrameworkCore/6.2.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.4", "OpenIddict.Core": "6.2.1", "OpenIddict.EntityFrameworkCore.Models": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.EntityFrameworkCore.dll": {}}}, "OpenIddict.EntityFrameworkCore.Models/6.2.1": {"runtime": {"lib/net9.0/OpenIddict.EntityFrameworkCore.Models.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.EntityFrameworkCore.Models.dll": {}}}, "OpenIddict.Server/6.2.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "OpenIddict.Abstractions": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Server.dll": {}}}, "OpenIddict.Server.AspNetCore/6.2.1": {"dependencies": {"OpenIddict.Server": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Server.AspNetCore.dll": {}}}, "OpenIddict.Server.DataProtection/6.2.1": {"dependencies": {"OpenIddict.Server": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Server.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Server.DataProtection.dll": {}}}, "OpenIddict.Validation/6.2.1": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "Microsoft.IdentityModel.Protocols": "8.4.0", "OpenIddict.Abstractions": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Validation.dll": {}}}, "OpenIddict.Validation.AspNetCore/6.2.1": {"dependencies": {"OpenIddict.Validation": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Validation.AspNetCore.dll": {}}}, "OpenIddict.Validation.DataProtection/6.2.1": {"dependencies": {"OpenIddict.Validation": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Validation.DataProtection.dll": {}}}, "OpenIddict.Validation.ServerIntegration/6.2.1": {"dependencies": {"OpenIddict.Server": "6.2.1", "OpenIddict.Validation": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.ServerIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Validation.ServerIntegration.dll": {}}}, "OpenIddict.Validation.SystemNetHttp/6.2.1": {"dependencies": {"Microsoft.Extensions.Http.Polly": "9.0.2", "Microsoft.Extensions.Http.Resilience": "9.2.0", "OpenIddict.Validation": "6.2.1"}, "runtime": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.125.22258"}}, "compile": {"lib/net9.0/OpenIddict.Validation.SystemNetHttp.dll": {}}}, "Polly/7.2.4": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.2.4.982"}}, "compile": {"lib/netstandard2.0/Polly.dll": {}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.4.2.3950"}}, "compile": {"lib/net8.0/Polly.Core.dll": {}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.4.2.3950"}}, "compile": {"lib/net8.0/Polly.Extensions.dll": {}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.4"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.4.2.3950"}}, "compile": {"lib/net8.0/Polly.RateLimiting.dll": {}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Serilog.dll": {}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Serilog.AspNetCore.dll": {}}}, "Serilog.Enrichers.Process/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Process.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Enrichers.Process.dll": {}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {}}}, "Serilog.Expressions/5.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Expressions.dll": {}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.4", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Serilog.Extensions.Logging.dll": {}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.dll": {}}}, "Serilog.Formatting.Compact.Reader/4.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.Reader.dll": {}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyModel": "9.0.4", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Serilog.Settings.Configuration.dll": {}}}, "Serilog.Sinks.Async/2.1.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Async.dll": {}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {}}}, "Serilog.Sinks.Map/2.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Map.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Map.dll": {}}}, "SixLabors.ImageSharp/3.1.7": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.7.0"}}, "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {}}}, "SixLabors.ImageSharp.Web/3.1.4": {"dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.1", "SixLabors.ImageSharp": "3.1.7"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Web.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.4.0"}}, "compile": {"lib/net6.0/SixLabors.ImageSharp.Web.dll": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "Swashbuckle.AspNetCore/8.1.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.1", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.1"}}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.1"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}, "compile": {"lib/net6.0/System.ClientModel.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.ComponentModel/4.0.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Configuration.ConfigurationManager/9.0.4": {"dependencies": {"System.Diagnostics.EventLog": "9.0.4", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.4": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Diagnostics.EventLog.dll": {}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Formats.Asn1/9.0.4": {"runtime": {"lib/net9.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Formats.Asn1.dll": {}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "Microsoft.IdentityModel.Tokens": "8.8.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Pipelines/9.0.2": {"runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}, "compile": {"lib/net9.0/System.IO.Pipelines.dll": {}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Async/5.0.0": {"runtime": {"lib/netcoreapp3.1/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"ref/netcoreapp3.1/System.Linq.Async.dll": {}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "9.0.4"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Memory.Data.dll": {}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Net.WebSockets/4.0.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/9.0.4": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4"}, "runtime": {"lib/net9.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Runtime.Caching.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Claims/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.0.1"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/9.0.4": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.4"}, "runtime": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {}}}, "System.Security.Principal/4.0.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/9.0.4": {"runtime": {"lib/net9.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/9.0.4": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "compile": {"lib/net9.0/System.Text.Json.dll": {}}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.AccessControl/8.0.0": {"runtime": {"lib/net8.0/System.Threading.AccessControl.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/System.Threading.AccessControl.dll": {}}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "Umbraco.Cms/16.0.0": {"dependencies": {"Umbraco.Cms.Imaging.ImageSharp": "16.0.0", "Umbraco.Cms.Persistence.EFCore.SqlServer": "16.0.0", "Umbraco.Cms.Persistence.EFCore.Sqlite": "16.0.0", "Umbraco.Cms.Persistence.SqlServer": "16.0.0", "Umbraco.Cms.Persistence.Sqlite": "16.0.0", "Umbraco.Cms.Targets": "16.0.0"}}, "Umbraco.Cms.Api.Common/16.0.0": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Microsoft.Extensions.Caching.Memory": "9.0.4", "OpenIddict.Abstractions": "6.2.1", "OpenIddict.AspNetCore": "6.2.1", "Swashbuckle.AspNetCore": "8.1.1", "Umbraco.Cms.Core": "16.0.0", "Umbraco.Cms.Web.Common": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Api.Common.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Api.Common.dll": {}}}, "Umbraco.Cms.Api.Delivery/16.0.0": {"dependencies": {"Umbraco.Cms.Api.Common": "16.0.0", "Umbraco.Cms.Web.Common": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Api.Delivery.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Api.Delivery.dll": {}}}, "Umbraco.Cms.Api.Management/16.0.0": {"dependencies": {"JsonPatch.Net": "3.3.0", "Swashbuckle.AspNetCore": "8.1.1", "Umbraco.Cms.Api.Common": "16.0.0", "Umbraco.Cms.Infrastructure": "16.0.0", "Umbraco.Cms.PublishedCache.HybridCache": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Api.Management.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Api.Management.dll": {}}}, "Umbraco.Cms.Core/16.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Embedded": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Identity.Core": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4", "Microsoft.Extensions.Options.DataAnnotations": "9.0.4"}, "runtime": {"lib/net9.0/Umbraco.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Core.dll": {}}}, "Umbraco.Cms.Examine.Lucene/16.0.0": {"dependencies": {"Examine": "3.7.1", "Lucene.Net.Replicator": "4.8.0-beta00017", "System.Security.Cryptography.Xml": "9.0.4", "Umbraco.Cms.Infrastructure": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Examine.Lucene.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Examine.Lucene.dll": {}}}, "Umbraco.Cms.Imaging.ImageSharp/16.0.0": {"dependencies": {"SixLabors.ImageSharp": "3.1.7", "SixLabors.ImageSharp.Web": "3.1.4", "Umbraco.Cms.Web.Common": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Imaging.ImageSharp.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Imaging.ImageSharp.dll": {}}}, "Umbraco.Cms.Infrastructure/16.0.0": {"dependencies": {"Examine.Core": "3.7.1", "HtmlAgilityPack": "1.12.1", "MailKit": "4.11.0", "Markdown": "2.2.1", "Microsoft.CodeAnalysis.CSharp": "4.13.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Identity.Stores": "9.0.4", "MiniProfiler.Shared": "4.5.4", "NPoco": "5.7.1", "OpenIddict.Abstractions": "6.2.1", "Serilog": "4.2.0", "Serilog.Enrichers.Process": "3.0.0", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Expressions": "5.0.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Formatting.Compact.Reader": "4.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Async": "2.1.0", "Serilog.Sinks.File": "6.0.0", "Serilog.Sinks.Map": "2.0.0", "System.Text.RegularExpressions": "4.3.1", "Umbraco.Cms.Core": "16.0.0", "NCrontab": "3.3.3"}, "runtime": {"lib/net9.0/Umbraco.Infrastructure.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Infrastructure.dll": {}}}, "Umbraco.Cms.Persistence.EFCore/16.0.0": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.4", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.4", "Microsoft.Extensions.Caching.Memory": "9.0.4", "OpenIddict.EntityFrameworkCore": "6.2.1", "System.Runtime.Caching": "9.0.4", "System.Text.Encodings.Web": "9.0.4", "Umbraco.Cms.Core": "16.0.0", "Umbraco.Cms.Infrastructure": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.dll": {}}}, "Umbraco.Cms.Persistence.EFCore.Sqlite/16.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": "9.0.4", "Umbraco.Cms.Persistence.EFCore": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.Sqlite.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.Sqlite.dll": {}}}, "Umbraco.Cms.Persistence.EFCore.SqlServer/16.0.0": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "System.Runtime.Caching": "9.0.4", "Umbraco.Cms.Persistence.EFCore": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.SqlServer.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Persistence.EFCore.SqlServer.dll": {}}}, "Umbraco.Cms.Persistence.Sqlite/16.0.0": {"dependencies": {"Microsoft.Data.Sqlite": "9.0.4", "Umbraco.Cms.Infrastructure": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Persistence.Sqlite.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Persistence.Sqlite.dll": {}}}, "Umbraco.Cms.Persistence.SqlServer/16.0.0": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Data.SqlClient": "6.0.1", "NPoco.SqlServer": "5.7.1", "System.Runtime.Caching": "9.0.4", "System.Text.Encodings.Web": "9.0.4", "Umbraco.Cms.Infrastructure": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.Persistence.SqlServer.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.Persistence.SqlServer.dll": {}}}, "Umbraco.Cms.PublishedCache.HybridCache/16.0.0": {"dependencies": {"K4os.Compression.LZ4": "1.3.8", "MessagePack": "3.1.3", "Microsoft.Extensions.Caching.Hybrid": "9.4.0", "Umbraco.Cms.Core": "16.0.0", "Umbraco.Cms.Infrastructure": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.PublishedCache.HybridCache.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.PublishedCache.HybridCache.dll": {}}}, "Umbraco.Cms.StaticAssets/16.0.0": {"dependencies": {"Umbraco.Cms.Api.Management": "16.0.0", "Umbraco.Cms.Web.Website": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Cms.StaticAssets.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Cms.StaticAssets.dll": {}}}, "Umbraco.Cms.Targets/16.0.0": {"dependencies": {"Umbraco.Cms.Api.Delivery": "16.0.0", "Umbraco.Cms.Api.Management": "16.0.0", "Umbraco.Cms.StaticAssets": "16.0.0"}}, "Umbraco.Cms.Web.Common/16.0.0": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Dazinator.Extensions.FileProviders": "2.0.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "8.8.0", "MiniProfiler.AspNetCore.Mvc": "4.5.4", "Serilog.AspNetCore": "9.0.0", "System.Net.Http": "4.3.4", "System.Text.Encodings.Web": "9.0.4", "System.Text.RegularExpressions": "4.3.1", "Umbraco.Cms.Examine.Lucene": "16.0.0", "Umbraco.Cms.PublishedCache.HybridCache": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Web.Common.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Web.Common.dll": {}}}, "Umbraco.Cms.Web.Website/16.0.0": {"dependencies": {"Umbraco.Cms.Web.Common": "16.0.0"}, "runtime": {"lib/net9.0/Umbraco.Web.Website.dll": {"assemblyVersion": "1*******", "fileVersion": "*********9"}}, "compile": {"lib/net9.0/Umbraco.Web.Website.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticAssets/*******": {"compile": {"Microsoft.AspNetCore.StaticAssets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/1*******": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives.Reference/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/*******": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext.Reference/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent.Reference/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.Reference/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console.Reference/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools.Reference/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression.Reference/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile.Reference/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http.Reference/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives.Reference/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets.Reference/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Reference/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors.Reference/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims.Reference/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal.Reference/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions.Reference/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting.Reference/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer.Reference/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter.Reference/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument.Reference/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"MDDPlus/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Asp.Versioning.Abstractions/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "path": "asp.versioning.abstractions/8.1.0", "hashPath": "asp.versioning.abstractions.8.1.0.nupkg.sha512"}, "Asp.Versioning.Http/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "path": "asp.versioning.http/8.1.0", "hashPath": "asp.versioning.http.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "path": "asp.versioning.mvc/8.1.0", "hashPath": "asp.versioning.mvc.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "path": "asp.versioning.mvc.apiexplorer/8.1.0", "hashPath": "asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "path": "azure.identity/1.13.2", "hashPath": "azure.identity.1.13.2.nupkg.sha512"}, "BouncyCastle.Cryptography/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "path": "bouncycastle.cryptography/2.5.1", "hashPath": "bouncycastle.cryptography.2.5.1.nupkg.sha512"}, "Dazinator.Extensions.FileProviders/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Jb10uIvdGdaaOmEGUXeO1ssjp6YuvOuR87B5gLxGORFbroV1j7PDaVfEIgni7vV8KRcyAY5KvuMxgx6ADIEXNw==", "path": "dazinator.extensions.fileproviders/2.0.0", "hashPath": "dazinator.extensions.fileproviders.2.0.0.nupkg.sha512"}, "DotNet.Glob/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i6x0hDsFWg6Ke2isaNAcHQ9ChxBvTJu2cSmBY+Jtjiv2W4q6y9QlA3JKYuZqJ573TAZmpAn65Qf3sRpjvZ1gmw==", "path": "dotnet.glob/3.1.0", "hashPath": "dotnet.glob.3.1.0.nupkg.sha512"}, "Examine/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-/Hq2jb+Bv2COlJszLhmsDIN9+8VZnwiaXA1RnzBSp24PfVR/GrY/WzlWNJSzjVt5yvYW7Fuq0V1Bfu9e/v1UIA==", "path": "examine/3.7.1", "hashPath": "examine.3.7.1.nupkg.sha512"}, "Examine.Core/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vsm5DWtCTZ5cSyYN4Ryy6wWTFM1Q3Nz/1eeWHf5vNWIall0XQySApNbIofDfDNqDPauanHCoulj7y00vkhNBiw==", "path": "examine.core/3.7.1", "hashPath": "examine.core.3.7.1.nupkg.sha512"}, "Examine.Lucene/3.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-pRpYAfSJ1DoNhq9gGy3EfSIGkv3BryVEMWvmvvYve5sFRtkK+bQbKIX4BvCbi2TR9ZzOo7mCsCzCj17JJ/CpeQ==", "path": "examine.lucene/3.7.1", "hashPath": "examine.lucene.3.7.1.nupkg.sha512"}, "HtmlAgilityPack/1.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-SP6/2Y26CXtxjXn0Wwsom9Ek35SNWKHEu/IWhNEFejBSSVWWXPRSlpqpBSYWv1SQhYFnwMO01xVbEdK3iRR4hg==", "path": "htmlagilitypack/1.12.1", "hashPath": "htmlagilitypack.1.12.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "J2N/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vooz1wbnnqWuS+u93tADXK5Owxo8vLJhSrZ9Ac+KpgDF3GJq9TybXXTF1TFcWILgEtRThc8AOBENEzB0TQH1JA==", "path": "j2n/2.1.0", "hashPath": "j2n.2.1.0.nupkg.sha512"}, "Json.More.Net/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg==", "path": "json.more.net/2.1.0", "hashPath": "json.more.net.2.1.0.nupkg.sha512"}, "JsonPatch.Net/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "path": "jsonpatch.net/3.3.0", "hashPath": "jsonpatch.net.3.3.0.nupkg.sha512"}, "JsonPointer.Net/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "path": "jsonpointer.net/5.2.0", "hashPath": "jsonpointer.net.5.2.0.nupkg.sha512"}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "path": "k4os.compression.lz4/1.3.8", "hashPath": "k4os.compression.lz4.1.3.8.nupkg.sha512"}, "Lucene.Net/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-7LLWS9nNwx01AyE/KXMh+qdAlzDkRANE8407AO/wEmLL1InzVKFwfsRdRmwg4ILOMFui4xZ1Y54eqvzo3Tf9Vw==", "path": "lucene.net/4.8.0-beta00017", "hashPath": "lucene.net.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Analysis.Common/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-rPpmww/HgwEwhvfvZgdWITxFsWRoCEpP3+WQBFgbGxTn4eLDr3U/oFoe8KS+8jUNAl2+5atErDrW5JOcFG+gcQ==", "path": "lucene.net.analysis.common/4.8.0-beta00017", "hashPath": "lucene.net.analysis.common.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Facet/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-LVxGwgRAVq9XdwvNfgCB8OH+ou40I0E1NYN53muPjQK5oUY+HpkgkFUhTFSHdajWWj7xFI1f+UFB23iweoVf2w==", "path": "lucene.net.facet/4.8.0-beta00017", "hashPath": "lucene.net.facet.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Grouping/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-nzMGvz0b1cedS8KKOlglJQJpyz8fT0ojgXFkgSkLLhwPNbMPwVoBsR7RlZs1FrF60Oz369O3Pm1a+MIr52KcLQ==", "path": "lucene.net.grouping/4.8.0-beta00017", "hashPath": "lucene.net.grouping.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Join/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-WcJl4O6t3iXiXwXHnhmbVCO7C6ilPxabBCsdW/auQN0lrDpbVIcHorCxwd199fGBEQnk7wbl5pPnk8nw/VK4eQ==", "path": "lucene.net.join/4.8.0-beta00017", "hashPath": "lucene.net.join.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Queries/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-RVpZCfa/7pgvytFw64zLqinvZPQt4TojvcFghdAA5vhnpSs5GTbtciPIxFH3wwH3f2dYJywiqYKo1h3JBCXRBA==", "path": "lucene.net.queries/4.8.0-beta00017", "hashPath": "lucene.net.queries.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.QueryParser/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-ZrF7EL06qB+2S2K4T3PliIa5EiJ5Ii7c/zFRMhsNozymz+HRHMVoI/nMYSdN6WF7X1Ef1DTeajMwvsbGTfl28Q==", "path": "lucene.net.queryparser/4.8.0-beta00017", "hashPath": "lucene.net.queryparser.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Replicator/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-YGZcKkQhuLweZ+M4UgA/Uok3Vl3HOTlvZpUmTZMS4J9cBdvTevG0e6rn/pZrfONUpp0TtbXe494oGA1rScouOA==", "path": "lucene.net.replicator/4.8.0-beta00017", "hashPath": "lucene.net.replicator.4.8.0-beta00017.nupkg.sha512"}, "Lucene.Net.Sandbox/4.8.0-beta00017": {"type": "package", "serviceable": true, "sha512": "sha512-wRAzQZ4Z1yEuAaTwO+RrZB6l3Lz+vNGAiDshf0IjAr8qeVvQj74iodEcff4Bes88bnhqsWLUZlDUg/ygraxX2Q==", "path": "lucene.net.sandbox/4.8.0-beta00017", "hashPath": "lucene.net.sandbox.4.8.0-beta00017.nupkg.sha512"}, "MailKit/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-JVoRxJ+QRqFMRtEM4veStj3pMLBPRulQGV+iZm6Tq1pnr66Dy6dFYOW9Uw02nxAVzdZAN8G+y3BsUPtgZcKXhA==", "path": "mailkit/4.11.0", "hashPath": "mailkit.4.11.0.nupkg.sha512"}, "Markdown/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-A6veXuFP1n50RbmFNtTgfHxnHmwMsgFLSCgS1xWbg5L8n5N6HFEksTlXocZ0LsmGW4leBzeLJd+BY7+g83zFJA==", "path": "markdown/2.2.1", "hashPath": "markdown.2.2.1.nupkg.sha512"}, "MessagePack/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-UiNv3fknvPzh5W+S0VV96R17RBZQQU71qgmsMnjjRZU2rtQM/XcTnOB+klT2dA6T1mxjnNKYrEm164AoXvGmYg==", "path": "messagepack/3.1.3", "hashPath": "messagepack.3.1.3.nupkg.sha512"}, "MessagePack.Annotations/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-XTy4njgTAf6UVBKFj7c7ad5R0WVKbvAgkbYZy4f00kplzX2T3VOQ34AUke/Vn/QgQZ7ETdd34/IDWS3KBInSGA==", "path": "messagepack.annotations/3.1.3", "hashPath": "messagepack.annotations.3.1.3.nupkg.sha512"}, "MessagePackAnalyzer/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-19u1oVNv2brCs5F/jma8O8CnsKMMpYwNqD0CAEDEzvqwDTAhqC9r7xHZP4stPb3APs/ryO/zVn7LvjoEHfvs7Q==", "path": "messagepackanalyzer/3.1.3", "hashPath": "messagepackanalyzer.3.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-E4pHyEb2Ul5a6bIwraGtw9TN39a/C2asyVPEJoyItc0reV4Y26FsPcEdcXyKjBbP4kSz9iU1Cz4Yhx/aOFPpqA==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.4", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-5v9Kj2arRrCftLKW80Hfj31HkNnjcKyw57lQhF84drvGxJlCR63J0zMM1sMM+Hc+KCQjuoDmHtjwN0uOT+X3ag==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.4", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-p6mlJTLfEoWyg4atIzdNpI48f/Bn8mpGqs5AW7TaqkQdxbVekovUj1BrLcuUoysyODVP3C9Db6J1y3RD6kD4pQ==", "path": "microsoft.aspnetcore.dataprotection/8.0.4", "hashPath": "microsoft.aspnetcore.dataprotection.8.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-iqEPvlPGn9WJl5d+gWRG+ASap3cRDmNTQG4Ozep7YZKr+fOTm6tbcIazNZtUlRIlTTxY9Rr0cwNXTmPJkxJnlw==", "path": "microsoft.aspnetcore.dataprotection.abstractions/8.0.4", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.8.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-CSVd9h1TdWDT2lt62C4FcgaF285J4O3MaOqTVvc7xP+3bFiwXcdp6qEd+u1CQrdJ+xJuslR+tvDW7vWQ/OH5Qw==", "path": "microsoft.aspnetcore.hosting.abstractions/1.0.2", "hashPath": "microsoft.aspnetcore.hosting.abstractions.1.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6ZtFh0huTlrUl72u9Vic0icCVIQiEx7ULFDx3P7BpOI97wjb0GAXf8B4m9uSpSGf0vqLEKFlkPbvXF0MXXEzhw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/1.0.2", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.1.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-peJqc7BgYwhTzOIfFHX3/esV6iOXf17Afekh6mCYuUD3aWyaBwQuWYaKLR+RnjBEWaSzpCDgfCMMp5Y3LUXsiA==", "path": "microsoft.aspnetcore.http.abstractions/1.0.2", "hashPath": "microsoft.aspnetcore.http.abstractions.1.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9l/Y/CO3q8tET3w+dDiByREH8lRtpd14cMevwMV5nw2a/avJ5qcE3VVIE5U5hesec2phTT6udQEgwjHmdRRbig==", "path": "microsoft.aspnetcore.http.features/1.0.2", "hashPath": "microsoft.aspnetcore.http.features.1.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ppzthXSIAV5Z3tqayYUPqKZFbM+kHxiNflFm5O3392cXRn7b+S513EF+tLgU/kpaCF1OwprSe5b2iQOxLr7qvA==", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/9.0.4", "hashPath": "microsoft.aspnetcore.mvc.razor.runtimecompilation.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "path": "microsoft.aspnetcore.razor.language/6.0.0", "hashPath": "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tjfuEv+QOznFL1bEPa7svmjpbNvDIrwdinMNy/HhrToQQpONW4hdp0Sans55Rcy9KB3z60duBeey89JY1VQOvg==", "path": "microsoft.bcl.cryptography/9.0.0", "hashPath": "microsoft.bcl.cryptography.9.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/EW3UE8/lbEYHoC2Qq7AR/DnmvpgdtAMndfQNmpuIMx/Mto8L5JnuCfdBYtgvalQOtfNCnxFejxuRrryvUTsg==", "path": "microsoft.codeanalysis.analyzers/3.11.0", "hashPath": "microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-T8nRl4mAUY4mhdYM4U2ra2vP2EL+ol8Yqwo0gwC/V55vmlXq9NxdIkZJynTpTL1uX/jHijJ90AeOEx4lf7OwzQ==", "path": "microsoft.codeanalysis.common/4.13.0", "hashPath": "microsoft.codeanalysis.common.4.13.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-BsH7Vijbj9IL7Fj4k/ysZSVyLGFqr75wmdFGwCKWJvSjnA1xwPaQ3hkB2BQdHOt5CpEYA6Q0I6Oo5sDTDHqHsg==", "path": "microsoft.codeanalysis.csharp/4.13.0", "hashPath": "microsoft.codeanalysis.csharp.4.13.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "path": "microsoft.codeanalysis.razor/6.0.0", "hashPath": "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "path": "microsoft.data.sqlclient/6.0.1", "hashPath": "microsoft.data.sqlclient.6.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-jD3xtvMnMRn2uQl/rMCh01Q4b4P0l6Y5cUJfjTL/MOhkCy/4iDa4tL6y/gHaSKJilO9SdkOEJ/v4Z2Z59/jgLQ==", "path": "microsoft.data.sqlite/9.0.4", "hashPath": "microsoft.data.sqlite.9.0.4.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-rnVGier1R0w9YEAzxOlUl8koFwq4QLwuYKiJN6NFqbCNCPrRLGW3f7x0OtL/Sp1KBMVhgffUIP6jWPppzhpa2Q==", "path": "microsoft.data.sqlite.core/9.0.4", "hashPath": "microsoft.data.sqlite.core.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-+5IAX0aicQYCRfN4pAjad+JPwdEYoVEM3Z1Cl8/EiEv3FVHQHdd8TJQpQIslQDDQS/UsUMb0MsOXwqOh+TJtRw==", "path": "microsoft.entityframeworkcore/9.0.4", "hashPath": "microsoft.entityframeworkcore.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-E0pkWzI0liqu2ogqJ1kohk2eGkYRhf5tI75HGF6IQDARsshY/0w+prGyLvNuUeV7B8I7vYQZ4CzAKYKxw7b9gQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.4", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cMsm1O7g9X5qbB2wjHf3BVVvGwkG+zeXQ+M91I1Bm6RfylFMImqBPzs0+vmuef7fPxr2yOzPhIfJ2wQJfmtaSw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.4", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-OjJ+xh/wQff5b0wiC3SPvoQqTA2boZeJQf+15+3+OJPtjBKzvxuwr25QRIu1p1t+K8ryQ8pzaoZ7eOpXfNzVGA==", "path": "microsoft.entityframeworkcore.relational/9.0.4", "hashPath": "microsoft.entityframeworkcore.relational.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YruNASPuiCjLOVxO09lpQT4e2OYvpsoD0e5NGEQKOcPCu143RDzWTNlpzcxhArBgAS0FPwQ+OEGZOWhwgWHvOA==", "path": "microsoft.entityframeworkcore.sqlite/9.0.4", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-96NFbmjcZsO9HkSdWAwh5tn/7LKIu7cLW+zubyGV1BR1w8xpcqPXZcTW4S/0eA0d9BxyFnH8tSDRjUerWGoU/Q==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.4", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UCo6rRUIx2Rhl6xVkMPf1yL/97jcYkwrryOKB5e68YCZ7NdQyk+7wfXJzEDvkFcjTw45H5sy4/1vW6vXCs/Kag==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.4", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GMCX3zybUB22aAADjYPXrWhhd1HNMkcY5EcFAJnXy/4k5pPpJ6TS4VRl37xfrtosNyzbpO2SI7pd2Q5PvggSdg==", "path": "microsoft.extensions.ambientmetadata.application/9.2.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-imcZ5BGhBw5mNsWLepBbqqumWaFe0GtvyCvne2/2wsDIBRa2+Lhx4cU/pKt/4BwOizzUEOls2k1eOJQXHGMalg==", "path": "microsoft.extensions.caching.abstractions/9.0.4", "hashPath": "microsoft.extensions.caching.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Hybrid/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-GtHP+DRraRM6RLq7TzUV8Iiyqm+WemJRLDNqy7uvA+Dgf6fjvxpmHnzgPb+RAcRNjADz961DMHHZ4i5EQjpDPw==", "path": "microsoft.extensions.caching.hybrid/9.4.0", "hashPath": "microsoft.extensions.caching.hybrid.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-G5rEq1Qez5VJDTEyRsRUnewAspKjaY57VGsdZ8g8Ja6sXXzoiI3PpTd1t43HjHqNWD5A06MQveb2lscn+2CU+w==", "path": "microsoft.extensions.caching.memory/9.0.4", "hashPath": "microsoft.extensions.caching.memory.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Te+N4xphDlGIS90lKJMZyezFiMWKLAtYV2/M8gGJG4thH6xyC7LWhMzgz2+tWMehxwZlBUq2D9DvVpjKBZFTPQ==", "path": "microsoft.extensions.compliance.abstractions/9.2.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "path": "microsoft.extensions.configuration/9.0.4", "hashPath": "microsoft.extensions.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "path": "microsoft.extensions.configuration.binder/9.0.4", "hashPath": "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "path": "microsoft.extensions.configuration.json/9.0.4", "hashPath": "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "path": "microsoft.extensions.dependencyinjection/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-WcwfTpl3IcPcaahTVEaJwMUg1eWog1SkIA6jQZZFqMXiMX9/tVkhNB6yzUQmBdGWdlWDDRKpOmK7T7x1Uu05pQ==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.2.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ACtnvl3H3M/f8Z42980JxsNu7V9PPbzys4vBs83ZewnsgKd7JeYK18OMPo0g+MxAHrpgMrjmlinXDiaSRPcVnA==", "path": "microsoft.extensions.dependencymodel/9.0.4", "hashPath": "microsoft.extensions.dependencymodel.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "path": "microsoft.extensions.diagnostics/9.0.4", "hashPath": "microsoft.extensions.diagnostics.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.4", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-et5JevHsLv1w1O1Zhb6LiUfai/nmDRzIHnbrZJdzLsIbbMCKTZpeHuANYIppAD//n12KvgOne05j4cu0GhG9gw==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.2.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fictUnSF95D+M9iH4X6TYBjud2gbB2r6bcIi0sQknXFc2bHbNucoaK+SzfLCzb47tHSR9a5pm0F1Ioj0PgmFeQ==", "path": "microsoft.extensions.fileproviders.embedded/9.0.4", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bXkwRPMo4x19YKH6/V9XotU7KYQJlihXhcWO1RDclAY3yfY3XNg4QtSEBvng4kK/DnboE0O/nwSl+6Jiv9P+FA==", "path": "microsoft.extensions.hosting.abstractions/9.0.4", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "path": "microsoft.extensions.http/9.0.4", "hashPath": "microsoft.extensions.http.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Eeup1LuD5hVk5SsKAuX1D7I9sF380MjrNG10IaaauRLOmrRg8rq2TA8PYTXVBXf3MLkZ6m2xpBqRbZdxf8ygkg==", "path": "microsoft.extensions.http.diagnostics/9.2.0", "hashPath": "microsoft.extensions.http.diagnostics.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-2y5a9Iijc9iTUN1M7rH2+kUMJPuuxTgfUyL9iAOqe4ueuWtTfG1SVX/oAj35q46OV4kSgCeJC82dLQ96xOo/RQ==", "path": "microsoft.extensions.http.polly/9.0.2", "hashPath": "microsoft.extensions.http.polly.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Km+YyCuk1IaeOsAzPDygtgsUOh3Fi89hpA18si0tFJmpSBf9aKzP9ffV5j7YOoVDvRWirpumXAPQzk1inBsvKw==", "path": "microsoft.extensions.http.resilience/9.2.0", "hashPath": "microsoft.extensions.http.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KKfCsoIHFGZmmCEjZBPuvDW0pCjboMru/Z3vbEyC/OIwUVeKrdPugFyjc81i7rNSjcPcDxVvGl/Ks8HLelKocg==", "path": "microsoft.extensions.identity.core/9.0.4", "hashPath": "microsoft.extensions.identity.core.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0F6lSngwyXzrv+qtX46nhHYBOlPxEzj0qyCCef1kvlyEYhbj8kBL13FuDk4nEPkzk1yVjZgsnXBG19+TrNdakQ==", "path": "microsoft.extensions.identity.stores/9.0.4", "hashPath": "microsoft.extensions.identity.stores.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "path": "microsoft.extensions.logging/9.0.4", "hashPath": "microsoft.extensions.logging.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pnwYZE7U6d3Y6iMVqADOAUUMMBGYAQPsT3fMwVr/V1Wdpe5DuVGFcViZavUthSJ5724NmelIl1cYy+kRfKfRPQ==", "path": "microsoft.extensions.logging.configuration/9.0.2", "hashPath": "microsoft.extensions.logging.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nWx7uY6lfkmtpyC2dGc0IxtrZZs/LnLCQHw3YYQucbqWj8a27U/dZ+eh72O3ZiolqLzzLkVzoC+w/M8dZwxRTw==", "path": "microsoft.extensions.objectpool/9.0.2", "hashPath": "microsoft.extensions.objectpool.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "path": "microsoft.extensions.options/9.0.4", "hashPath": "microsoft.extensions.options.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "path": "microsoft.extensions.options.configurationextensions/9.0.4", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options.DataAnnotations/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-jJq7xO1PLi//cts59Yp6dKNN07xV0Day/JmVR7aXCdo2rYHAoFlyARyxrfB0CTzsErA+TOhYTz2Ee0poR8SPeQ==", "path": "microsoft.extensions.options.dataannotations/9.0.4", "hashPath": "microsoft.extensions.options.dataannotations.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-dyaM+Jeznh/i21bOrrRs3xceFfn0571EOjOq95dRXmL1rHDLC4ExhACJ2xipRBP6g1AgRNqmryi+hMrVWWgmlg==", "path": "microsoft.extensions.resilience/9.2.0", "hashPath": "microsoft.extensions.resilience.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4+bw7W4RrAMrND9TxonnSmzJOdXiPxljoda8OPJiReIN607mKCc0t0Mf28sHNsTujO1XQw28wsI0poxeeQxohw==", "path": "microsoft.extensions.telemetry/9.2.0", "hashPath": "microsoft.extensions.telemetry.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kEl+5G3RqS20XaEhHh/nOugcjKEK+rgVtMJra1iuwNzdzQXElelf3vu8TugcT7rIZ/T4T76EKW1OX/fmlxz4hw==", "path": "microsoft.extensions.telemetry.abstractions/9.2.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime/********": {"type": "package", "serviceable": true, "sha512": "sha512-Z42uzvs0TN9Y02xgHtRgPcumLRnvK3MHVHZ0Pr3OrnvyZYhBwqDgdYBOvoELcTsayUgwqrPLb+C5Fqqk66zlUg==", "path": "microsoft.icu.icu4c.runtime/********", "hashPath": "microsoft.icu.icu4c.runtime.********.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime.linux-arm64/********": {"type": "package", "serviceable": true, "sha512": "sha512-u/2cPX6JBgSgTOeDjkb2A672LsL3zQo60ViYUTqHOrxuFOIx0ag6bFu2WgN4zRZ71K6L0fubnrlS1HpN+k5kyA==", "path": "microsoft.icu.icu4c.runtime.linux-arm64/********", "hashPath": "microsoft.icu.icu4c.runtime.linux-arm64.********.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime.linux-x64/********": {"type": "package", "serviceable": true, "sha512": "sha512-q1iHc4EGCBYbpb+gfMZGn6L/WuBei/la52pRbxlVy4ed7FdB9UmvUXhoRzv6OsYa6E4VlTlj6EKgYvrwPkVGKQ==", "path": "microsoft.icu.icu4c.runtime.linux-x64/********", "hashPath": "microsoft.icu.icu4c.runtime.linux-x64.********.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime.win-arm64/********": {"type": "package", "serviceable": true, "sha512": "sha512-/h8OPK1fqrI9t8hKNmpnSy7MYssGB1CtoXANsduFqf0Sc+OOtfoCIvRp2Mt9Fk80CmtU/53TldGvt1oCH7KpEA==", "path": "microsoft.icu.icu4c.runtime.win-arm64/********", "hashPath": "microsoft.icu.icu4c.runtime.win-arm64.********.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime.win-x64/********": {"type": "package", "serviceable": true, "sha512": "sha512-7j6NsmvKuVxgoFsoy0Ty7I09V/tvrQBZN+ddfHtz/OWNRaEIy7PsAguGoyD4AcQZh/KkfT9RQlHoQJ4xVQPr6g==", "path": "microsoft.icu.icu4c.runtime.win-x64/********", "hashPath": "microsoft.icu.icu4c.runtime.win-x64.********.nupkg.sha512"}, "Microsoft.ICU.ICU4C.Runtime.win-x86/********": {"type": "package", "serviceable": true, "sha512": "sha512-xTHoHJKtgHDsYkQ/RU3o4U36ktjQqnR+ML00HDDK2SWr+9nMekxnXvtLZ2I4cqF8s51frxqTRgx1jDVtIzCf3w==", "path": "microsoft.icu.icu4c.runtime.win-x86/********", "hashPath": "microsoft.icu.icu4c.runtime.win-x86.********.nupkg.sha512"}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "path": "microsoft.identity.client/4.67.2", "hashPath": "microsoft.identity.client.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "path": "microsoft.identity.client.extensions.msal/4.67.2", "hashPath": "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-0lKw+f3vkmV9t3PLe6sY3xPrYrHYiMRFxuOse5CMkKPxhQYiabpfJsuk6wX2RrVQ86Dn+t/8poHpH0nbp6sFvA==", "path": "microsoft.identitymodel.abstractions/8.8.0", "hashPath": "microsoft.identitymodel.abstractions.8.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-lepOkZZTMfJCPSnWITXxV+4Wxb54g+9oIybs9YovlOzZWuR1i2DOpzaDgSe+piDJaGtnSrcUlcB9fZ5Swur7Uw==", "path": "microsoft.identitymodel.jsonwebtokens/8.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-sUyoxzg/WBZobbFLJK8loT9IILKtS9ePmWu5B11ogQqhSHppE6SRZKw0fhI6Fd16X6ey52cbbWc2rvMBC98EQA==", "path": "microsoft.identitymodel.logging/8.8.0", "hashPath": "microsoft.identitymodel.logging.8.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cDvZZ8JYto6L1I64sWW17JGwYGuFZ5Qm+WZG+wLk0QHjtuSosujDVAc4nr/sx6+n88q1mdW93rGEl7TCniNp5Q==", "path": "microsoft.identitymodel.protocols/8.4.0", "hashPath": "microsoft.identitymodel.protocols.8.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "path": "microsoft.identitymodel.protocols.openidconnect/7.5.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-09hnbUJh/18gUmu5nCVFMvyzAFC4l1qyc4bwSJaKzUBqHN7aNDwmSx8dE3/MMJImbvnKq9rEtkkgnrS/OUBtjA==", "path": "microsoft.identitymodel.tokens/8.8.0", "hashPath": "microsoft.identitymodel.tokens.8.8.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DJHjP1cTSu+sMt0vdNFLH9/wLHkq3EZk6BPZKI/R9anry41Ng/TOOZM0iXo+stXlY3LivTW/70Y26oMVGgTsTg==", "path": "microsoft.net.http.headers/9.0.2", "hashPath": "microsoft.net.http.headers.9.0.2.nupkg.sha512"}, "Microsoft.NET.StringTools/17.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-mudqUHhNpeqIdJoUx2YDWZO/I9uEDYVowan89R6wsomfnUJQk6HteoQTlNjZDixhT2B4IXMkMtgZtoceIjLRmA==", "path": "microsoft.net.stringtools/17.11.4", "hashPath": "microsoft.net.stringtools.17.11.4.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "MimeKit/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-6p0RC1qwBGBHxf7hvzuR1GngzigF+Q6HQUTbD2RbmDrnS2m1qO2rgqOhYtn8n8JH7WGZ+7RthS8lfMuMzeg8AA==", "path": "mimekit/4.11.0", "hashPath": "mimekit.4.11.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-meedJsjpYOeHPhE8H6t+dGQ9zLxcCQVpi4DXzmxmYAXywmTzlo6jv2IASUv5QijTU0CxsROln3FHd8RsTO8Z8A==", "path": "miniprofiler.aspnetcore/4.5.4", "hashPath": "miniprofiler.aspnetcore.4.5.4.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-+NqXyCy9aNdroPm6leW5+cpngtCnkCdoyOlJzvVN62uucSx+MYkx8jmKbgAt+aCP6aghADfHBExwrTIldHxapg==", "path": "miniprofiler.aspnetcore.mvc/4.5.4", "hashPath": "miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512"}, "MiniProfiler.Shared/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-f8ckFm/xTS8C2Bn4BdVc94dNvg+tRfk0e4XFaETOqRi6r0PUOyn3Z9jTQCVpB3R1pP5WiRsEIrqqxux95BVpTA==", "path": "miniprofiler.shared/4.5.4", "hashPath": "miniprofiler.shared.4.5.4.nupkg.sha512"}, "NCrontab/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-2yzZXZLI0YpxrNgWnW/4xoo7ErLgWJIwTljRVEJ3hyjc7Kw9eGdjbFZGP1AhBuTUEZQ443PgZifG1yox6Qo1/A==", "path": "ncrontab/3.3.3", "hashPath": "ncrontab.3.3.3.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NPoco/5.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6qjyBqqc0TSK/xHjXA6tSZhABSDQqXGrTOIdUIVazPsmN0OyTaBTEtwV2wTV0NyfkzcRPhLyO6bIW89ZFNvlWg==", "path": "npoco/5.7.1", "hashPath": "npoco.5.7.1.nupkg.sha512"}, "NPoco.SqlServer/5.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-39esICE6E8oMQF3E2PgimW7EpjNyuRJgPZDzzYFPjtBoSw8TUfAVRNkSiQ9LND812Yf7vCX9DCIOi/roOtrxHA==", "path": "npoco.sqlserver/5.7.1", "hashPath": "npoco.sqlserver.5.7.1.nupkg.sha512"}, "OpenIddict/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZOFIUA4JgCp9AsYp0POqQopTWQgM02THsXAPHXa2vCm85m9t6etYHY4Ssy7nZWcORuTNipxEHmpUrU5HjhFxbw==", "path": "openiddict/6.2.1", "hashPath": "openiddict.6.2.1.nupkg.sha512"}, "OpenIddict.Abstractions/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-YW6djPWEZkTEAujiF0jCDO+ZN09WxCXmCD5qyJS49OgW/Rp5qtFAx0vtCjpz1R/YYGd4GlEBpqM+P2EjhW/i7w==", "path": "openiddict.abstractions/6.2.1", "hashPath": "openiddict.abstractions.6.2.1.nupkg.sha512"}, "OpenIddict.AspNetCore/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-aTDOfXq1ekKxWNZi8hQ/AcOHysM1O4hhXLBnY6UsmSCYvdz0RULXQm06i4U/RsWxXk1Ghiw172l3vH/MDR1nCQ==", "path": "openiddict.aspnetcore/6.2.1", "hashPath": "openiddict.aspnetcore.6.2.1.nupkg.sha512"}, "OpenIddict.Client/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-28wBUl4Df3doBvzgRPPG0fJCm7uCTrfn0y/mwFclxWTHi+iRY6BoPwOXtROlm3A+2X/K3giLasUhfASgBilSbQ==", "path": "openiddict.client/6.2.1", "hashPath": "openiddict.client.6.2.1.nupkg.sha512"}, "OpenIddict.Client.AspNetCore/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-qD/I0+6HAVJLMwvwZOesoNWjsj9GFTTPs/K5LOEVPFQ41vw7faS8BiaYebIz2xdSChxjtAIouQiCr7guYUcmCg==", "path": "openiddict.client.aspnetcore/6.2.1", "hashPath": "openiddict.client.aspnetcore.6.2.1.nupkg.sha512"}, "OpenIddict.Client.DataProtection/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-mdhyO164k2LkbVWWxm7yJZNir5fLiTWJBse3gH+y2Aeri154FV9jJc4XdzgSpWmJaCy8z7WyW70c4lmIs3FeLw==", "path": "openiddict.client.dataprotection/6.2.1", "hashPath": "openiddict.client.dataprotection.6.2.1.nupkg.sha512"}, "OpenIddict.Client.SystemIntegration/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-8xXFFLF8g+kXkGAZm2uDRFIIetjvoI47d7+tfxfpMpTn3hXWcJaLj/u77MLw4M4ovhbj51WRE4k5XpuBglYm1g==", "path": "openiddict.client.systemintegration/6.2.1", "hashPath": "openiddict.client.systemintegration.6.2.1.nupkg.sha512"}, "OpenIddict.Client.SystemNetHttp/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-QImC6ac+H4ynPuSO80Pnc6ScM5Ei7JUiT71ZidHbiZ2IO3550adHnKJQmEq5Rx8sw9neCp97FUwTf4s7LMHQpw==", "path": "openiddict.client.systemnethttp/6.2.1", "hashPath": "openiddict.client.systemnethttp.6.2.1.nupkg.sha512"}, "OpenIddict.Client.WebIntegration/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-7cK5tISMHA9gIBN0mZ0BEOskPT70PVicbfsL5jQ+gw23z8sIfeZENujpCTv45qaosFO/M0bzpJMTb4+EybD4kw==", "path": "openiddict.client.webintegration/6.2.1", "hashPath": "openiddict.client.webintegration.6.2.1.nupkg.sha512"}, "OpenIddict.Core/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-hSV50l8aaKVbA3KDq4DIcJbg6QHLkwu0NN0lTNFjbk6gHTZOXMlHv1dQDZj/CCJb07bCLygQV6zFqRbbskzjxA==", "path": "openiddict.core/6.2.1", "hashPath": "openiddict.core.6.2.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-ntrt8k1Fnd6TBRgahu3Ce5EbHkhhi++g6EEh3IGfTs3toxTQu1U7L40uQ5Ym8Xm1JlhQgUc616SrWGidwTzJZA==", "path": "openiddict.entityframeworkcore/6.2.1", "hashPath": "openiddict.entityframeworkcore.6.2.1.nupkg.sha512"}, "OpenIddict.EntityFrameworkCore.Models/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUUbEtuka8lKcaNcVUHC8EfEVkW7Lkf42zWGDB/ysRaMYcBQwBcxibECv8nPosyX1R0ymDLgxTxozgGp2dakeg==", "path": "openiddict.entityframeworkcore.models/6.2.1", "hashPath": "openiddict.entityframeworkcore.models.6.2.1.nupkg.sha512"}, "OpenIddict.Server/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-jeflLF1HO+nj9BAneGDEgOXBYQ/Wn4BHIsI48rwY/z3frSLT2x+i78ZnSIPR1P7zsYk5TY8Fb4MDHVPWfn+4FQ==", "path": "openiddict.server/6.2.1", "hashPath": "openiddict.server.6.2.1.nupkg.sha512"}, "OpenIddict.Server.AspNetCore/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-SwOQtruLKPWdJCBLSPF/tYx/xYz4f6xCOGLbo9hMoj87jxWGmbsS+lBi4X9tI7dnNcNCDxuLFk/kspyrRWrKrA==", "path": "openiddict.server.aspnetcore/6.2.1", "hashPath": "openiddict.server.aspnetcore.6.2.1.nupkg.sha512"}, "OpenIddict.Server.DataProtection/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-AqQjVAhtki+9Y/4HTOV4mN/PR+wqua3IzBbLEv85/RvO/RUyuMXqO/4rmIPDxjsYWTFEO8vBzRCdlP1n5tbuOQ==", "path": "openiddict.server.dataprotection/6.2.1", "hashPath": "openiddict.server.dataprotection.6.2.1.nupkg.sha512"}, "OpenIddict.Validation/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-7xbh9kxqLnur1FMUHgrkKytT+k3O+A50f3mHsMesTm1gJyZdpLbdd+QyMCICOfdrjSc3i47V+Sl/AEpb/AFm3w==", "path": "openiddict.validation/6.2.1", "hashPath": "openiddict.validation.6.2.1.nupkg.sha512"}, "OpenIddict.Validation.AspNetCore/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xpC7a1f9itmGS6J+0k3x125wLqr8bI1lKNEKWge6AzeOm62GkcXYiSOG5mXiyVlcy4Q1BKO4rQZ+o2yjh5NTsA==", "path": "openiddict.validation.aspnetcore/6.2.1", "hashPath": "openiddict.validation.aspnetcore.6.2.1.nupkg.sha512"}, "OpenIddict.Validation.DataProtection/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-4KK/TbovoiKN950wJM3BaW5bbzM4yyt2Et3Leh1z6KB6rysaBIZDkoNYJDchg+6u9UbbKefaaGrYEkTF5C5OsQ==", "path": "openiddict.validation.dataprotection/6.2.1", "hashPath": "openiddict.validation.dataprotection.6.2.1.nupkg.sha512"}, "OpenIddict.Validation.ServerIntegration/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-cYizHa8Num8vGA+tRTOiIn32M2/XXZp9tGoIw54K5iUxYjIsmhdLhE+zzgZyPWOlEjQpM9BNr4IUxBI6N3vjPQ==", "path": "openiddict.validation.serverintegration/6.2.1", "hashPath": "openiddict.validation.serverintegration.6.2.1.nupkg.sha512"}, "OpenIddict.Validation.SystemNetHttp/6.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-tA4eg79H53w2jy09KqfU4kh3vlbdtR011vdDRUiP2VyGB450kVexiwAvjRwQaB1rjOAR23rXI7rvl0lhZDIU3w==", "path": "openiddict.validation.systemnethttp/6.2.1", "hashPath": "openiddict.validation.systemnethttp.6.2.1.nupkg.sha512"}, "Polly/7.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "path": "polly/7.2.4", "hashPath": "polly.7.2.4.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Enrichers.Process/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/wPYz2PDCJGSHNI+Z0PAacZvrgZgrGduWqLXeC2wvW6pgGM/Bi45JrKy887MRcRPHIZVU0LAlkmJ7TkByC0boQ==", "path": "serilog.enrichers.process/3.0.0", "hashPath": "serilog.enrichers.process.3.0.0.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Expressions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhZjXtUcA2QfQRA60m+DfyIfidKsQV7HBstbYEDqzJKMbJH/KnKthkkjciRuYrmFE+scWv1JibC5LlXrdtOUmw==", "path": "serilog.expressions/5.0.0", "hashPath": "serilog.expressions.5.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact.Reader/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E1gvPAx0AsQhlyzGwgcVnGe5QrdkSugwKh+6V/FUSdTMVKKPSiO6Ff5iosjBMNBvq244Zys7BhTfFmgCE0KUyQ==", "path": "serilog.formatting.compact.reader/4.0.0", "hashPath": "serilog.formatting.compact.reader.4.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Async/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SnmRknWsSMgyo9wDXeZZCqSp48kkQYy44taSM6vcpxfiRICzSf09oLKEmVr0RCwQnfd8mJQ2WNN6nvhqf0RowQ==", "path": "serilog.sinks.async/2.1.0", "hashPath": "serilog.sinks.async.2.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Map/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bpfOs8W9r5AwZ65/7IHGDI8eBdd8FgUbLd8aCmaMNN4ZSkcHfXGUnPL+PO/wpGJzw/XQNMLx8tro5H7xf2uL1A==", "path": "serilog.sinks.map/2.0.0", "hashPath": "serilog.sinks.map.2.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "path": "sixlabors.imagesharp/3.1.7", "hashPath": "sixlabors.imagesharp.3.1.7.nupkg.sha512"}, "SixLabors.ImageSharp.Web/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-wuH8nGNUNx6s2T8+YFpZg25yTEcmN2Zof24dLWO+TADEai0rKJrob7W/ai/l07095a381PxzbdvYUslmAEdonw==", "path": "sixlabors.imagesharp.web/3.1.4", "hashPath": "sixlabors.imagesharp.web.3.1.4.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "Swashbuckle.AspNetCore/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HJHexmU0PiYevgTLvKjYkxEtclF2w4O7iTd3Ef3p6KeT0kcYLpkFVgCw6glpGS57h8769anv8G+NFi9Kge+/yw==", "path": "swashbuckle.aspnetcore/8.1.1", "hashPath": "swashbuckle.aspnetcore.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+8D5jQtnl6X4f2hJQwf0Khj0SnCQANzirCELjXJ6quJ4C1aNNCvJrAsQ+4fOKAMqJkvW48cKj79ftG+YoGcRg==", "path": "swashbuckle.aspnetcore.swagger/8.1.1", "hashPath": "swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2EuPzXSNleOOzYvziERWRLnk1Oz9i0Z1PimaUFy1SasBqeV/rG+eMfwFAMtTaf4W6gvVOzRcUCNRHvpBIIzr+A==", "path": "swashbuckle.aspnetcore.swaggergen/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GDLX/MpK4oa2nYC1N/zN2UidQTtVKLPF6gkdEmGb0RITEwpJG9Gu8olKqPYnKqVeFn44JZoCS0M2LGRKXP8B/A==", "path": "swashbuckle.aspnetcore.swaggerui/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.ComponentModel/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-oBZFnm7seFiVfugsIyOvQCWobNZs7FzqDV/B7tx20Ep/l3UUFCPDkdTnCNaJZTU27zjeODmy2C/cP60u3D4c9w==", "path": "system.componentmodel/4.0.1", "hashPath": "system.componentmodel.4.0.1.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "path": "system.configuration.configurationmanager/9.0.4", "hashPath": "system.configuration.configurationmanager.9.0.4.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "path": "system.diagnostics.eventlog/9.0.4", "hashPath": "system.diagnostics.eventlog.9.0.4.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-WklXbMuiSM9X7UyM6t9UzNnMGGO9RV3OTtLjR++mvR4fcrMnuPPH3ui+BKVe2RhmDC3Z7ytWJCl+j8KOqKsVzw==", "path": "system.formats.asn1/9.0.4", "hashPath": "system.formats.asn1.9.0.4.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "path": "system.identitymodel.tokens.jwt/7.5.0", "hashPath": "system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UIBaK7c/A3FyQxmX/747xw4rCUkm1BhNiVU617U5jweNJssNjLJkPUGhBsrlDG0BpKWCYKsncD+Kqpy4KmvZZQ==", "path": "system.io.pipelines/9.0.2", "hashPath": "system.io.pipelines.9.0.2.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Async/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cPtIuuH8TIjVHSi2ewwReWGW1PfChPE0LxPIDlfwVcLuTM9GANFTXiMB7k3aC4sk3f0cQU25LNKzx+jZMxijqw==", "path": "system.linq.async/5.0.0", "hashPath": "system.linq.async.5.0.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Net.WebSockets/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2KJo8hir6Edi9jnMDAMhiJoI691xRBmKcbNpwjrvpIMOCTYOtBpSsSEGBxBDV7PKbasJNaFp1+PZz1D7xS41Hg==", "path": "system.net.websockets/4.0.0", "hashPath": "system.net.websockets.4.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ePDc9MvE/9QXeY6lU4Y85hjOSRzqdDF1XlqINhpThLKV33PYC2fLu/fadwDCWplq3mSef7Fof3vui3FUWpUp6w==", "path": "system.runtime.caching/9.0.4", "hashPath": "system.runtime.caching.9.0.4.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw==", "path": "system.security.claims/4.0.1", "hashPath": "system.security.claims.4.0.1.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cUFTcMlz/Qw9s90b2wnWSCvHdjv51Bau9FQqhsr4TlwSe1OX+7SoXUqphis5G74MLOvMOCghxPPlEqOdCrVVGA==", "path": "system.security.cryptography.pkcs/9.0.4", "hashPath": "system.security.cryptography.pkcs.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "path": "system.security.cryptography.protecteddata/9.0.4", "hashPath": "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YXQBGO+rIp142WrIVqq5HyOx6fTKDMHxhGWNeSYZDSMH6AnCEmx6ue6GMokdg+LTWEqtomPguRMiL3KT0LUeAg==", "path": "system.security.cryptography.xml/9.0.4", "hashPath": "system.security.cryptography.xml.9.0.4.nupkg.sha512"}, "System.Security.Principal/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww==", "path": "system.security.principal/4.0.1", "hashPath": "system.security.principal.4.0.1.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-V+5cCPpk1S2ngekUs9nDrQLHGiWFZMg8BthADQr+Fwi59a8DdHFu26S2oi9Bfgv+d67bqmkPqctJXMEXiimXUg==", "path": "system.text.encodings.web/9.0.4", "hashPath": "system.text.encodings.web.9.0.4.nupkg.sha512"}, "System.Text.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "path": "system.text.json/9.0.4", "hashPath": "system.text.json.9.0.4.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cIed5+HuYz+eV9yu9TH95zPkqmm1J9Qps9wxjB335sU8tsqc2kGdlTEH9FZzZeCS8a7mNSEsN8ZkyhQp1gfdEw==", "path": "system.threading.accesscontrol/8.0.0", "hashPath": "system.threading.accesscontrol.8.0.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "Umbraco.Cms/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mreSDAP9BdLpCMK9Am+0U5mrEjFJVKUi1Zu5yqMmdFrBzCyO7lOnOgK/U5SX5sQxJoa85CHHGkYpRTmNnaahPw==", "path": "umbraco.cms/16.0.0", "hashPath": "umbraco.cms.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Api.Common/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SdzM2CJShkGSo97dfz0r29Swte3HNbEANlLeanlpuflwQmp8POwFU3ZtJpP7U4Naw6V6j5tMiP+UDpAaM7AH4A==", "path": "umbraco.cms.api.common/16.0.0", "hashPath": "umbraco.cms.api.common.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Api.Delivery/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1KWK3A3tTnNzdEk/89gibBvYga/4lRLRuGjs0FW5uiooB+bR+BolJAH7Mt7tr+FnsVcWjKnO1BVAHnEsisrwTg==", "path": "umbraco.cms.api.delivery/16.0.0", "hashPath": "umbraco.cms.api.delivery.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Api.Management/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4Kx4NRjqVmBwxTgDhYZqPszDwKnEIA7LQFT8denIkqsj5VRQGXg+PQ+Y6jZG3idfQq9vRrKNSDAdt4hIOiWNtQ==", "path": "umbraco.cms.api.management/16.0.0", "hashPath": "umbraco.cms.api.management.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Core/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-J4WQaP50T8mRKNVS0Wo6M7EI+KFaHP/jOw72Kkk7B/4hf+CB9C6rJDGJVmgYIIAU0rObUhLA8UmrgBJ5Oxx26A==", "path": "umbraco.cms.core/16.0.0", "hashPath": "umbraco.cms.core.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Examine.Lucene/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rTHadVBoROYq5+xTmN6qc9Mr/17JoiLQhzAhbKoWOPkTUyKjGr2h4m2wmi4mZmJDZxqe0gkBgQjndUkd84L0IQ==", "path": "umbraco.cms.examine.lucene/16.0.0", "hashPath": "umbraco.cms.examine.lucene.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Imaging.ImageSharp/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MLqnI9u6MlNg62dK03/xIPpDScMMmkPKWFIXlbTVHdrHcqm+ZjQL0lasp1defbtPwyNfOvS8nu/nTOks17HrOg==", "path": "umbraco.cms.imaging.imagesharp/16.0.0", "hashPath": "umbraco.cms.imaging.imagesharp.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Infrastructure/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-66HnEWg/4qKbwmADZ2g8iYLSDSCIHJIs24zaP7thzz9mvsqvDfr2qWu9DIUJOCP/fZxkZaJ7O1dbTauJkh05Fw==", "path": "umbraco.cms.infrastructure/16.0.0", "hashPath": "umbraco.cms.infrastructure.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Persistence.EFCore/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xbe/xQMgTZ+Wp/74XGaK2ByyaYMZLxNCOzDUUdm5tdRTpKxmaaSHkkHGS8fxThvCG0FbOgZncxj3CCtrpcSk4Q==", "path": "umbraco.cms.persistence.efcore/16.0.0", "hashPath": "umbraco.cms.persistence.efcore.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Persistence.EFCore.Sqlite/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WTzqsdQIHhcOv0Q1S13F3G54IFvUbcpglS5x/h4CyWioVfXs7l4lxL8wEpsv1CGEcTF6I2BJZxe/nVyIdxDMg==", "path": "umbraco.cms.persistence.efcore.sqlite/16.0.0", "hashPath": "umbraco.cms.persistence.efcore.sqlite.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Persistence.EFCore.SqlServer/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-O7nf9yFv/ujbGLYShtlfSGWh9jbTLemPluvIY/s7aU4FlI/OU96RS5nDMtrVSwQW5lC5W8kX05AIhpLCgM3nHg==", "path": "umbraco.cms.persistence.efcore.sqlserver/16.0.0", "hashPath": "umbraco.cms.persistence.efcore.sqlserver.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Persistence.Sqlite/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XaZyi0hpO898jO1UpAyAKJD1N1OBUD7J1Q3gzF1QXDLTZB+i4ZKdoNTIQwsuh/0SkMmtzqfB5vnAF/6LFbBAgQ==", "path": "umbraco.cms.persistence.sqlite/16.0.0", "hashPath": "umbraco.cms.persistence.sqlite.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Persistence.SqlServer/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-vTidsBrVVritFfyyGlLHsjeJDmJ/kQz+gzMpjCXtJT7zPEd7NsqJrinuaHYYLZSlf9tlIw0jUmMCsS5pBHevuQ==", "path": "umbraco.cms.persistence.sqlserver/16.0.0", "hashPath": "umbraco.cms.persistence.sqlserver.16.0.0.nupkg.sha512"}, "Umbraco.Cms.PublishedCache.HybridCache/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ANqQhye2gmS+jYqGTU5Zvyg1XxHwGHnHzW4D4vfH6OlWcLjibCKosTl3q3WKU/RlVtOH6MDGHppfFREbY3dxA==", "path": "umbraco.cms.publishedcache.hybridcache/16.0.0", "hashPath": "umbraco.cms.publishedcache.hybridcache.16.0.0.nupkg.sha512"}, "Umbraco.Cms.StaticAssets/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xTM1xlIKNSZo5085CGSal3QMARzV6nryQfUL7a56Lj1spMUVnsBK1bdQQJ6pPLd7elJs+jXYN94CuRz35BdiCA==", "path": "umbraco.cms.staticassets/16.0.0", "hashPath": "umbraco.cms.staticassets.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Targets/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VERqEWBeomGR22qBHJWiTfjnIcWDGoIq9bycigFUgptm1CCy6GDxMBOWSqCF/VGD0xZfzo7UZnxHWqJJ3T8Z3g==", "path": "umbraco.cms.targets/16.0.0", "hashPath": "umbraco.cms.targets.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Web.Common/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-D3ng9Yl0xImGTCGFFe2VSWq6KvHyorwQz8KbNk0ovpbvErFDx5JRs3cQAKTz1V9CYkCOMcfLI8eDuLRC8O1ciQ==", "path": "umbraco.cms.web.common/16.0.0", "hashPath": "umbraco.cms.web.common.16.0.0.nupkg.sha512"}, "Umbraco.Cms.Web.Website/16.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yJ+iH2DhUGmwfmyU/oQ8eZtex9EQFLJz1NRmujZDmiIA53FBNYNZe7TMz8kgpJ0MqMFBq7QwLOmx1uPMf5bgMg==", "path": "umbraco.cms.web.website/16.0.0", "hashPath": "umbraco.cms.web.website.16.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticAssets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/1*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}