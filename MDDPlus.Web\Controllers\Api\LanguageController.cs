using Microsoft.AspNetCore.Mvc;
using MDDPlus.Web.Services;

namespace MDDPlus.Web.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class LanguageController : ControllerBase
    {
        private readonly ILanguageService _languageService;

        public LanguageController(ILanguageService languageService)
        {
            _languageService = languageService;
        }

        [HttpPost("switch")]
        public IActionResult SwitchLanguage([FromBody] SwitchLanguageRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Language) || 
                    !new[] { "ar", "en" }.Contains(request.Language))
                {
                    return BadRequest(new { error = "Invalid language code" });
                }

                _languageService.SetLanguage(request.Language);

                var redirectUrl = !string.IsNullOrEmpty(request.ReturnUrl) 
                    ? _languageService.GetLocalizedUrl(request.ReturnUrl, request.Language)
                    : "/";

                return Ok(new { 
                    success = true, 
                    language = request.Language,
                    direction = request.Language == "ar" ? "rtl" : "ltr",
                    redirectUrl = redirectUrl
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to switch language", details = ex.Message });
            }
        }

        [HttpGet("current")]
        public IActionResult GetCurrentLanguage()
        {
            try
            {
                var currentLanguage = _languageService.GetCurrentLanguage();
                var currentDirection = _languageService.GetCurrentDirection();
                var availableLanguages = _languageService.GetAvailableLanguages();

                return Ok(new
                {
                    current = new
                    {
                        language = currentLanguage,
                        direction = currentDirection
                    },
                    available = availableLanguages
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to get language info", details = ex.Message });
            }
        }
    }

    public class SwitchLanguageRequest
    {
        public string Language { get; set; } = "";
        public string ReturnUrl { get; set; } = "";
    }
}
