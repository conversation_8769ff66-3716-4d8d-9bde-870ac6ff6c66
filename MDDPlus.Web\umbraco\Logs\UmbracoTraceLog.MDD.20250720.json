{"@t":"2025-07-20T10:24:15.5562480Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:15.5661994Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5368903Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5399089Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5449249Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5454863Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5458781Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5462636Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5466424Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5470480Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5474388Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5478761Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5482968Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5486773Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5490762Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.5491265Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.7174421Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.7176749Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.7945683Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.7946167Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:16.7946342Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:24:21.0416830Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"f0240620fc20fa62324eaaf6f44e8d0b","@sp":"b5fa7f6c717d7ab2","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000020D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":20,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4c4cdfc1-1c24-4507-860f-6a94679ca99c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:21.0602165Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"f0240620fc20fa62324eaaf6f44e8d0b","@sp":"b5fa7f6c717d7ab2","Request":"{\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"oR6OdMN5Fe\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"OU71S-yMgktbeJxm_b2bt-9Qwu-0jiNZYlxlVy59kxk\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000020D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":20,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4c4cdfc1-1c24-4507-860f-6a94679ca99c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:21.1158818Z","@mt":"The authorization request was successfully validated.","@tr":"f0240620fc20fa62324eaaf6f44e8d0b","@sp":"b5fa7f6c717d7ab2","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000020D","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":20,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4c4cdfc1-1c24-4507-860f-6a94679ca99c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:29.8236142Z","@mt":"Revoking active tokens for user with ID {id}","@tr":"970c16dfea7ccee48ba6b5ebd31fd0a3","@sp":"ac15c4024ceab641","id":-1,"SourceContext":"Umbraco.Cms.Api.Management.Handlers.RevokeUserAuthenticationTokensNotificationHandler","ActionId":"3cd46aee-dc4c-4b60-8ac7-06e81c2f2c97","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Login (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:00000229","RequestPath":"/umbraco/management/api/v1/security/back-office/login","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":33,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"f84f24e0-f762-484d-b64a-5a2adea6d443","HttpRequestNumber":2,"HttpSessionId":"0b9ed31b-8ae2-c137-b692-bec714858d5f"}
{"@t":"2025-07-20T10:24:29.9813395Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"f8a9c3c22e6cf18b18330b6d9b4dbaba","@sp":"934e93d9a943a846","Endpoint":"Authorization","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000022B","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c03d7eca-94b5-4f56-b5fc-01ffd28b767c","HttpRequestNumber":3,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:29.9815778Z","@mt":"The authorization request was successfully extracted: {Request}.","@tr":"f8a9c3c22e6cf18b18330b6d9b4dbaba","@sp":"934e93d9a943a846","Request":"{\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"response_type\": \"code\",\r\n  \"state\": \"oR6OdMN5Fe\",\r\n  \"scope\": \"offline_access\",\r\n  \"prompt\": \"consent\",\r\n  \"access_type\": \"offline\",\r\n  \"code_challenge\": \"OU71S-yMgktbeJxm_b2bt-9Qwu-0jiNZYlxlVy59kxk\",\r\n  \"code_challenge_method\": \"S256\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000022B","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c03d7eca-94b5-4f56-b5fc-01ffd28b767c","HttpRequestNumber":3,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:29.9841300Z","@mt":"The authorization request was successfully validated.","@tr":"f8a9c3c22e6cf18b18330b6d9b4dbaba","@sp":"934e93d9a943a846","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000022B","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c03d7eca-94b5-4f56-b5fc-01ffd28b767c","HttpRequestNumber":3,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.0484816Z","@mt":"An ad hoc authorization was automatically created and associated with the '{ClientId}' application: {Identifier}.","@tr":"f8a9c3c22e6cf18b18330b6d9b4dbaba","@sp":"934e93d9a943a846","ClientId":"umbraco-back-office","Identifier":"e30e7086-eb7d-481e-85d0-47112eccf2de","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"13b350b7-db89-49e3-ba35-95c0a0c82bbb","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000022B","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c03d7eca-94b5-4f56-b5fc-01ffd28b767c","HttpRequestNumber":3,"HttpSessionId":"b55597dd-54a0-114c-429b-7e852a1e2af0"}
{"@t":"2025-07-20T10:24:30.1327655Z","@mt":"The authorization response was successfully returned to '{RedirectUri}' using the query response mode: {Response}.","@tr":"f8a9c3c22e6cf18b18330b6d9b4dbaba","@sp":"934e93d9a943a846","RedirectUri":"https://localhost:44387/umbraco/oauth_complete","Response":"{\r\n  \"code\": \"[redacted]\",\r\n  \"state\": \"oR6OdMN5Fe\",\r\n  \"iss\": \"https://localhost:44387/\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"13b350b7-db89-49e3-ba35-95c0a0c82bbb","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Authorize (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000022B","RequestPath":"/umbraco/management/api/v1/security/back-office/authorize","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"c03d7eca-94b5-4f56-b5fc-01ffd28b767c","HttpRequestNumber":3,"HttpSessionId":"b55597dd-54a0-114c-429b-7e852a1e2af0"}
{"@t":"2025-07-20T10:24:30.2588607Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"ccb752a50db7ef843529f11354ae300a","@sp":"b51f90b25f502811","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:00000239","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ff9b7f63-f564-4f0c-839c-32e9eba56a32","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.2631343Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"ccb752a50db7ef843529f11354ae300a","@sp":"b51f90b25f502811","Request":"{\r\n  \"grant_type\": \"authorization_code\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"code\": \"[redacted]\",\r\n  \"code_verifier\": \"iFILbPi8D4B37BWRPyOjaUiw7EPcQrO8EAJUaLo3pEcry4Z1UUjhAA1tT0IZdLdQs6JiIKjnSqHyqwHFPDGfZhliN9lcebaecB7wUwzF8qxBshn60bc1E01yXXpZzKtp\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:00000239","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ff9b7f63-f564-4f0c-839c-32e9eba56a32","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.3427992Z","@mt":"The token request was successfully validated.","@tr":"ccb752a50db7ef843529f11354ae300a","@sp":"b51f90b25f502811","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:00000239","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ff9b7f63-f564-4f0c-839c-32e9eba56a32","HttpRequestNumber":4,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.3547996Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"ccb752a50db7ef843529f11354ae300a","@sp":"b51f90b25f502811","Identifier":"ec748ba1-51b4-4234-b5a3-a3c2ec197dcb","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:00000239","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ff9b7f63-f564-4f0c-839c-32e9eba56a32","HttpRequestNumber":4,"HttpSessionId":"f3278382-e09b-8a1f-63ac-dc24ba38e0ea"}
{"@t":"2025-07-20T10:24:30.3725475Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"ccb752a50db7ef843529f11354ae300a","@sp":"b51f90b25f502811","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:00000239","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ff9b7f63-f564-4f0c-839c-32e9eba56a32","HttpRequestNumber":4,"HttpSessionId":"f3278382-e09b-8a1f-63ac-dc24ba38e0ea"}
{"@t":"2025-07-20T10:24:30.3815451Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"8966917e8c99013a559260fc22a54c58","@sp":"e01dc38cdcd223e9","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000023B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"7ed11bab-840e-446c-a247-84db4efe81d4","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.3818486Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"8966917e8c99013a559260fc22a54c58","@sp":"e01dc38cdcd223e9","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000023B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"7ed11bab-840e-446c-a247-84db4efe81d4","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.3911820Z","@mt":"The token request was successfully validated.","@tr":"8966917e8c99013a559260fc22a54c58","@sp":"e01dc38cdcd223e9","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000023B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"7ed11bab-840e-446c-a247-84db4efe81d4","HttpRequestNumber":5,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:30.3949641Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"8966917e8c99013a559260fc22a54c58","@sp":"e01dc38cdcd223e9","Identifier":"799bcb86-f3a4-425b-8d34-a8cb15542472","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000023B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"7ed11bab-840e-446c-a247-84db4efe81d4","HttpRequestNumber":5,"HttpSessionId":"c60de8b1-b5c8-a85b-b8e1-d1a052d0f4a2"}
{"@t":"2025-07-20T10:24:30.4013125Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"8966917e8c99013a559260fc22a54c58","@sp":"e01dc38cdcd223e9","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000023B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"7ed11bab-840e-446c-a247-84db4efe81d4","HttpRequestNumber":5,"HttpSessionId":"c60de8b1-b5c8-a85b-b8e1-d1a052d0f4a2"}
{"@t":"2025-07-20T10:24:45.6818058Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"6d246ae8d9f0e463dac4756004bfc0b5","@sp":"019bde40c5b6ec23","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000060B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":34,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"772eb93d-cebf-4c85-8cab-3a1f68a5dd22","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:45.6826909Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"6d246ae8d9f0e463dac4756004bfc0b5","@sp":"019bde40c5b6ec23","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000060B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":34,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"772eb93d-cebf-4c85-8cab-3a1f68a5dd22","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:45.6914792Z","@mt":"The token request was successfully validated.","@tr":"6d246ae8d9f0e463dac4756004bfc0b5","@sp":"019bde40c5b6ec23","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EUAEHLPN:0000060B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":34,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"772eb93d-cebf-4c85-8cab-3a1f68a5dd22","HttpRequestNumber":6,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:24:45.6943155Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"6d246ae8d9f0e463dac4756004bfc0b5","@sp":"019bde40c5b6ec23","Identifier":"97af6365-327c-4c94-84a5-a203f77c4ed9","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000060B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":34,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"772eb93d-cebf-4c85-8cab-3a1f68a5dd22","HttpRequestNumber":6,"HttpSessionId":"833c371b-01a1-edc6-46a5-b2aee981401f"}
{"@t":"2025-07-20T10:24:45.7004070Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"6d246ae8d9f0e463dac4756004bfc0b5","@sp":"019bde40c5b6ec23","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 299,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"93422dd0-3ae7-4c49-82f8-518466df57da","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:0000060B","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":34,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"772eb93d-cebf-4c85-8cab-3a1f68a5dd22","HttpRequestNumber":6,"HttpSessionId":"833c371b-01a1-edc6-46a5-b2aee981401f"}
{"@t":"2025-07-20T10:25:17.7402933Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":18,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:25:35.6603854Z","@mt":"Adding examine event handlers for {RegisteredIndexers} index providers.","@tr":"c908d50f2d497b565f04ab5bfabaa66f","@sp":"5309ee56b8c31a0d","RegisteredIndexers":3,"SourceContext":"Umbraco.Cms.Infrastructure.Examine.ExamineUmbracoIndexingHandler","ActionId":"5674a28f-9e04-459a-b673-75257a7fc220","ActionName":"Umbraco.Cms.Api.Management.Controllers.DocumentType.CreateDocumentTypeController.Create (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EUAEHLPN:00000687","RequestPath":"/umbraco/management/api/v1/document-type","ConnectionId":"0HNE7EUAEHLPN","ProcessId":42936,"ProcessName":"MDDPlus.Web","ThreadId":33,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"a2e56295-3bd6-499b-a230-f5c3690fb389","HttpRequestNumber":7,"HttpSessionId":"b1f8c7cf-96be-e53a-e022-b0b8735c65fb"}
{"@t":"2025-07-20T10:27:08.2726991Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:08.2857804Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2287274Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2321204Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2358675Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2365136Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2369963Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2374408Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2378552Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2383119Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2387935Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2392946Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2397940Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2402578Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2407336Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.2407969Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.3726700Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.3729197Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.4553210Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.4553839Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:09.4553955Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-20T10:27:12.9745488Z","@mt":"The request URI matched a server endpoint: {Endpoint}.","@tr":"aa02e924159b8cb848be5ea5d4d607c2","@sp":"ecfe083f5fb8792e","Endpoint":"Token","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EVTU3LI1:00000203","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EVTU3LI1","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":32,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ead03c31-13c0-40f8-a262-71fc18cf54ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:27:12.9838515Z","@mt":"The token request was successfully extracted: {Request}.","@tr":"aa02e924159b8cb848be5ea5d4d607c2","@sp":"ecfe083f5fb8792e","Request":"{\r\n  \"grant_type\": \"refresh_token\",\r\n  \"client_id\": \"umbraco-back-office\",\r\n  \"redirect_uri\": \"https://localhost:44387/umbraco/oauth_complete\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EVTU3LI1:00000203","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EVTU3LI1","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":32,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ead03c31-13c0-40f8-a262-71fc18cf54ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:27:13.1745007Z","@mt":"The token request was successfully validated.","@tr":"aa02e924159b8cb848be5ea5d4d607c2","@sp":"ecfe083f5fb8792e","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","RequestId":"0HNE7EVTU3LI1:00000203","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EVTU3LI1","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":32,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ead03c31-13c0-40f8-a262-71fc18cf54ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-20T10:27:13.2073646Z","@mt":"The token '{Identifier}' was successfully marked as redeemed.","@tr":"aa02e924159b8cb848be5ea5d4d607c2","@sp":"ecfe083f5fb8792e","Identifier":"8a4ec63d-6d0b-4263-b9c4-a2df117a6e8c","SourceContext":"OpenIddict.Core.OpenIddictTokenManager","ActionId":"b8f1a7a5-071c-4285-8dca-d75ddc2916c3","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EVTU3LI1:00000203","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EVTU3LI1","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":32,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ead03c31-13c0-40f8-a262-71fc18cf54ed","HttpRequestNumber":1,"HttpSessionId":"d6450ea8-b586-b8a2-df5e-ec448ab51f55"}
{"@t":"2025-07-20T10:27:13.2568565Z","@mt":"The response was successfully returned as a JSON document: {Response}.","@tr":"aa02e924159b8cb848be5ea5d4d607c2","@sp":"ecfe083f5fb8792e","Response":"{\r\n  \"access_token\": \"[redacted]\",\r\n  \"token_type\": \"Bearer\",\r\n  \"expires_in\": 300,\r\n  \"scope\": \"offline_access\",\r\n  \"refresh_token\": \"[redacted]\"\r\n}","SourceContext":"OpenIddict.Server.OpenIddictServerDispatcher","ActionId":"b8f1a7a5-071c-4285-8dca-d75ddc2916c3","ActionName":"Umbraco.Cms.Api.Management.Controllers.Security.BackOfficeController.Token (Umbraco.Cms.Api.Management)","RequestId":"0HNE7EVTU3LI1:00000203","RequestPath":"/umbraco/management/api/v1/security/back-office/token","ConnectionId":"0HNE7EVTU3LI1","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":32,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"ead03c31-13c0-40f8-a262-71fc18cf54ed","HttpRequestNumber":1,"HttpSessionId":"d6450ea8-b586-b8a2-df5e-ec448ab51f55"}
{"@t":"2025-07-20T10:28:10.4339705Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":34156,"ProcessName":"MDDPlus.Web","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
