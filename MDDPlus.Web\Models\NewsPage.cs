using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// News Page Document Type for MDD Plus Financial News and Updates
    /// </summary>
    public class NewsPage : PublishedContentModel
    {
        public NewsPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // News Content
        public string NewsTitle => this.Value<string>("newsTitle") ?? "الأخبار والتحديثات";
        public string NewsDescription => this.Value<string>("newsDescription") ?? "آخر الأخبار والتحديثات من عالم التمويل الجماعي والتكنولوجيا المالية";
        
        // Article Properties
        public string ArticleTitle => this.Value<string>("articleTitle") ?? "";
        public string ArticleExcerpt => this.Value<string>("articleExcerpt") ?? "";
        public string ArticleContent => this.Value<string>("articleContent") ?? "";
        public DateTime PublishDate => this.Value<DateTime>("publishDate", fallback: Fallback.ToDefaultValue, defaultValue: DateTime.Now);
        public string Author => this.Value<string>("author") ?? "فريق مدد بلس";
        public IPublishedContent? FeaturedImage => this.Value<IPublishedContent>("featuredImage");
        
        // Categories and Tags
        public IEnumerable<string> Categories => this.Value<IEnumerable<string>>("categories") ?? new List<string>();
        public IEnumerable<string> Tags => this.Value<IEnumerable<string>>("tags") ?? new List<string>();
        
        // Reading Time
        public int EstimatedReadingTime => this.Value<int>("estimatedReadingTime", fallback: Fallback.ToDefaultValue, defaultValue: 5);
        
        // Related Articles
        public IEnumerable<IPublishedContent> RelatedArticles => this.Value<IEnumerable<IPublishedContent>>("relatedArticles") ?? new List<IPublishedContent>();
        
        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? ArticleTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? ArticleExcerpt;
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? string.Join(", ", Tags);
    }
}
