using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class ContentPageController : UmbracoPageController
    {
        private readonly ILogger<ContentPageController> _logger;

        public ContentPageController(ILogger<ContentPageController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
            _logger = logger;
        }

        public IActionResult Index(ContentPage model)
        {
            // Set language and direction based on current culture
            var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
            ViewBag.Language = currentCulture.Name.StartsWith("ar") ? "ar" : "en";
            ViewBag.Direction = currentCulture.Name.StartsWith("ar") ? "rtl" : "ltr";

            // Set page-specific meta data
            ViewBag.Title = model.MetaTitle;
            ViewBag.MetaDescription = model.MetaDescription;
            ViewBag.MetaKeywords = model.MetaKeywords;

            return View(model);
        }
    }
}
