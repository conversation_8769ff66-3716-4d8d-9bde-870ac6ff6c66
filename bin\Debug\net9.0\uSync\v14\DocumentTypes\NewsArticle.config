<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="e8f9a0b1-2c3d-4e5f-6a7b-8c9d0e1f2a3b" Alias="newsArticle" Level="1">
  <Info>
    <Name>News Article</Name>
    <Icon>icon-article</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Individual news article for MDD Plus fintech website</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <ListView>00000000-0000-0000-0000-000000000000</ListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Compositions />
    <DefaultTemplate>NewsArticle</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="e8f9a0b1-2c3d-4e5f-6a7b-8c9d0e1f2a3b">NewsArticle</Template>
    </AllowedTemplates>
  </Info>
  <Structure />
  <GenericProperties>
    <!-- Article Content -->
    <GenericProperty>
      <Key>1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Name>Article Title</Name>
      <Alias>articleTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main article title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Name>Article Summary</Name>
      <Alias>articleSummary</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Brief article summary]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Name>Article Content</Name>
      <Alias>articleContent</Alias>
      <Definition>ca90c950-0aff-4e72-b976-a30b1ac57dad</Definition>
      <Type>Umbraco.TinyMCE</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Full article content]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Name>Featured Image</Name>
      <Alias>featuredImage</Alias>
      <Definition>ad9f0cf2-bda2-45d5-9ea1-a63cfc873fd3</Definition>
      <Type>Umbraco.MediaPicker3</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Article featured image]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Name>Publication Date</Name>
      <Alias>publicationDate</Alias>
      <Definition>e4d66c0f-b935-4200-81f0-025f7256b89a</Definition>
      <Type>Umbraco.DateTime</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Article publication date]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c</Key>
      <Name>Author</Name>
      <Alias>author</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Article author name]]></Description>
      <SortOrder>6</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d</Key>
      <Name>Tags</Name>
      <Alias>tags</Alias>
      <Definition>b6b73142-b9c1-4bf8-a16d-e1c7a6c4a5f2</Definition>
      <Type>Umbraco.Tags</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Article tags for categorization]]></Description>
      <SortOrder>7</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- SEO Properties -->
    <GenericProperty>
      <Key>8b9c0d1e-2f3a-4b5c-6d7e-8f9a0b1c2d3e</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Caption>Content</Caption>
      <Alias>content</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
