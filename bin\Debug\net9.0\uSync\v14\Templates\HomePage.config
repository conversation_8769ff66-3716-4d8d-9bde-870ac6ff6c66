<?xml version="1.0" encoding="utf-8"?>
<Template Key="b4c5d6e7-8f9a-1b2c-3d4e-5f6a7b8c9d0e" Alias="HomePage" Level="1">
  <Name>HomePage</Name>
  <Parent></Parent>
  <Path>/HomePage</Path>
  <Master></Master>
  <Design><![CDATA[@using MDDPlus.Models
@model HomePage
@{
    Layout = "_Layout";
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
}

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>@Model.HeroTitle</h1>
            <p>@Model.HeroSubtitle</p>
            @if (!string.IsNullOrEmpty(Model.HeroButtonText))
            {
                <a href="#services" class="btn btn-primary">@Model.HeroButtonText</a>
            }
        </div>
        @if (Model.HeroImage != null)
        {
            <div class="hero-image">
                <img src="@Model.HeroImage.Url()" alt="@Model.HeroImage.Value("altText")" />
            </div>
        }
    </div>
</section>

<!-- Statistics Section -->
<section class="statistics">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">@Model.TotalFunded</div>
                <div class="stat-label">إجمالي التمويل</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">@Model.AnnualReturn%</div>
                <div class="stat-label">العائد السنوي</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">@Model.ActiveInvestors</div>
                <div class="stat-label">المستثمرون النشطون</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">@Model.RepaymentRate%</div>
                <div class="stat-label">معدل السداد</div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
@if (!string.IsNullOrEmpty(Model.AboutText))
{
    <section class="about" id="about">
        <div class="container">
            <h2>@Model.AboutTitle</h2>
            <div class="about-content">
                @Html.Raw(Model.AboutText)
            </div>
        </div>
    </section>
}

<!-- Services Section -->
<section class="services" id="services">
    <div class="container">
        <h2>@Model.ServicesTitle</h2>
        <div class="services-grid">
            <div class="service-item">
                <div class="service-icon">
                    <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <h3>التمويل الآمن</h3>
                <p>حلول تمويلية متوافقة مع الشريعة الإسلامية ومرخصة من البنك المركزي السعودي</p>
            </div>
            <div class="service-item">
                <div class="service-icon">
                    <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                    </svg>
                </div>
                <h3>عوائد تنافسية</h3>
                <p>احصل على عوائد تصل إلى 18% سنوياً مع ضمانات قوية وشفافية كاملة</p>
            </div>
            <div class="service-item">
                <div class="service-icon">
                    <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19v2h-1.5v17.5c0 .83-.67 1.5-1.5 1.5H8c-.83 0-1.5-.67-1.5-1.5V4H5V2h4.5V.5c0-.28.22-.5.5-.5h4c.28 0 .5.22.5.5V2H19.5z"/>
                    </svg>
                </div>
                <h3>إدارة المخاطر</h3>
                <p>نظام متقدم لإدارة المخاطر مع معدل سداد يصل إلى 99.8%</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact" id="contact">
    <div class="container">
        <h2>@Model.ContactTitle</h2>
        <div class="contact-info">
            @if (!string.IsNullOrEmpty(Model.ContactEmail))
            {
                <div class="contact-item">
                    <strong>البريد الإلكتروني:</strong>
                    <a href="mailto:@Model.ContactEmail">@Model.ContactEmail</a>
                </div>
            }
            @if (!string.IsNullOrEmpty(Model.ContactPhone))
            {
                <div class="contact-item">
                    <strong>الهاتف:</strong>
                    <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                </div>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
}]]></Design>
</Template>
