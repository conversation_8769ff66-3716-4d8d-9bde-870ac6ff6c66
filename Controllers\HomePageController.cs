using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class HomePageController : UmbracoPageController
    {
        private readonly ILogger<HomePageController> _logger;

        public HomePageController(ILogger<HomePageController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
            _logger = logger;
        }

        public IActionResult Index(HomePage model)
        {
            // Set language and direction based on current culture
            var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
            ViewBag.Language = currentCulture.Name.StartsWith("ar") ? "ar" : "en";
            ViewBag.Direction = currentCulture.Name.StartsWith("ar") ? "rtl" : "ltr";
            ViewBag.ActivePage = "home";

            // Set page-specific meta data
            ViewBag.Title = model.MetaTitle;
            ViewBag.MetaDescription = model.MetaDescription;
            ViewBag.MetaKeywords = model.MetaKeywords;

            return View(model);
        }
    }
}
