using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// Contact Page Document Type for MDD Plus Fintech Website
    /// </summary>
    public class ContactPage : PublishedContentModel
    {
        public ContactPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Contact Information
        public string ContactTitle => this.Value<string>("contactTitle") ?? "تواصل معنا";
        public string ContactDescription => this.Value<string>("contactDescription") ?? "نحن هنا لمساعدتك في جميع استفساراتك المالية";
        
        // Office Information
        public string OfficeAddress => this.Value<string>("officeAddress") ?? "الرياض، المملكة العربية السعودية";
        public string PhoneNumber => this.Value<string>("phoneNumber") ?? "+966 11 234 5678";
        public string EmailAddress => this.Value<string>("emailAddress") ?? "<EMAIL>";
        public string WorkingHours => this.Value<string>("workingHours") ?? "الأحد - الخميس: 9:00 ص - 6:00 م";
        
        // Support Information
        public string SupportEmail => this.Value<string>("supportEmail") ?? "<EMAIL>";
        public string InvestorRelationsEmail => this.Value<string>("investorRelationsEmail") ?? "<EMAIL>";
        public string ComplianceEmail => this.Value<string>("complianceEmail") ?? "<EMAIL>";
        
        // Map Settings
        public string MapLatitude => this.Value<string>("mapLatitude") ?? "24.7136";
        public string MapLongitude => this.Value<string>("mapLongitude") ?? "46.6753";
        
        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? "تواصل معنا - مدد بلس";
        public string MetaDescription => this.Value<string>("metaDescription") ?? "تواصل مع فريق مدد بلس للحصول على الدعم والمساعدة في جميع خدماتنا المالية";
    }
}
