@using MDDPlus.Models
@model ContentPage
@{
    Layout = "_Layout";
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
}

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>@Model.PageTitle</h1>
        @if (!string.IsNullOrEmpty(Model.PageSubtitle))
        {
            <p class="page-subtitle">@Model.PageSubtitle</p>
        }
    </div>
</section>

<!-- Page Content -->
<section class="page-content">
    <div class="container">
        @if (Model.PageImage != null)
        {
            <div class="page-image">
                <img src="@Model.PageImage.Url()" alt="@Model.PageImage.Value("altText")" />
            </div>
        }
        
        @if (!string.IsNullOrEmpty(Model.PageContent))
        {
            <div class="content">
                @Html.Raw(Model.PageContent)
            </div>
        }
    </div>
</section>
