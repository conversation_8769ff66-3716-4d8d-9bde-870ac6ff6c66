@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<MDDPlus.Models.ContentPage>
@{
    Layout = "_Layout";
    ViewBag.Title = Model?.MetaTitle ?? Model?.PageTitle ?? "مدد بلس";
    ViewBag.MetaDescription = Model?.MetaDescription ?? Model?.PageSubtitle ?? "";
    var isRtl = System.Globalization.CultureInfo.CurrentCulture.Name.StartsWith("ar");
}

<!-- Content Page -->
<section class="content-page" style="padding-top: 120px;">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header animate-on-scroll" data-animation="fadeInUp">
            <h1 class="page-title">@Model?.PageTitle</h1>
            @if (!string.IsNullOrEmpty(Model?.PageSubtitle))
            {
                <p class="page-subtitle">@Model.PageSubtitle</p>
            }
        </div>

        <!-- Page Image -->
        @if (Model?.PageImage != null)
        {
            <div class="page-image animate-on-scroll" data-animation="scaleIn" data-delay="200">
                <img src="@Model.PageImage.Url()" alt="@Model.PageTitle" class="img-fluid rounded-lg shadow-lg">
            </div>
        }

        <!-- Page Content -->
        <div class="page-content animate-on-scroll" data-animation="fadeInUp" data-delay="400">
            <div class="content-wrapper">
                @if (!string.IsNullOrEmpty(Model?.PageContent))
                {
                    @Html.Raw(Model.PageContent)
                }
                else
                {
                    <div class="empty-content">
                        <div class="empty-icon">📄</div>
                        <h3 data-ar="المحتوى قيد التطوير" data-en="Content Coming Soon">المحتوى قيد التطوير</h3>
                        <p data-ar="نعمل على إضافة محتوى هذه الصفحة قريباً" data-en="We're working on adding content to this page soon">نعمل على إضافة محتوى هذه الصفحة قريباً</p>
                    </div>
                }
            </div>
        </div>

        <!-- Navigation -->
        <div class="page-navigation animate-on-scroll" data-animation="fadeInUp" data-delay="600">
            @if (Model?.Parent != null)
            {
                <a href="@Model.Parent.Url()" class="btn btn-secondary">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
                    </svg>
                    <span data-ar="العودة" data-en="Back">العودة</span>
                </a>
            }

            @if (Model?.Children?.Any() == true)
            {
                <div class="child-pages">
                    <h4 data-ar="الصفحات الفرعية" data-en="Sub Pages">الصفحات الفرعية</h4>
                    <div class="child-pages-grid">
                        @foreach (var child in Model.Children.Where(x => x.IsVisible()))
                        {
                            <a href="@child.Url()" class="child-page-card">
                                <h5>@child.Name</h5>
                                @if (child is MDDPlus.Models.ContentPage childPage && !string.IsNullOrEmpty(childPage.PageSubtitle))
                                {
                                    <p>@childPage.PageSubtitle</p>
                                }
                            </a>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</section>
