using Microsoft.AspNetCore.Mvc;

namespace MDDPlus.Controllers
{
    public class TestController : Controller
    {
        [Route("test")]
        public IActionResult Index()
        {
            return Content(@"
<!DOCTYPE html>
<html>
<head>
    <title>MDD Plus Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            border-radius: 10px;
        }
        .status { 
            background: rgba(255,255,255,0.1); 
            padding: 20px; 
            border-radius: 10px; 
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        h1 { color: #ffd700; }
        a { color: #ffd700; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🚀 MDD Plus - System Status</h1>
    
    <div class='status'>
        <h3>✅ Application Status</h3>
        <p><strong>Status:</strong> Running Successfully</p>
        <p><strong>Framework:</strong> .NET 9 + Umbraco 14</p>
        <p><strong>Time:</strong> " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + @"</p>
    </div>
    
    <div class='status'>
        <h3>🌐 URLs</h3>
        <p><strong>Website:</strong> <a href='http://localhost:26967'>http://localhost:26967</a></p>
        <p><strong>Umbraco Admin:</strong> <a href='http://localhost:26967/umbraco'>http://localhost:26967/umbraco</a></p>
        <p><strong>Test Page:</strong> <a href='http://localhost:26967/test'>http://localhost:26967/test</a></p>
    </div>
    
    <div class='status'>
        <h3>📋 Next Steps</h3>
        <p>1. Go to <a href='http://localhost:26967/umbraco'>Umbraco Admin</a></p>
        <p>2. Create HomePage Document Type</p>
        <p>3. Add content and publish</p>
        <p>4. View your website</p>
    </div>
    
    <div class='status'>
        <h3>🔧 Quick Actions</h3>
        <p><a href='http://localhost:26967/umbraco#/settings/documentTypes'>→ Create Document Types</a></p>
        <p><a href='http://localhost:26967/umbraco#/content'>→ Manage Content</a></p>
        <p><a href='http://localhost:26967'>→ View Website</a></p>
    </div>
</body>
</html>", "text/html");
        }
    }
}
