/* MDD Plus - Clean Umbraco Site Styles */

:root {
    --primary-color: #1a365d;
    --secondary-color: #d4af37;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: var(--bg-primary);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.navbar-brand h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #2d5a87;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color), #2d5a87);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.5rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* Statistics Section */
.statistics {
    background: var(--bg-secondary);
    padding: 4rem 0;
    margin-top: -2rem;
    position: relative;
    z-index: 3;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Sections */
.about, .services, .contact {
    padding: 3rem 0;
}

.about {
    background: var(--bg-secondary);
}

.about h2, .services h2, .contact h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 2rem;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.8;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-item {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.service-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border-color: var(--secondary-color);
}

.service-item h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.service-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.service-icon svg {
    width: 48px;
    height: 48px;
}

/* Contact */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
}

.contact-item strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.contact-item a {
    color: var(--text-primary);
    text-decoration: none;
}

.contact-item a:hover {
    color: var(--primary-color);
}

/* Page Content */
.page-header {
    background: var(--bg-secondary);
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.page-content {
    padding: 3rem 0;
}

.page-image {
    text-align: center;
    margin-bottom: 2rem;
}

.page-image img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
}

.content {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.8;
}

/* Footer */
.footer {
    background: var(--primary-color);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-section a:hover {
    opacity: 1;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.8;
}

/* News Page Styles */
.featured-articles {
    padding: 4rem 0;
    background: var(--bg-secondary);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.articles-grid.featured {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.article-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.article-card.featured {
    border: 2px solid var(--secondary-color);
}

.article-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-content {
    padding: 1.5rem;
}

.article-content h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    line-height: 1.4;
}

.article-content h3 a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-content h3 a:hover {
    color: var(--primary-color);
}

.article-summary {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tag {
    background: var(--bg-secondary);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: var(--secondary-color);
}

/* Contact Form Styles */
.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--bg-secondary);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group.checkbox {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.form-group.checkbox input {
    width: auto;
    margin: 0;
}

.form-group.checkbox label {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Map Section */
.map-section {
    padding: 4rem 0;
    background: white;
}

.map-container {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow);
    height: 400px;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Responsive */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
    }

    .navbar-nav {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .articles-grid,
    .articles-grid.featured {
        grid-template-columns: 1fr;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
