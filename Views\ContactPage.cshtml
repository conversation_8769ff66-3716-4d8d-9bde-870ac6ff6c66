@using MDDPlus.Models
@model ContactPage
@{
    Layout = "_Layout";
    ViewBag.Title = Model.MetaTitle ?? Model.PageTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
}

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>@Model.PageTitle</h1>
        @if (!string.IsNullOrEmpty(Model.PageSubtitle))
        {
            <p class="page-subtitle">@Model.PageSubtitle</p>
        }
    </div>
</section>

<!-- Contact Introduction -->
@if (!string.IsNullOrEmpty(Model.ContactIntroduction))
{
    <section class="contact-intro">
        <div class="container">
            @Html.Raw(Model.ContactIntroduction)
        </div>
    </section>
}

<!-- Contact Information & Form -->
<section class="contact-section">
    <div class="container">
        <div class="contact-wrapper">
            <!-- Contact Information -->
            <div class="contact-info">
                <h2>معلومات التواصل</h2>
                
                @if (!string.IsNullOrEmpty(Model.OfficeAddress))
                {
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h3>العنوان</h3>
                            <p>@Html.Raw(Model.OfficeAddress.Replace("\n", "<br>"))</p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                {
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h3>الهاتف</h3>
                            <p><a href="tel:@Model.PhoneNumber">@Model.PhoneNumber</a></p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.EmailAddress))
                {
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h3>البريد الإلكتروني</h3>
                            <p><a href="mailto:@Model.EmailAddress">@Model.EmailAddress</a></p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.WhatsappNumber))
                {
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h3>واتساب</h3>
                            <p><a href="https://wa.me/@Model.WhatsappNumber.Replace("+", "").Replace(" ", "")" target="_blank">@Model.WhatsappNumber</a></p>
                        </div>
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(Model.WorkingHours))
                {
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h3>ساعات العمل</h3>
                            <p>@Html.Raw(Model.WorkingHours.Replace("\n", "<br>"))</p>
                        </div>
                    </div>
                }
            </div>
            
            <!-- Contact Form -->
            <div class="contact-form">
                <h2>أرسل لنا رسالة</h2>
                <form id="contactForm" action="/contact/submit" method="post">
                    <div class="form-group">
                        <label for="name">الاسم الكامل *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">الموضوع *</label>
                        <select id="subject" name="subject" required>
                            <option value="">اختر الموضوع</option>
                            <option value="investment">استفسار عن الاستثمار</option>
                            <option value="financing">طلب تمويل</option>
                            <option value="partnership">شراكة</option>
                            <option value="support">الدعم الفني</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">الرسالة *</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    
                    <div class="form-group checkbox">
                        <label>
                            <input type="checkbox" name="privacy" required>
                            أوافق على <a href="/privacy-policy" target="_blank">سياسة الخصوصية</a> وشروط الاستخدام *
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
@if (!string.IsNullOrEmpty(Model.GoogleMapsEmbed) || (!string.IsNullOrEmpty(Model.Latitude) && !string.IsNullOrEmpty(Model.Longitude)))
{
    <section class="map-section">
        <div class="container">
            <h2>موقعنا</h2>
            @if (!string.IsNullOrEmpty(Model.GoogleMapsEmbed))
            {
                <div class="map-container">
                    @Html.Raw(Model.GoogleMapsEmbed)
                </div>
            }
            else if (!string.IsNullOrEmpty(Model.Latitude) && !string.IsNullOrEmpty(Model.Longitude))
            {
                <div class="map-container">
                    <div id="customMap" data-lat="@Model.Latitude" data-lng="@Model.Longitude"></div>
                </div>
            }
        </div>
    </section>
}

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Contact form handling
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Basic form validation
                    const formData = new FormData(this);
                    const name = formData.get('name');
                    const email = formData.get('email');
                    const subject = formData.get('subject');
                    const message = formData.get('message');
                    const privacy = formData.get('privacy');
                    
                    if (!name || !email || !subject || !message || !privacy) {
                        alert('يرجى ملء جميع الحقول المطلوبة');
                        return;
                    }
                    
                    // Email validation
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        alert('يرجى إدخال بريد إلكتروني صحيح');
                        return;
                    }
                    
                    // Submit form (you can implement actual submission logic here)
                    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                    this.reset();
                });
            }
            
            // Initialize custom map if coordinates are provided
            const customMap = document.getElementById('customMap');
            if (customMap) {
                const lat = parseFloat(customMap.dataset.lat);
                const lng = parseFloat(customMap.dataset.lng);
                
                // You can integrate with Google Maps API or other mapping services here
                customMap.innerHTML = `<p>الخريطة: ${lat}, ${lng}</p>`;
            }
        });
    </script>
}
