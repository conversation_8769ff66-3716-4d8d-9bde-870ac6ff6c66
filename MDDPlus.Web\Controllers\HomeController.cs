using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;

namespace MDDPlus.Web.Controllers
{
    public class HomeController : UmbracoPageController
    {
        public HomeController(ILogger<UmbracoPageController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
        }

        public IActionResult Index()
        {
            // Set language and direction based on current culture
            var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
            ViewBag.Language = currentCulture.Name.StartsWith("ar") ? "ar" : "en";
            ViewBag.Direction = currentCulture.Name.StartsWith("ar") ? "rtl" : "ltr";

            return View();
        }
    }
}
