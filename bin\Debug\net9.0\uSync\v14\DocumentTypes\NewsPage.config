<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="d6e7f8a9-0b1c-2d3e-4f5a-6b7c8d9e0f1a" Alias="newsPage" Level="1">
  <Info>
    <Name>News Page</Name>
    <Icon>icon-newspaper</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>News and articles page for MDD Plus fintech website</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <ListView>00000000-0000-0000-0000-000000000000</ListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Compositions />
    <DefaultTemplate>NewsPage</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="d7e8f9a0-1b2c-3d4e-5f6a-7b8c9d0e1f2a">NewsPage</Template>
    </AllowedTemplates>
  </Info>
  <Structure>
    <DocumentType Key="e8f9a0b1-2c3d-4e5f-6a7b-8c9d0e1f2a3b">newsArticle</DocumentType>
  </Structure>
  <GenericProperties>
    <!-- Page Content -->
    <GenericProperty>
      <Key>1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Name>Page Title</Name>
      <Alias>pageTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[News page title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Name>Page Subtitle</Name>
      <Alias>pageSubtitle</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[News page subtitle]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Name>Featured Articles</Name>
      <Alias>featuredArticles</Alias>
      <Definition>7e062c12-1cf9-40e0-8f13-45b612c89202</Definition>
      <Type>Umbraco.MultiNodeTreePicker</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Select featured news articles]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- SEO Properties -->
    <GenericProperty>
      <Key>4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Caption>Content</Caption>
      <Alias>content</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
