@model MDDPlus.Web.Controllers.ContentEditModel
@{
    ViewBag.Title = "تحرير المحتوى - مدد بلس";
    Layout = "_AdminLayout";
}

<div class="admin-container">
    <div class="admin-header">
        <h1>تحرير المحتوى</h1>
        <p>تحرير محتوى الصفحة الرئيسية</p>
    </div>

    <form method="post" action="/admin/content/edit/@Model.Id" class="content-form">
        <div class="form-sections">
            <!-- Hero Section -->
            <div class="card">
                <div class="card-header">
                    <h3>قسم البطل (Hero Section)</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="heroTitle" class="form-label">عنوان البطل الرئيسي</label>
                        <input type="text" id="heroTitle" name="HeroTitle" value="@Model.HeroTitle" class="form-input" placeholder="منصة التمويل الجماعي بالدين الرائدة في السعودية">
                    </div>

                    <div class="form-group">
                        <label for="heroSubtitle" class="form-label">العنوان الفرعي</label>
                        <textarea id="heroSubtitle" name="HeroSubtitle" class="form-input" rows="3" placeholder="حلول تمويلية متوافقة مع الشريعة الإسلامية...">@Model.HeroSubtitle</textarea>
                    </div>

                    <div class="form-group">
                        <label for="heroButtonText" class="form-label">نص الزر الرئيسي</label>
                        <input type="text" id="heroButtonText" name="HeroButtonText" value="@Model.HeroButtonText" class="form-input" placeholder="ابدأ الاستثمار الآن">
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="card">
                <div class="card-header">
                    <h3>قسم الإحصائيات</h3>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="totalFunded" class="form-label">إجمالي المبلغ الممول (ريال)</label>
                            <input type="text" id="totalFunded" name="TotalFunded" value="@Model.TotalFunded" class="form-input" placeholder="500000000">
                            <small class="form-help">أدخل الرقم فقط بدون فواصل</small>
                        </div>

                        <div class="form-group">
                            <label for="annualReturn" class="form-label">العائد السنوي (%)</label>
                            <input type="text" id="annualReturn" name="AnnualReturn" value="@Model.AnnualReturn" class="form-input" placeholder="18">
                            <small class="form-help">أدخل النسبة المئوية فقط</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Section -->
            <div class="card">
                <div class="card-header">
                    <h3>تحسين محركات البحث (SEO)</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="metaTitle" class="form-label">عنوان الصفحة (Meta Title)</label>
                        <input type="text" id="metaTitle" name="MetaTitle" value="@Model.MetaTitle" class="form-input" placeholder="مدد بلس - منصة التمويل الجماعي">
                        <small class="form-help">يظهر في نتائج البحث وعنوان المتصفح</small>
                    </div>

                    <div class="form-group">
                        <label for="metaDescription" class="form-label">وصف الصفحة (Meta Description)</label>
                        <textarea id="metaDescription" name="MetaDescription" class="form-input" rows="3" placeholder="منصة مدد بلس للتمويل الجماعي بالدين...">@Model.MetaDescription</textarea>
                        <small class="form-help">يظهر في نتائج البحث تحت العنوان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M5 13l4 4L19 7"/>
                </svg>
                حفظ التغييرات
            </button>
            <a href="/admin/content" class="btn btn-outline">
                إلغاء
            </a>
            <a href="/" target="_blank" class="btn btn-secondary">
                معاينة الصفحة
            </a>
        </div>
    </form>

    <!-- Preview Section -->
    <div class="preview-section">
        <div class="card">
            <div class="card-header">
                <h3>معاينة سريعة</h3>
            </div>
            <div class="card-body">
                <div class="preview-hero">
                    <h2 id="preview-title">@Model.HeroTitle</h2>
                    <p id="preview-subtitle">@Model.HeroSubtitle</p>
                    <button class="btn btn-primary" id="preview-button">@Model.HeroButtonText</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.content-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-sections {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    direction: rtl;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
}

.preview-section {
    margin-top: 3rem;
}

.preview-hero {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: white;
    border-radius: 0.5rem;
}

.preview-hero h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.preview-hero p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('heroTitle');
    const subtitleInput = document.getElementById('heroSubtitle');
    const buttonInput = document.getElementById('heroButtonText');
    
    const previewTitle = document.getElementById('preview-title');
    const previewSubtitle = document.getElementById('preview-subtitle');
    const previewButton = document.getElementById('preview-button');
    
    titleInput.addEventListener('input', function() {
        previewTitle.textContent = this.value || 'عنوان البطل الرئيسي';
    });
    
    subtitleInput.addEventListener('input', function() {
        previewSubtitle.textContent = this.value || 'العنوان الفرعي';
    });
    
    buttonInput.addEventListener('input', function() {
        previewButton.textContent = this.value || 'نص الزر';
    });
});
</script>
