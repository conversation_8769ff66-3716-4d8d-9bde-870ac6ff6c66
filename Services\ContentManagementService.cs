using Umbraco.Cms.Core.Services;
using Umbraco.Cms.Core.Models;
using Microsoft.Extensions.Logging;

namespace MDDPlus.Services
{
    public interface IContentManagementService
    {
        Task<bool> CreateInitialContentAsync();
        Task<bool> CreateHomePageAsync();
        Task<bool> CreateAboutPageAsync();
        Task<bool> CreateServicesPageAsync();
        Task<bool> CreateContactPageAsync();
    }

    public class ContentManagementService : IContentManagementService
    {
        private readonly IContentService _contentService;
        private readonly IContentTypeService _contentTypeService;
        private readonly ILogger<ContentManagementService> _logger;

        public ContentManagementService(
            IContentService contentService,
            IContentTypeService contentTypeService,
            ILogger<ContentManagementService> logger)
        {
            _contentService = contentService;
            _contentTypeService = contentTypeService;
            _logger = logger;
        }

        public async Task<bool> CreateInitialContentAsync()
        {
            try
            {
                _logger.LogInformation("Starting initial content creation for MDD Plus");

                // Create home page first
                await CreateHomePageAsync();

                // Create other pages
                await CreateAboutPageAsync();
                await CreateServicesPageAsync();
                await CreateContactPageAsync();

                _logger.LogInformation("Initial content creation completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating initial content");
                return false;
            }
        }

        public async Task<bool> CreateHomePageAsync()
        {
            try
            {
                var homePageType = _contentTypeService.Get("homePage");
                if (homePageType == null)
                {
                    _logger.LogWarning("HomePage document type not found");
                    return false;
                }

                // Check if home page already exists
                var existingHomePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (existingHomePage != null)
                {
                    _logger.LogInformation("Home page already exists");
                    return true;
                }

                var homePage = _contentService.Create("الصفحة الرئيسية", -1, "homePage");

                // Set Arabic content
                homePage.SetCultureName("مدد بلس - الصفحة الرئيسية", "ar-SA");
                homePage.SetValue("heroTitle", "منصة التمويل الجماعي بالدين الرائدة في السعودية", "ar-SA");
                homePage.SetValue("heroSubtitle", "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. استثمر بأمان واحصل على عوائد تنافسية تصل إلى 18% سنوياً.", "ar-SA");
                homePage.SetValue("heroButtonText", "ابدأ الاستثمار الآن", "ar-SA");
                
                homePage.SetValue("aboutTitle", "من نحن", "ar-SA");
                homePage.SetValue("aboutText", "<p>مدد بلس هي منصة التمويل الجماعي بالدين الرائدة في المملكة العربية السعودية. نحن نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، مما يتيح للمستثمرين الحصول على عوائد تنافسية بينما ندعم الشركات الصغيرة والمتوسطة في تحقيق أهدافها.</p><p>مرخصة من البنك المركزي السعودي، نحن ملتزمون بأعلى معايير الشفافية والأمان في جميع عملياتنا.</p>", "ar-SA");
                
                homePage.SetValue("servicesTitle", "خدماتنا", "ar-SA");
                homePage.SetValue("contactTitle", "تواصل معنا", "ar-SA");

                // Set English content
                homePage.SetCultureName("MDD Plus - Home Page", "en-US");
                homePage.SetValue("heroTitle", "Saudi Arabia's Leading Debt Crowdfunding Platform", "en-US");
                homePage.SetValue("heroSubtitle", "Sharia-compliant financing solutions, licensed by the Saudi Central Bank. Invest safely and earn competitive returns up to 18% annually.", "en-US");
                homePage.SetValue("heroButtonText", "Start Investing Now", "en-US");
                
                homePage.SetValue("aboutTitle", "About Us", "en-US");
                homePage.SetValue("aboutText", "<p>MDD Plus is the leading debt crowdfunding platform in Saudi Arabia. We provide innovative financing solutions that are Sharia-compliant, allowing investors to earn competitive returns while supporting small and medium enterprises in achieving their goals.</p><p>Licensed by the Saudi Central Bank, we are committed to the highest standards of transparency and security in all our operations.</p>", "en-US");
                
                homePage.SetValue("servicesTitle", "Our Services", "en-US");
                homePage.SetValue("contactTitle", "Contact Us", "en-US");

                // Set statistics (language-neutral)
                homePage.SetValue("totalFunded", "500,000,000");
                homePage.SetValue("annualReturn", "18");
                homePage.SetValue("activeInvestors", "5,000");
                homePage.SetValue("repaymentRate", "99.8");

                // Set contact info
                homePage.SetValue("contactEmail", "<EMAIL>");
                homePage.SetValue("contactPhone", "+966 11 234 5678");

                // Set SEO
                homePage.SetValue("metaTitle", "مدد بلس - منصة التمويل الجماعي بالدين الرائدة في السعودية", "ar-SA");
                homePage.SetValue("metaDescription", "منصة مدد بلس للتمويل الجماعي بالدين - حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. عوائد تصل إلى 18% سنوياً.", "ar-SA");
                homePage.SetValue("metaKeywords", "التمويل الجماعي، الاستثمار، السعودية، الشريعة الإسلامية، مدد بلس", "ar-SA");

                homePage.SetValue("metaTitle", "MDD Plus - Saudi Arabia's Leading Debt Crowdfunding Platform", "en-US");
                homePage.SetValue("metaDescription", "MDD Plus debt crowdfunding platform - Sharia-compliant financing solutions licensed by Saudi Central Bank. Returns up to 18% annually.", "en-US");
                homePage.SetValue("metaKeywords", "crowdfunding, investment, Saudi Arabia, Sharia compliant, MDD Plus", "en-US");

                var saveResult = _contentService.Save(homePage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(homePage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("Home page created and published successfully");
                        return true;
                    }
                }

                _logger.LogError("Failed to save or publish home page");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating home page");
                return false;
            }
        }

        public async Task<bool> CreateAboutPageAsync()
        {
            try
            {
                var contentPageType = _contentTypeService.Get("contentPage");
                if (contentPageType == null)
                {
                    _logger.LogWarning("ContentPage document type not found");
                    return false;
                }

                var homePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (homePage == null)
                {
                    _logger.LogWarning("Home page not found, cannot create about page");
                    return false;
                }

                var aboutPage = _contentService.Create("من نحن", homePage.Id, "contentPage");

                // Set Arabic content
                aboutPage.SetCultureName("من نحن", "ar-SA");
                aboutPage.SetValue("pageTitle", "من نحن", "ar-SA");
                aboutPage.SetValue("pageSubtitle", "تعرف على مدد بلس ورؤيتنا في مجال التمويل الجماعي", "ar-SA");
                aboutPage.SetValue("pageContent", "<h2>رؤيتنا</h2><p>أن نكون المنصة الرائدة في مجال التمويل الجماعي بالدين في المنطقة، نساهم في تمكين الشركات الصغيرة والمتوسطة وتحقيق التنمية الاقتصادية المستدامة.</p><h2>مهمتنا</h2><p>نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، تربط بين المستثمرين والشركات بطريقة آمنة وشفافة، مما يحقق عوائد مجزية للمستثمرين ونمواً مستداماً للشركات.</p><h2>قيمنا</h2><ul><li><strong>الشفافية:</strong> نؤمن بالشفافية الكاملة في جميع عملياتنا</li><li><strong>الأمان:</strong> نضع أمان استثمارات عملائنا في المقدمة</li><li><strong>الابتكار:</strong> نسعى دائماً لتطوير حلول مالية مبتكرة</li><li><strong>التوافق الشرعي:</strong> جميع منتجاتنا متوافقة مع أحكام الشريعة الإسلامية</li></ul>", "ar-SA");

                // Set English content
                aboutPage.SetCultureName("About Us", "en-US");
                aboutPage.SetValue("pageTitle", "About Us", "en-US");
                aboutPage.SetValue("pageSubtitle", "Learn about MDD Plus and our vision in crowdfunding", "en-US");
                aboutPage.SetValue("pageContent", "<h2>Our Vision</h2><p>To be the leading debt crowdfunding platform in the region, contributing to empowering small and medium enterprises and achieving sustainable economic development.</p><h2>Our Mission</h2><p>We provide innovative and Sharia-compliant financing solutions that connect investors and companies in a safe and transparent manner, achieving rewarding returns for investors and sustainable growth for companies.</p><h2>Our Values</h2><ul><li><strong>Transparency:</strong> We believe in complete transparency in all our operations</li><li><strong>Security:</strong> We put the security of our clients' investments first</li><li><strong>Innovation:</strong> We always strive to develop innovative financial solutions</li><li><strong>Sharia Compliance:</strong> All our products comply with Islamic law</li></ul>", "en-US");

                var saveResult = _contentService.Save(aboutPage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(aboutPage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("About page created and published successfully");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating about page");
                return false;
            }
        }

        public async Task<bool> CreateServicesPageAsync()
        {
            // Similar implementation for services page
            return true;
        }

        public async Task<bool> CreateContactPageAsync()
        {
            // Similar implementation for contact page
            return true;
        }
    }
}
