using Umbraco.Cms.Core.Services;
using Umbraco.Cms.Core.Models;
using Microsoft.Extensions.Logging;

namespace MDDPlus.Services
{
    public interface IContentManagementService
    {
        Task<bool> CreateInitialContentAsync();
        Task<bool> CreateHomePageAsync();
        Task<bool> CreateAboutPageAsync();
        Task<bool> CreateServicesPageAsync();
        Task<bool> CreateContactPageAsync();
    }

    public class ContentManagementService : IContentManagementService
    {
        private readonly IContentService _contentService;
        private readonly IContentTypeService _contentTypeService;
        private readonly ILogger<ContentManagementService> _logger;

        public ContentManagementService(
            IContentService contentService,
            IContentTypeService contentTypeService,
            ILogger<ContentManagementService> logger)
        {
            _contentService = contentService;
            _contentTypeService = contentTypeService;
            _logger = logger;
        }

        public async Task<bool> CreateInitialContentAsync()
        {
            try
            {
                _logger.LogInformation("Starting initial content creation for MDD Plus");

                // Create home page first
                await CreateHomePageAsync();

                // Create other pages
                await CreateAboutPageAsync();
                await CreateServicesPageAsync();
                await CreateContactPageAsync();

                _logger.LogInformation("Initial content creation completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating initial content");
                return false;
            }
        }

        public async Task<bool> CreateHomePageAsync()
        {
            try
            {
                var homePageType = _contentTypeService.Get("homePage");
                if (homePageType == null)
                {
                    _logger.LogWarning("HomePage document type not found");
                    return false;
                }

                // Check if home page already exists
                var existingHomePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (existingHomePage != null)
                {
                    _logger.LogInformation("Home page already exists");
                    return true;
                }

                var homePage = _contentService.Create("الصفحة الرئيسية", -1, "homePage");

                // Set Arabic content
                homePage.SetCultureName("مدد بلس - الصفحة الرئيسية", "ar-SA");
                homePage.SetValue("heroTitle", "منصة التمويل الجماعي بالدين الرائدة في السعودية", "ar-SA");
                homePage.SetValue("heroSubtitle", "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. استثمر بأمان واحصل على عوائد تنافسية تصل إلى 18% سنوياً.", "ar-SA");
                homePage.SetValue("heroButtonText", "ابدأ الاستثمار الآن", "ar-SA");

                homePage.SetValue("aboutTitle", "من نحن", "ar-SA");
                homePage.SetValue("aboutText", "<p>مدد بلس هي منصة التمويل الجماعي بالدين الرائدة في المملكة العربية السعودية. نحن نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، مما يتيح للمستثمرين الحصول على عوائد تنافسية بينما ندعم الشركات الصغيرة والمتوسطة في تحقيق أهدافها.</p><p>مرخصة من البنك المركزي السعودي، نحن ملتزمون بأعلى معايير الشفافية والأمان في جميع عملياتنا.</p>", "ar-SA");

                homePage.SetValue("servicesTitle", "خدماتنا", "ar-SA");
                homePage.SetValue("contactTitle", "تواصل معنا", "ar-SA");

                // Set English content
                homePage.SetCultureName("MDD Plus - Home Page", "en-US");
                homePage.SetValue("heroTitle", "Saudi Arabia's Leading Debt Crowdfunding Platform", "en-US");
                homePage.SetValue("heroSubtitle", "Sharia-compliant financing solutions, licensed by the Saudi Central Bank. Invest safely and earn competitive returns up to 18% annually.", "en-US");
                homePage.SetValue("heroButtonText", "Start Investing Now", "en-US");

                homePage.SetValue("aboutTitle", "About Us", "en-US");
                homePage.SetValue("aboutText", "<p>MDD Plus is the leading debt crowdfunding platform in Saudi Arabia. We provide innovative financing solutions that are Sharia-compliant, allowing investors to earn competitive returns while supporting small and medium enterprises in achieving their goals.</p><p>Licensed by the Saudi Central Bank, we are committed to the highest standards of transparency and security in all our operations.</p>", "en-US");

                homePage.SetValue("servicesTitle", "Our Services", "en-US");
                homePage.SetValue("contactTitle", "Contact Us", "en-US");

                // Set statistics (language-neutral)
                homePage.SetValue("totalFunded", "500,000,000");
                homePage.SetValue("annualReturn", "18");
                homePage.SetValue("activeInvestors", "5,000");
                homePage.SetValue("repaymentRate", "99.8");

                // Set contact info
                homePage.SetValue("contactEmail", "<EMAIL>");
                homePage.SetValue("contactPhone", "+966 11 234 5678");

                // Set SEO
                homePage.SetValue("metaTitle", "مدد بلس - منصة التمويل الجماعي بالدين الرائدة في السعودية", "ar-SA");
                homePage.SetValue("metaDescription", "منصة مدد بلس للتمويل الجماعي بالدين - حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. عوائد تصل إلى 18% سنوياً.", "ar-SA");
                homePage.SetValue("metaKeywords", "التمويل الجماعي، الاستثمار، السعودية، الشريعة الإسلامية، مدد بلس", "ar-SA");

                homePage.SetValue("metaTitle", "MDD Plus - Saudi Arabia's Leading Debt Crowdfunding Platform", "en-US");
                homePage.SetValue("metaDescription", "MDD Plus debt crowdfunding platform - Sharia-compliant financing solutions licensed by Saudi Central Bank. Returns up to 18% annually.", "en-US");
                homePage.SetValue("metaKeywords", "crowdfunding, investment, Saudi Arabia, Sharia compliant, MDD Plus", "en-US");

                var saveResult = _contentService.Save(homePage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(homePage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("Home page created and published successfully");
                        return true;
                    }
                }

                _logger.LogError("Failed to save or publish home page");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating home page");
                return false;
            }
        }

        public async Task<bool> CreateAboutPageAsync()
        {
            try
            {
                var contentPageType = _contentTypeService.Get("contentPage");
                if (contentPageType == null)
                {
                    _logger.LogWarning("ContentPage document type not found");
                    return false;
                }

                var homePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (homePage == null)
                {
                    _logger.LogWarning("Home page not found, cannot create about page");
                    return false;
                }

                var aboutPage = _contentService.Create("من نحن", homePage.Id, "contentPage");

                // Set Arabic content
                aboutPage.SetCultureName("من نحن", "ar-SA");
                aboutPage.SetValue("pageTitle", "من نحن", "ar-SA");
                aboutPage.SetValue("pageSubtitle", "تعرف على مدد بلس ورؤيتنا في مجال التمويل الجماعي", "ar-SA");
                aboutPage.SetValue("pageContent", "<h2>رؤيتنا</h2><p>أن نكون المنصة الرائدة في مجال التمويل الجماعي بالدين في المنطقة، نساهم في تمكين الشركات الصغيرة والمتوسطة وتحقيق التنمية الاقتصادية المستدامة.</p><h2>مهمتنا</h2><p>نقدم حلولاً تمويلية مبتكرة ومتوافقة مع الشريعة الإسلامية، تربط بين المستثمرين والشركات بطريقة آمنة وشفافة، مما يحقق عوائد مجزية للمستثمرين ونمواً مستداماً للشركات.</p><h2>قيمنا</h2><ul><li><strong>الشفافية:</strong> نؤمن بالشفافية الكاملة في جميع عملياتنا</li><li><strong>الأمان:</strong> نضع أمان استثمارات عملائنا في المقدمة</li><li><strong>الابتكار:</strong> نسعى دائماً لتطوير حلول مالية مبتكرة</li><li><strong>التوافق الشرعي:</strong> جميع منتجاتنا متوافقة مع أحكام الشريعة الإسلامية</li></ul>", "ar-SA");

                // Set English content
                aboutPage.SetCultureName("About Us", "en-US");
                aboutPage.SetValue("pageTitle", "About Us", "en-US");
                aboutPage.SetValue("pageSubtitle", "Learn about MDD Plus and our vision in crowdfunding", "en-US");
                aboutPage.SetValue("pageContent", "<h2>Our Vision</h2><p>To be the leading debt crowdfunding platform in the region, contributing to empowering small and medium enterprises and achieving sustainable economic development.</p><h2>Our Mission</h2><p>We provide innovative and Sharia-compliant financing solutions that connect investors and companies in a safe and transparent manner, achieving rewarding returns for investors and sustainable growth for companies.</p><h2>Our Values</h2><ul><li><strong>Transparency:</strong> We believe in complete transparency in all our operations</li><li><strong>Security:</strong> We put the security of our clients' investments first</li><li><strong>Innovation:</strong> We always strive to develop innovative financial solutions</li><li><strong>Sharia Compliance:</strong> All our products comply with Islamic law</li></ul>", "en-US");

                var saveResult = _contentService.Save(aboutPage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(aboutPage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("About page created and published successfully");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating about page");
                return false;
            }
        }

        public async Task<bool> CreateServicesPageAsync()
        {
            try
            {
                var contentPageType = _contentTypeService.Get("contentPage");
                if (contentPageType == null)
                {
                    _logger.LogWarning("ContentPage document type not found");
                    return false;
                }

                var homePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (homePage == null)
                {
                    _logger.LogWarning("Home page not found, cannot create services page");
                    return false;
                }

                var servicesPage = _contentService.Create("خدماتنا", homePage.Id, "contentPage");

                // Set Arabic content
                servicesPage.SetCultureName("خدماتنا", "ar-SA");
                servicesPage.SetValue("pageTitle", "خدماتنا", "ar-SA");
                servicesPage.SetValue("pageSubtitle", "تعرف على الحلول التمويلية المبتكرة التي نقدمها", "ar-SA");
                servicesPage.SetValue("pageContent", "<h2>التمويل الجماعي بالدين</h2><p>نقدم منصة متطورة للتمويل الجماعي بالدين تربط بين المستثمرين والشركات الصغيرة والمتوسطة. جميع عملياتنا متوافقة مع أحكام الشريعة الإسلامية ومرخصة من البنك المركزي السعودي.</p><h3>مميزات خدماتنا:</h3><ul><li>عوائد تنافسية تصل إلى 18% سنوياً</li><li>شفافية كاملة في جميع العمليات</li><li>نظام متقدم لإدارة المخاطر</li><li>معدل سداد عالي يصل إلى 99.8%</li><li>دعم فني متخصص على مدار الساعة</li></ul><h2>للمستثمرين</h2><p>استثمر أموالك بطريقة آمنة ومربحة من خلال منصتنا. نوفر لك فرص استثمارية متنوعة مع ضمانات قوية وعوائد مجزية.</p><h2>للشركات</h2><p>احصل على التمويل اللازم لنمو شركتك من خلال شبكة واسعة من المستثمرين. نقدم حلول تمويلية مرنة تناسب احتياجاتك.</p>", "ar-SA");

                // Set English content
                servicesPage.SetCultureName("Our Services", "en-US");
                servicesPage.SetValue("pageTitle", "Our Services", "en-US");
                servicesPage.SetValue("pageSubtitle", "Discover the innovative financing solutions we offer", "en-US");
                servicesPage.SetValue("pageContent", "<h2>Debt Crowdfunding</h2><p>We provide an advanced debt crowdfunding platform that connects investors with small and medium enterprises. All our operations are Sharia-compliant and licensed by the Saudi Central Bank.</p><h3>Service Features:</h3><ul><li>Competitive returns up to 18% annually</li><li>Complete transparency in all operations</li><li>Advanced risk management system</li><li>High repayment rate up to 99.8%</li><li>24/7 specialized technical support</li></ul><h2>For Investors</h2><p>Invest your money safely and profitably through our platform. We provide diverse investment opportunities with strong guarantees and rewarding returns.</p><h2>For Companies</h2><p>Get the financing you need to grow your business through a wide network of investors. We offer flexible financing solutions that suit your needs.</p>", "en-US");

                var saveResult = _contentService.Save(servicesPage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(servicesPage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("Services page created and published successfully");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating services page");
                return false;
            }
        }

        public async Task<bool> CreateContactPageAsync()
        {
            try
            {
                var contactPageType = _contentTypeService.Get("contactPage");
                if (contactPageType == null)
                {
                    _logger.LogWarning("ContactPage document type not found");
                    return false;
                }

                var homePage = _contentService.GetRootContent().FirstOrDefault(x => x.ContentType.Alias == "homePage");
                if (homePage == null)
                {
                    _logger.LogWarning("Home page not found, cannot create contact page");
                    return false;
                }

                var contactPage = _contentService.Create("تواصل معنا", homePage.Id, "contactPage");

                // Set Arabic content
                contactPage.SetCultureName("تواصل معنا", "ar-SA");
                contactPage.SetValue("pageTitle", "تواصل معنا", "ar-SA");
                contactPage.SetValue("pageSubtitle", "نحن هنا لمساعدتك في جميع استفساراتك", "ar-SA");
                contactPage.SetValue("contactIntroduction", "<p>فريقنا المتخصص جاهز لمساعدتك في جميع الأوقات. سواء كنت مستثمراً تبحث عن فرص استثمارية أو شركة تحتاج إلى تمويل، نحن هنا لتقديم الدعم والمشورة اللازمة.</p>", "ar-SA");

                // Set English content
                contactPage.SetCultureName("Contact Us", "en-US");
                contactPage.SetValue("pageTitle", "Contact Us", "en-US");
                contactPage.SetValue("pageSubtitle", "We are here to help you with all your inquiries", "en-US");
                contactPage.SetValue("contactIntroduction", "<p>Our specialized team is ready to help you at all times. Whether you are an investor looking for investment opportunities or a company in need of financing, we are here to provide the necessary support and advice.</p>", "en-US");

                // Set contact information
                contactPage.SetValue("officeAddress", "برج المملكة، الطابق 25\nشارع الملك فهد\nالرياض 12345\nالمملكة العربية السعودية", "ar-SA");
                contactPage.SetValue("phoneNumber", "+966 11 234 5678");
                contactPage.SetValue("emailAddress", "<EMAIL>");
                contactPage.SetValue("whatsappNumber", "+966 50 123 4567");
                contactPage.SetValue("workingHours", "الأحد - الخميس: 9:00 ص - 6:00 م\nالجمعة: مغلق\nالسبت: 10:00 ص - 4:00 م", "ar-SA");

                contactPage.SetValue("officeAddress", "Kingdom Tower, 25th Floor\nKing Fahd Road\nRiyadh 12345\nSaudi Arabia", "en-US");
                contactPage.SetValue("workingHours", "Sunday - Thursday: 9:00 AM - 6:00 PM\nFriday: Closed\nSaturday: 10:00 AM - 4:00 PM", "en-US");

                // Set location coordinates (Riyadh)
                contactPage.SetValue("latitude", "24.7136");
                contactPage.SetValue("longitude", "46.6753");

                var saveResult = _contentService.Save(contactPage);
                if (saveResult.Success)
                {
                    var publishResult = _contentService.Publish(contactPage, new[] { "ar-SA", "en-US" });
                    if (publishResult.Success)
                    {
                        _logger.LogInformation("Contact page created and published successfully");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contact page");
                return false;
            }
        }
    }
}
