using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;
using Umbraco.Extensions;

namespace MDDPlus.Models
{
    public class NewsPage : PublishedContentModel
    {
        public NewsPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Page Content
        public string PageTitle => this.Value<string>("pageTitle") ?? "الأخبار";
        public string PageSubtitle => this.Value<string>("pageSubtitle") ?? "";
        public IEnumerable<IPublishedContent> FeaturedArticles => this.Value<IEnumerable<IPublishedContent>>("featuredArticles") ?? Enumerable.Empty<IPublishedContent>();

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? PageTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? "";
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? "";

        // Helper Methods
        public IEnumerable<IPublishedContent> GetAllArticles()
        {
            return this.Children().Where(x => x.ContentType.Alias == "newsArticle")
                      .OrderByDescending(x => x.Value<DateTime>("publicationDate"));
        }

        public IEnumerable<IPublishedContent> GetRecentArticles(int count = 5)
        {
            return GetAllArticles().Take(count);
        }

        public IEnumerable<IPublishedContent> GetArticlesByTag(string tag)
        {
            return GetAllArticles().Where(x =>
                x.HasValue("tags") &&
                x.Value<string>("tags").Split(',').Any(t => t.Trim().Equals(tag, StringComparison.OrdinalIgnoreCase))
            );
        }
    }
}
