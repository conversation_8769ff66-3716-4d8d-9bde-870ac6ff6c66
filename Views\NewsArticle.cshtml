@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<MDDPlus.Models.NewsArticle>
@{
    Layout = "_Layout";
    ViewBag.Title = Model?.MetaTitle ?? Model?.ArticleTitle ?? "مقال - مدد بلس";
    ViewBag.MetaDescription = Model?.MetaDescription ?? Model?.ArticleSummary ?? "";
}

<!-- Article Header -->
<section class="article-header">
    <div class="container">
        <div class="breadcrumb">
            <a href="/">الرئيسية</a>
            <span>/</span>
            <a href="/news">الأخبار</a>
            <span>/</span>
            <span>@Model.ArticleTitle</span>
        </div>
        <h1>@Model.ArticleTitle</h1>
        @if (!string.IsNullOrEmpty(Model.ArticleSummary))
        {
            <p class="article-lead">@Model.ArticleSummary</p>
        }
        <div class="article-meta">
            @if (Model.PublicationDate.HasValue)
            {
                <span class="date">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    @Model.PublicationDate.Value.ToString("dd MMMM yyyy")
                </span>
            }
            @if (!string.IsNullOrEmpty(Model.Author))
            {
                <span class="author">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    بواسطة @Model.Author
                </span>
            }
        </div>
    </div>
</section>

<!-- Featured Image -->
@if (Model.FeaturedImage != null)
{
    <section class="article-featured-image">
        <div class="container">
            <img src="@Model.FeaturedImage.Url()" alt="@Model.FeaturedImage.Value("altText")" />
        </div>
    </section>
}

<!-- Article Content -->
<section class="article-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="main-content">
                @Html.Raw(Model.ArticleContent)

                @if (Model.Tags.Any())
                {
                    <div class="article-tags">
                        <h4>العلامات:</h4>
                        @foreach (var tag in Model.Tags)
                        {
                            <span class="tag">@tag</span>
                        }
                    </div>
                }

                <!-- Social Sharing -->
                <div class="social-sharing">
                    <h4>شارك المقال:</h4>
                    <div class="social-buttons">
                        <a href="https://twitter.com/intent/tweet?text=@Model.ArticleTitle&url=@Context.Request.GetDisplayUrl()" target="_blank" class="social-btn twitter">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                            تويتر
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=@Context.Request.GetDisplayUrl()" target="_blank" class="social-btn linkedin">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                            لينكد إن
                        </a>
                        <a href="https://wa.me/?text=@Model.ArticleTitle @Context.Request.GetDisplayUrl()" target="_blank" class="social-btn whatsapp">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                            واتساب
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="widget">
                    <h3>أحدث الأخبار</h3>
                    <div class="recent-articles">
                        @foreach (var recentArticle in Model.Parent.Children.Where(x => x.ContentType.Alias == "newsArticle" && x.Id != Model.Id).OrderByDescending(x => x.Value<DateTime>("publicationDate")).Take(5))
                        {
                            <div class="recent-article">
                                @if (recentArticle.HasValue("featuredImage"))
                                {
                                    <div class="recent-article-image">
                                        <img src="@recentArticle.Value("featuredImage")" alt="@recentArticle.Value("articleTitle")" />
                                    </div>
                                }
                                <div class="recent-article-content">
                                    <h4><a href="@recentArticle.Url()">@recentArticle.Value("articleTitle")</a></h4>
                                    @if (recentArticle.HasValue("publicationDate"))
                                    {
                                        <span class="date">@recentArticle.Value<DateTime>("publicationDate").ToString("dd MMM yyyy")</span>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="widget">
                    <h3>تواصل معنا</h3>
                    <div class="contact-widget">
                        <p>هل لديك استفسار حول هذا المقال؟</p>
                        <a href="/contact" class="btn btn-primary">تواصل معنا</a>
                    </div>
                </div>
            </aside>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Article specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
}
