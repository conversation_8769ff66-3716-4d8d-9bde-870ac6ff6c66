# MDD Plus Umbraco Setup Guide

## Step 1: Create Document Types

### 1. HomePage Document Type
1. Go to **Settings** > **Document Types**
2. Click **Create** > **Document Type**
3. Set:
   - **Name**: Home Page
   - **Alias**: homePage
   - **Icon**: icon-home
   - **Allow at root**: ✓
   - **Vary by culture**: ✓

#### Properties for HomePage:
**Hero Section Tab:**
- heroTitle (Textstring) - Vary by culture
- heroSubtitle (Textarea) - Vary by culture  
- heroButtonText (Textstring) - Vary by culture
- heroImage (Media Picker)

**About Section Tab:**
- aboutTitle (Textstring) - Vary by culture
- aboutText (Rich Text Editor) - Vary by culture

**Services Section Tab:**
- servicesTitle (Textstring) - Vary by culture

**Statistics Tab:**
- totalFunded (Textstring)
- annualReturn (Textstring)
- activeInvestors (Textstring)
- repaymentRate (Textstring)

**Contact Section Tab:**
- contactTitle (Textstring) - Vary by culture
- contactEmail (Textstring)
- contactPhone (Textstring)

**SEO Tab:**
- metaTitle (Textstring) - Vary by culture
- metaDescription (Textarea) - Vary by culture
- metaKeywords (Textstring) - Vary by culture

### 2. ContentPage Document Type
1. Create new Document Type:
   - **Name**: Content Page
   - **Alias**: contentPage
   - **Icon**: icon-document
   - **Vary by culture**: ✓

#### Properties for ContentPage:
**Content Tab:**
- pageTitle (Textstring) - Vary by culture, Mandatory
- pageSubtitle (Textarea) - Vary by culture
- pageContent (Rich Text Editor) - Vary by culture
- pageImage (Media Picker)

**Navigation Tab:**
- hideFromNavigation (True/False)
- navigationTitle (Textstring) - Vary by culture

**SEO Tab:**
- metaTitle (Textstring) - Vary by culture
- metaDescription (Textarea) - Vary by culture
- metaKeywords (Textstring) - Vary by culture

### 3. Set Templates
1. Go to **Settings** > **Templates**
2. Templates should already exist: HomePage, ContentPage
3. Make sure they're assigned to the correct Document Types

### 4. Set Allowed Child Content Types
1. Edit HomePage Document Type
2. Go to **Permissions** tab
3. Set **Allowed child node types**: ContentPage

## Step 2: Create Content

### 1. Create Home Page
1. Go to **Content**
2. Click **Create** > **Home Page**
3. Name: "الصفحة الرئيسية" (Arabic) / "Home Page" (English)

#### Fill in Arabic Content (ar-SA):
**Hero Section:**
- Hero Title: "منصة التمويل الجماعي بالدين الرائدة في السعودية"
- Hero Subtitle: "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي. استثمر بأمان واحصل على عوائد تنافسية تصل إلى 18% سنوياً."
- Hero Button Text: "ابدأ الاستثمار الآن"

**About Section:**
- About Title: "من نحن"
- About Text: "مدد بلس هي منصة التمويل الجماعي بالدين الرائدة في المملكة العربية السعودية..."

**Services Section:**
- Services Title: "خدماتنا"

**Statistics:**
- Total Funded: "500,000,000"
- Annual Return: "18"
- Active Investors: "5,000"
- Repayment Rate: "99.8"

**Contact Section:**
- Contact Title: "تواصل معنا"
- Contact Email: "<EMAIL>"
- Contact Phone: "+966 11 234 5678"

#### Fill in English Content (en-US):
**Hero Section:**
- Hero Title: "Saudi Arabia's Leading Debt Crowdfunding Platform"
- Hero Subtitle: "Sharia-compliant financing solutions, licensed by the Saudi Central Bank. Invest safely and earn competitive returns up to 18% annually."
- Hero Button Text: "Start Investing Now"

**About Section:**
- About Title: "About Us"
- About Text: "MDD Plus is the leading debt crowdfunding platform in Saudi Arabia..."

**Services Section:**
- Services Title: "Our Services"

**Contact Section:**
- Contact Title: "Contact Us"

### 2. Create Additional Pages
Create ContentPage instances for:
- About Us (من نحن)
- Services (خدماتنا)  
- Contact (تواصل معنا)

## Step 3: Configure Languages
1. Go to **Settings** > **Languages**
2. Ensure Arabic (ar-SA) and English (en-US) are configured
3. Set Arabic as default if needed

## Step 4: Publish Content
1. Save and Publish all content
2. Test the website at http://localhost:26967

## Current Status
- ✅ Application running on http://localhost:26967
- ✅ Umbraco backoffice available at /umbraco
- ✅ Templates created and working
- ✅ Models and Controllers implemented
- ✅ CSS styling complete
- ⏳ Document Types need to be created manually
- ⏳ Content needs to be added through Umbraco
