using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;
using Umbraco.Extensions;

namespace MDDPlus.Models
{
    public class NewsArticle : PublishedContentModel
    {
        public NewsArticle(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Article Content
        public string ArticleTitle => this.Value<string>("articleTitle") ?? "";
        public string ArticleSummary => this.Value<string>("articleSummary") ?? "";
        public string ArticleContent => this.Value<string>("articleContent") ?? "";
        public IPublishedContent? FeaturedImage => this.Value<IPublishedContent>("featuredImage");
        public DateTime? PublicationDate => this.Value<DateTime?>("publicationDate");
        public string Author => this.Value<string>("author") ?? "";
        public IEnumerable<string> Tags =>
            this.HasValue("tags") ?
            this.Value<string>("tags").Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)) :
            Enumerable.Empty<string>();

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? ArticleTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? ArticleSummary;
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? string.Join(", ", Tags);

        // Helper Methods
        public string GetFormattedDate(string format = "dd MMMM yyyy")
        {
            return PublicationDate?.ToString(format) ?? "";
        }

        public string GetReadingTime()
        {
            if (string.IsNullOrEmpty(ArticleContent))
                return "دقيقة واحدة";

            // Estimate reading time based on word count (average 200 words per minute for Arabic)
            var wordCount = ArticleContent.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
            var readingTime = Math.Max(1, (int)Math.Ceiling(wordCount / 200.0));

            return readingTime == 1 ? "دقيقة واحدة" : $"{readingTime} دقائق";
        }

        public IEnumerable<IPublishedContent> GetRelatedArticles(int count = 3)
        {
            var parent = this.Parent();
            if (parent == null) return Enumerable.Empty<IPublishedContent>();

            var allArticles = parent.Children().Where(x => x.ContentType.Alias == "newsArticle" && x.Id != this.Id);

            // Try to find articles with similar tags first
            if (Tags.Any())
            {
                var relatedByTags = allArticles.Where(x =>
                    x.HasValue("tags") &&
                    x.Value<string>("tags").Split(',').Any(tag =>
                        Tags.Contains(tag.Trim(), StringComparer.OrdinalIgnoreCase)
                    )
                ).Take(count);

                if (relatedByTags.Any())
                    return relatedByTags;
            }

            // Fallback to recent articles
            return allArticles.OrderByDescending(x => x.Value<DateTime>("publicationDate")).Take(count);
        }

        public string GetExcerpt(int maxLength = 150)
        {
            if (string.IsNullOrEmpty(ArticleSummary))
            {
                if (string.IsNullOrEmpty(ArticleContent))
                    return "";

                // Extract text from HTML content
                var plainText = System.Text.RegularExpressions.Regex.Replace(ArticleContent, "<.*?>", "");
                return plainText.Length <= maxLength ? plainText : plainText.Substring(0, maxLength) + "...";
            }

            return ArticleSummary.Length <= maxLength ? ArticleSummary : ArticleSummary.Substring(0, maxLength) + "...";
        }
    }
}
