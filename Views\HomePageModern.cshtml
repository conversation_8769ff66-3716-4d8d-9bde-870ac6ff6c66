@using MDDPlus.Models
@model HomePage
@{
    Layout = "_Layout";
    ViewBag.Title = Model?.MetaTitle ?? "مدد بلس - منصة التمويل الجماعي";
    ViewBag.MetaDescription = Model?.MetaDescription ?? "منصة التمويل الجماعي الرائدة في السعودية";
}

<!-- Hero Section -->
<section class="hero" id="home">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 data-ar="@(Model?.HeroTitle ?? "منصة التمويل الجماعي بالدين الرائدة في السعودية")" 
                    data-en="@(Model?.HeroTitle ?? "Saudi Arabia's Leading Debt Crowdfunding Platform")">@(Model?.HeroTitle ?? "منصة التمويل الجماعي بالدين الرائدة في السعودية")</h1>
                <p data-ar="@(Model?.HeroSubtitle ?? "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي")"
                   data-en="@(Model?.HeroSubtitle ?? "Sharia-compliant financing solutions, licensed by the Saudi Central Bank")">@(Model?.HeroSubtitle ?? "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي")</p>
                <div class="hero-buttons">
                    <a href="#services" class="btn btn-primary">
                        <span data-ar="@(Model?.HeroButtonText ?? "ابدأ الاستثمار الآن")" 
                              data-en="@(Model?.HeroButtonText ?? "Start Investing Now")">@(Model?.HeroButtonText ?? "ابدأ الاستثمار الآن")</span>
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                    </a>
                    <a href="#about" class="btn btn-secondary">
                        <span data-ar="تعرف علينا" data-en="Learn More">تعرف علينا</span>
                    </a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="floating-card" data-animation="float">
                    <div class="stat-icon">💰</div>
                    <div class="stat-number">@(Model?.TotalFunded ?? "500M")</div>
                    <div class="stat-label">إجمالي التمويل</div>
                </div>
                <div class="floating-card" data-animation="float">
                    <div class="stat-icon">📈</div>
                    <div class="stat-number">@(Model?.AnnualReturn ?? "18")%</div>
                    <div class="stat-label">العائد السنوي</div>
                </div>
                <div class="floating-card" data-animation="float">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">@(Model?.ActiveInvestors ?? "5K")+</div>
                    <div class="stat-label">مستثمر نشط</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="statistics" id="stats">
    <div class="container">
        <div class="section-header animate-on-scroll">
            <h2 class="section-title" data-ar="إنجازاتنا بالأرقام" data-en="Our Achievements in Numbers">إنجازاتنا بالأرقام</h2>
            <p class="section-subtitle" data-ar="نفخر بما حققناه من نجاحات في خدمة المستثمرين والشركات" 
               data-en="We are proud of our achievements in serving investors and companies">نفخر بما حققناه من نجاحات في خدمة المستثمرين والشركات</p>
        </div>
        <div class="stats-grid">
            <div class="stat-card animate-on-scroll" data-animation="scaleIn" data-delay="100">
                <div class="stat-icon">💰</div>
                <div class="stat-number">@(Model?.TotalFunded ?? "500000000")</div>
                <div class="stat-label" data-ar="إجمالي التمويل (ريال)" data-en="Total Funding (SAR)">إجمالي التمويل (ريال)</div>
            </div>
            <div class="stat-card animate-on-scroll" data-animation="scaleIn" data-delay="200">
                <div class="stat-icon">📈</div>
                <div class="stat-number">@(Model?.AnnualReturn ?? "18")%</div>
                <div class="stat-label" data-ar="العائد السنوي" data-en="Annual Return">العائد السنوي</div>
            </div>
            <div class="stat-card animate-on-scroll" data-animation="scaleIn" data-delay="300">
                <div class="stat-icon">👥</div>
                <div class="stat-number">@(Model?.ActiveInvestors ?? "5000")</div>
                <div class="stat-label" data-ar="المستثمرون النشطون" data-en="Active Investors">المستثمرون النشطون</div>
            </div>
            <div class="stat-card animate-on-scroll" data-animation="scaleIn" data-delay="400">
                <div class="stat-icon">✅</div>
                <div class="stat-number">@(Model?.RepaymentRate ?? "99.8")%</div>
                <div class="stat-label" data-ar="معدل السداد" data-en="Repayment Rate">معدل السداد</div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="features" id="about">
    <div class="container">
        <div class="section-header animate-on-scroll">
            <h2 class="section-title" data-ar="@(Model?.AboutTitle ?? "من نحن")" 
                data-en="@(Model?.AboutTitle ?? "About Us")">@(Model?.AboutTitle ?? "من نحن")</h2>
            <p class="section-subtitle" data-ar="نحن رواد التمويل الجماعي في المملكة العربية السعودية" 
               data-en="We are the pioneers of crowdfunding in Saudi Arabia">نحن رواد التمويل الجماعي في المملكة العربية السعودية</p>
        </div>
        <div class="features-grid">
            <div class="feature-card animate-on-scroll" data-animation="fadeInLeft" data-delay="100">
                <div class="feature-icon">🏛️</div>
                <h3 class="feature-title" data-ar="مرخص رسمياً" data-en="Officially Licensed">مرخص رسمياً</h3>
                <p class="feature-description" data-ar="مرخص من البنك المركزي السعودي (ساما) وفقاً لأعلى المعايير التنظيمية" 
                   data-en="Licensed by the Saudi Central Bank (SAMA) according to the highest regulatory standards">مرخص من البنك المركزي السعودي (ساما) وفقاً لأعلى المعايير التنظيمية</p>
            </div>
            <div class="feature-card animate-on-scroll" data-animation="fadeInUp" data-delay="200">
                <div class="feature-icon">☪️</div>
                <h3 class="feature-title" data-ar="متوافق مع الشريعة" data-en="Sharia Compliant">متوافق مع الشريعة</h3>
                <p class="feature-description" data-ar="جميع منتجاتنا وخدماتنا متوافقة مع أحكام الشريعة الإسلامية" 
                   data-en="All our products and services comply with Islamic Sharia principles">جميع منتجاتنا وخدماتنا متوافقة مع أحكام الشريعة الإسلامية</p>
            </div>
            <div class="feature-card animate-on-scroll" data-animation="fadeInRight" data-delay="300">
                <div class="feature-icon">🔒</div>
                <h3 class="feature-title" data-ar="أمان وشفافية" data-en="Security & Transparency">أمان وشفافية</h3>
                <p class="feature-description" data-ar="نلتزم بأعلى معايير الأمان والشفافية في جميع عملياتنا" 
                   data-en="We adhere to the highest standards of security and transparency in all our operations">نلتزم بأعلى معايير الأمان والشفافية في جميع عملياتنا</p>
            </div>
        </div>
        @if (!string.IsNullOrEmpty(Model?.AboutText))
        {
            <div class="about-content animate-on-scroll" data-animation="fadeInUp" data-delay="400">
                @Html.Raw(Model.AboutText)
            </div>
        }
    </div>
</section>

<!-- Services Section -->
<section class="services" id="services">
    <div class="container">
        <div class="section-header animate-on-scroll">
            <h2 class="section-title" data-ar="@(Model?.ServicesTitle ?? "خدماتنا")" 
                data-en="@(Model?.ServicesTitle ?? "Our Services")">@(Model?.ServicesTitle ?? "خدماتنا")</h2>
            <p class="section-subtitle" data-ar="نقدم مجموعة شاملة من الحلول التمويلية المبتكرة" 
               data-en="We offer a comprehensive range of innovative financing solutions">نقدم مجموعة شاملة من الحلول التمويلية المبتكرة</p>
        </div>
        <div class="services-grid">
            <div class="service-card animate-on-scroll" data-animation="slideInUp" data-delay="100">
                <div class="service-icon">💼</div>
                <h3 class="service-title" data-ar="تمويل الشركات" data-en="Corporate Financing">تمويل الشركات</h3>
                <p class="service-description" data-ar="حلول تمويلية مرنة للشركات الصغيرة والمتوسطة لتحقيق أهدافها النمو" 
                   data-en="Flexible financing solutions for SMEs to achieve their growth objectives">حلول تمويلية مرنة للشركات الصغيرة والمتوسطة لتحقيق أهدافها النمو</p>
                <a href="#" class="service-link" data-ar="اعرف المزيد" data-en="Learn More">اعرف المزيد →</a>
            </div>
            <div class="service-card animate-on-scroll" data-animation="slideInUp" data-delay="200">
                <div class="service-icon">📊</div>
                <h3 class="service-title" data-ar="الاستثمار الذكي" data-en="Smart Investment">الاستثمار الذكي</h3>
                <p class="service-description" data-ar="فرص استثمارية متنوعة مع عوائد تنافسية وإدارة مخاطر متقدمة" 
                   data-en="Diverse investment opportunities with competitive returns and advanced risk management">فرص استثمارية متنوعة مع عوائد تنافسية وإدارة مخاطر متقدمة</p>
                <a href="#" class="service-link" data-ar="اعرف المزيد" data-en="Learn More">اعرف المزيد →</a>
            </div>
            <div class="service-card animate-on-scroll" data-animation="slideInUp" data-delay="300">
                <div class="service-icon">🛡️</div>
                <h3 class="service-title" data-ar="إدارة المخاطر" data-en="Risk Management">إدارة المخاطر</h3>
                <p class="service-description" data-ar="نظام متطور لتقييم وإدارة المخاطر لضمان أعلى معدلات الأمان" 
                   data-en="Advanced system for risk assessment and management to ensure highest safety rates">نظام متطور لتقييم وإدارة المخاطر لضمان أعلى معدلات الأمان</p>
                <a href="#" class="service-link" data-ar="اعرف المزيد" data-en="Learn More">اعرف المزيد →</a>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact" id="contact">
    <div class="container">
        <div class="contact-content">
            <div class="contact-info animate-on-scroll" data-animation="fadeInLeft">
                <h2 data-ar="@(Model?.ContactTitle ?? "تواصل معنا")" 
                    data-en="@(Model?.ContactTitle ?? "Contact Us")">@(Model?.ContactTitle ?? "تواصل معنا")</h2>
                <p data-ar="نحن هنا لمساعدتك في رحلتك الاستثمارية. تواصل معنا اليوم" 
                   data-en="We are here to help you in your investment journey. Contact us today">نحن هنا لمساعدتك في رحلتك الاستثمارية. تواصل معنا اليوم</p>
                <div class="contact-details">
                    @if (!string.IsNullOrEmpty(Model?.ContactEmail))
                    {
                        <div class="contact-item">
                            <div class="contact-icon">📧</div>
                            <div class="contact-text">
                                <div class="contact-label" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</div>
                                <div class="contact-value">
                                    <a href="mailto:@Model.ContactEmail">@Model.ContactEmail</a>
                                </div>
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model?.ContactPhone))
                    {
                        <div class="contact-item">
                            <div class="contact-icon">📞</div>
                            <div class="contact-text">
                                <div class="contact-label" data-ar="الهاتف" data-en="Phone">الهاتف</div>
                                <div class="contact-value">
                                    <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                                </div>
                            </div>
                        </div>
                    }
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-text">
                            <div class="contact-label" data-ar="العنوان" data-en="Address">العنوان</div>
                            <div class="contact-value" data-ar="الرياض، المملكة العربية السعودية" 
                                 data-en="Riyadh, Saudi Arabia">الرياض، المملكة العربية السعودية</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="contact-form animate-on-scroll" data-animation="fadeInRight">
                <form class="modern-form">
                    <div class="form-group">
                        <input type="text" id="name" required>
                        <label for="name" data-ar="الاسم الكامل" data-en="Full Name">الاسم الكامل</label>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" required>
                        <label for="email" data-ar="البريد الإلكتروني" data-en="Email Address">البريد الإلكتروني</label>
                    </div>
                    <div class="form-group">
                        <input type="tel" id="phone" required>
                        <label for="phone" data-ar="رقم الهاتف" data-en="Phone Number">رقم الهاتف</label>
                    </div>
                    <div class="form-group">
                        <textarea id="message" rows="4" required></textarea>
                        <label for="message" data-ar="الرسالة" data-en="Message">الرسالة</label>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <span data-ar="إرسال الرسالة" data-en="Send Message">إرسال الرسالة</span>
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>
