@using MDDPlus.Web.Models
@model AboutPage
@{
    ViewBag.Title = Model.MetaTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
    ViewBag.ActivePage = "about";
    Layout = "_Layout";
}

<!-- Page Header -->
<section class="hero" style="padding: var(--space-16) 0; background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));">
    <div class="container">
        <div class="text-center">
            <h1 class="animate-fade-in-up">@Model.PageTitle</h1>
            <p class="animate-fade-in-up" style="animation-delay: 0.2s; font-size: var(--text-xl); margin-bottom: 0;">
                @Model.PageSubtitle
            </p>
        </div>
    </div>
</section>

<!-- Company Overview -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="grid grid-cols-2" style="gap: var(--space-12); align-items: center;">
            <div>
                <h2 class="mb-6">@Model.CompanyOverviewTitle</h2>
                <p class="text-lg text-secondary leading-relaxed">
                    @Model.CompanyOverviewContent
                </p>
                <div class="mt-8">
                    <a href="/services" class="btn btn-primary">
                        اكتشف خدماتنا
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="card p-8">
                    <div class="feature-icon mx-auto mb-6" style="width: 80px; height: 80px;">
                        <svg width="40" height="40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <h4 class="mb-4">مرخص من البنك المركزي السعودي</h4>
                    <p class="text-secondary">
                        نعمل تحت رقابة وإشراف البنك المركزي السعودي لضمان أعلى معايير الأمان والامتثال
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Vision & Mission -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>رؤيتنا ومهمتنا</h2>
            <p class="text-xl text-secondary max-w-2xl mx-auto">
                نسعى لتحقيق رؤية طموحة في مجال التمويل الجماعي بالدين
            </p>
        </div>
        
        <div class="grid grid-cols-2" style="gap: var(--space-8);">
            <!-- Vision -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </div>
                    <h3>@Model.VisionTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary">@Model.VisionContent</p>
                </div>
            </div>
            
            <!-- Mission -->
            <div class="card">
                <div class="card-header text-center">
                    <div class="feature-icon mx-auto mb-4">
                        <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                        </svg>
                    </div>
                    <h3>@Model.MissionTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary">@Model.MissionContent</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center mb-16">
            <h2>@Model.ValuesTitle</h2>
            <p class="text-xl text-secondary max-w-3xl mx-auto">
                @Model.ValuesContent
            </p>
        </div>
        
        <div class="grid grid-cols-3" style="gap: var(--space-8);">
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h4>الشفافية</h4>
                <p class="text-secondary">
                    نؤمن بالشفافية الكاملة في جميع تعاملاتنا ونقدم معلومات واضحة ومفصلة
                </p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                </div>
                <h4>الأمانة</h4>
                <p class="text-secondary">
                    نحافظ على أموال عملائنا ومعلوماتهم بأعلى درجات الأمانة والمسؤولية
                </p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                </div>
                <h4>الابتكار</h4>
                <p class="text-secondary">
                    نسعى دائماً لتطوير حلول مبتكرة تلبي احتياجات عملائنا المتطورة
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Licensing & Compliance -->
<section style="background: var(--bg-secondary); padding: var(--space-20) 0;">
    <div class="container">
        <div class="grid grid-cols-2" style="gap: var(--space-12);">
            <!-- Licensing -->
            <div class="card">
                <div class="card-header">
                    <h3>@Model.LicensingTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary mb-6">@Model.LicensingContent</p>
                    <div class="flex items-center" style="gap: var(--space-4);">
                        <img src="/images/sama-logo.png" alt="البنك المركزي السعودي" width="60" height="30">
                        <div>
                            <p class="font-semibold">البنك المركزي السعودي</p>
                            <p class="text-sm text-secondary">ترخيص رقم: 123456</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sharia Compliance -->
            <div class="card">
                <div class="card-header">
                    <h3>@Model.ShariaTitle</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary mb-6">@Model.ShariaContent</p>
                    <div class="flex items-center" style="gap: var(--space-4);">
                        <div class="feature-icon" style="width: 60px; height: 60px;">
                            <svg width="30" height="30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-semibold">معتمد شرعياً</p>
                            <p class="text-sm text-secondary">لجنة شرعية مستقلة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section style="padding: var(--space-20) 0;">
    <div class="container">
        <div class="text-center">
            <h2 class="mb-6">انضم إلى مدد بلس اليوم</h2>
            <p class="text-xl text-secondary mb-8 max-w-2xl mx-auto">
                كن جزءاً من منصة التمويل الجماعي الرائدة في السعودية واستفد من فرص استثمارية متميزة
            </p>
            <div class="flex justify-center" style="gap: var(--space-4);">
                <a href="/register" class="btn btn-primary btn-lg">
                    ابدأ الاستثمار
                </a>
                <a href="/contact" class="btn btn-outline btn-lg">
                    تواصل معنا
                </a>
            </div>
        </div>
    </div>
</section>
