{"openapi": "3.0.4", "info": {"title": "Default API", "description": "All endpoints not defined under specific APIs", "version": "Latest"}, "paths": {"/admin/content": {"get": {"tags": [null], "responses": {"200": {"description": "OK"}}}}, "/admin/content/create": {"post": {"tags": [null], "responses": {"200": {"description": "OK", "headers": {"Umb-Notifications": {"description": "The list of notifications produced during the request.", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationHeaderModel"}, "nullable": true}}}}}}}, "/admin/content/edit/{id}": {"get": {"tags": [null], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": [null], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "HeroSubtitle", "in": "query", "schema": {"type": "string"}}, {"name": "HeroButtonText", "in": "query", "schema": {"type": "string"}}, {"name": "TotalFunded", "in": "query", "schema": {"type": "string"}}, {"name": "AnnualReturn", "in": "query", "schema": {"type": "string"}}, {"name": "Meta<PERSON>itle", "in": "query", "schema": {"type": "string"}}, {"name": "MetaDescription", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "headers": {"Umb-Notifications": {"description": "The list of notifications produced during the request.", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationHeaderModel"}, "nullable": true}}}}}}}, "/api/Language/current": {"get": {"tags": ["v1"], "responses": {"200": {"description": "OK"}}}}, "/api/Language/switch": {"post": {"tags": ["v1"], "requestBody": {"content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/SwitchLanguageRequest"}]}}, "text/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/SwitchLanguageRequest"}]}}, "application/*+json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/SwitchLanguageRequest"}]}}}}, "responses": {"200": {"description": "OK", "headers": {"Umb-Notifications": {"description": "The list of notifications produced during the request.", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationHeaderModel"}, "nullable": true}}}}}}}}, "components": {"schemas": {"EventMessageTypeModel": {"enum": ["<PERSON><PERSON><PERSON>", "Info", "Error", "Success", "Warning"], "type": "string"}, "NotificationHeaderModel": {"required": ["category", "message", "type"], "type": "object", "properties": {"message": {"type": "string"}, "category": {"type": "string"}, "type": {"$ref": "#/components/schemas/EventMessageTypeModel"}}, "additionalProperties": false}, "SwitchLanguageRequest": {"required": ["language", "returnUrl"], "type": "object", "properties": {"language": {"type": "string"}, "returnUrl": {"type": "string"}}, "additionalProperties": false}}, "securitySchemes": {"Backoffice User": {"type": "oauth2", "description": "Umbraco Authentication", "flows": {"authorizationCode": {"authorizationUrl": "/umbraco/management/api/v1/security/back-office/authorize", "tokenUrl": "/umbraco/management/api/v1/security/back-office/token", "scopes": {}}}}}}}