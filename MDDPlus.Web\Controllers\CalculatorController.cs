using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Web.Models;
using System.Text.Json;

namespace MDDPlus.Web.Controllers
{
    public class CalculatorController : UmbracoPageController
    {
        private readonly ILogger<CalculatorController> _logger;

        public CalculatorController(ILogger<CalculatorController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
            _logger = logger;
        }

        public IActionResult Index(CalculatorPage model)
        {
            // Set language and direction based on current culture
            var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
            ViewBag.Language = currentCulture.Name.StartsWith("ar") ? "ar" : "en";
            ViewBag.Direction = currentCulture.Name.StartsWith("ar") ? "rtl" : "ltr";
            ViewBag.ActivePage = "calculator";

            // Set page-specific meta data
            ViewBag.Title = model.MetaTitle;
            ViewBag.MetaDescription = model.MetaDescription;

            return View(model);
        }

        [HttpPost]
        public IActionResult CalculateReturns([FromBody] InvestmentCalculationRequest request)
        {
            try
            {
                var result = CalculateInvestmentReturns(
                    request.InvestmentAmount,
                    request.AnnualReturnRate,
                    request.InvestmentPeriodMonths
                );

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating investment returns");
                return BadRequest(new { error = "حدث خطأ في حساب العوائد" });
            }
        }

        private InvestmentCalculationResult CalculateInvestmentReturns(decimal amount, decimal annualRate, int months)
        {
            var monthlyRate = annualRate / 100 / 12;
            var totalReturn = amount * (decimal)Math.Pow((double)(1 + monthlyRate), months);
            var profit = totalReturn - amount;
            var monthlyProfit = profit / months;

            return new InvestmentCalculationResult
            {
                InitialAmount = amount,
                TotalReturn = Math.Round(totalReturn, 2),
                TotalProfit = Math.Round(profit, 2),
                MonthlyProfit = Math.Round(monthlyProfit, 2),
                AnnualReturnRate = annualRate,
                InvestmentPeriodMonths = months,
                CalculatedAt = DateTime.Now
            };
        }
    }

    public class InvestmentCalculationRequest
    {
        public decimal InvestmentAmount { get; set; }
        public decimal AnnualReturnRate { get; set; }
        public int InvestmentPeriodMonths { get; set; }
    }

    public class InvestmentCalculationResult
    {
        public decimal InitialAmount { get; set; }
        public decimal TotalReturn { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal MonthlyProfit { get; set; }
        public decimal AnnualReturnRate { get; set; }
        public int InvestmentPeriodMonths { get; set; }
        public DateTime CalculatedAt { get; set; }
    }
}
