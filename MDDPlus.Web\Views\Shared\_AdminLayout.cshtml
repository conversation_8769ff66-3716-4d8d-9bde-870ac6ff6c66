<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@(ViewBag.Title ?? "إدارة المحتوى - مدد بلس")</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/mdd-fintech.css">
    
    <style>
        body {
            background: var(--bg-secondary);
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        }
        
        .admin-nav {
            background: var(--primary-color);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .admin-nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .admin-nav h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .admin-nav-links {
            display: flex;
            gap: 2rem;
        }
        
        .admin-nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }
        
        .admin-nav-links a:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .card-header h3 {
            margin: 0;
            color: var(--primary-color);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <nav class="admin-nav">
        <div class="container">
            <h1>إدارة المحتوى - مدد بلس</h1>
            <div class="admin-nav-links">
                <a href="/admin/content">إدارة المحتوى</a>
                <a href="/umbraco" target="_blank">Umbraco</a>
                <a href="/" target="_blank">الموقع الرئيسي</a>
            </div>
        </div>
    </nav>

    <main>
        @RenderBody()
    </main>

    <script src="/js/mdd-fintech.js"></script>
</body>
</html>
