using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Models
{
    /// <summary>
    /// Generic Content Page for MDD Plus
    /// </summary>
    public class ContentPage : PublishedContentModel
    {
        public ContentPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) 
            : base(content, publishedValueFallback) { }

        // Page Content
        public string PageTitle => this.Value<string>("pageTitle") ?? this.Name;
        public string PageSubtitle => this.Value<string>("pageSubtitle") ?? "";
        public string PageContent => this.Value<string>("pageContent") ?? "";
        public IPublishedContent? PageImage => this.Value<IPublishedContent>("pageImage");

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? PageTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? PageSubtitle;
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? "";

        // Navigation
        public bool HideFromNavigation => this.Value<bool>("hideFromNavigation");
        public string NavigationTitle => this.Value<string>("navigationTitle") ?? PageTitle;
    }
}
