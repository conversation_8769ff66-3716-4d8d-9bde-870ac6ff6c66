{"@t":"2025-07-17T10:35:24.9292154Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:35:24.9345579Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:35:24.9965042Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:26.0108890Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:27.0214143Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:28.0298686Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:29.0388813Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:29.0657724Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.DirectoryNotFoundException: D:\\project\\mdd_plus\\MDDPlus.Web\\wwwroot\\media\\\r\n   at Microsoft.Extensions.FileProviders.PhysicalFileProvider..ctor(String root, ExclusionFilters filters)\r\n   at Umbraco.Cms.Core.IO.PhysicalFileSystem.Create()\r\n   at Umbraco.Extensions.FileSystemExtensions.TryCreateFileProvider(IFileSystem fileSystem, IFileProvider& fileProvider)\r\n   at Umbraco.Cms.Core.IO.ShadowWrapper.Create()\r\n   at Umbraco.Extensions.FileSystemExtensions.TryCreateFileProvider(IFileSystem fileSystem, IFileProvider& fileProvider)\r\n   at Umbraco.Extensions.ApplicationBuilderExtensions.UseUmbracoMediaFileProvider(IApplicationBuilder app)\r\n   at Umbraco.Cms.Web.Common.ApplicationBuilder.UmbracoApplicationBuilder.RegisterDefaultRequiredMiddleware()\r\n   at Umbraco.Cms.Web.Common.ApplicationBuilder.UmbracoApplicationBuilder.WithMiddleware(Action`1 configureUmbracoMiddleware)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 96\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:36:41.6700490Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:41.6751023Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:41.6958326Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:36:42.7043754Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:36:43.7157298Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:36:44.7225221Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:36:45.7300449Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:36:46.2597036Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2608477Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2633156Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2635651Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2637150Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2638487Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2639632Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2640625Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2641948Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2643890Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2645228Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2646416Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2647799Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.2648289Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.3162938Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.3164038Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.3164310Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:36:46.3164385Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:37:38.9149353Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"853cf320ae1711677fdbfec0859629b6","@sp":"8df6dcd44eda46ad","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNE53NPQ9S6C:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6C","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":7,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:37:38.9448536Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"853cf320ae1711677fdbfec0859629b6","@sp":"8df6dcd44eda46ad","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6C:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6C","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":7,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:37:45.6812120Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"71d1433719771488a348bdd570b6270e","@sp":"5d6b6df279b19c12","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6D:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6D","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":7,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:42:09.7917219Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"ea9c7df5bd4b0e5b8b071236877a0888","@sp":"06f759539685015c","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6F:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6F","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":23,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:42:56.0217463Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"d9b723e6a6892e379e472ca2ec592417","@sp":"31a290b2d11acbea","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6H:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6H","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":28,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:43:34.8909864Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"3ed912c263e6dd90a10d442d787c6dc4","@sp":"954307a2c293daff","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6F:00000002","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6F","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":25,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:45:12.6101300Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.ResponseCompression.ResponseCompressionMiddleware.InvokeCore(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"fffdc413ec676d6cc8aa2a9b014e8d67","@sp":"ebb911406ab2a2a6","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6J:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6J","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":31,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T10:46:30.0762926Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Program.<>c.<<<Main>$>b__0_3>d.MoveNext() in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 86\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"332227730bac33f10939d3ea8cba3a09","@sp":"43b177e8baaed5b6","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE53NPQ9S6L:00000001","RequestPath":"/","ConnectionId":"0HNE53NPQ9S6L","ProcessId":31436,"ProcessName":"MDDPlus.Web","ThreadId":35,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:04:05.7385279Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:05.7511818Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:05.7920012Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:04:06.7973467Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:04:07.8042726Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:04:08.8082890Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:04:09.8164574Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:04:10.3882610Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3892473Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3923519Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3929661Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3935421Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3939948Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3944045Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3948232Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3952542Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3957277Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3962991Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3967408Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3971694Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.3972350Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.5299853Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.5300327Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.5851890Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.5852437Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:10.5852568Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:04:11.2592932Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"e32d0f87e3b2bdab396ac3a89f500ccd","@sp":"efd804e235747187","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE546K6MI72:00000001","RequestPath":"/","ConnectionId":"0HNE546K6MI72","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":19,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:04:25.9068877Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"216b7d0bc909ff2584de03f4a8ab18cc","@sp":"25a8baae50455494","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE546K6MI72:00000009","RequestPath":"/","ConnectionId":"0HNE546K6MI72","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:04:36.5505567Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"728b9986e1dd38a979c88632e11e0b29","@sp":"3d3acecf3ab190e0","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE546K6MI72:0000000F","RequestPath":"/umbraco","ConnectionId":"0HNE546K6MI72","ProcessId":18384,"ProcessName":"MDDPlus.Web","ThreadId":11,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:06:11.5072847Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:11.5180961Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:11.6103601Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:06:12.6213476Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:06:13.6309379Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:06:14.6441622Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:06:15.6557520Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:06:16.2389372Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2398425Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2424822Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2429776Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2433587Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2437348Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2441498Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2445236Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2449091Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2453902Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2459195Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2464473Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2468694Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.2469187Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.3753164Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.3753598Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.4311182Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.4311648Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.4311777Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:06:16.8910230Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"09cf993af1ea4da78114498b706a4ff8","@sp":"a52aa9806832ffbf","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE547PMJ7V3:00000001","RequestPath":"/","ConnectionId":"0HNE547PMJ7V3","ProcessId":11080,"ProcessName":"MDDPlus.Web","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:22:01.1857610Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:01.1950917Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:01.2992981Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:02.3119671Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:03.3185223Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:04.3318101Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:05.3405625Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:05.8465554Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8474276Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8498239Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8503748Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8507481Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8511850Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8517378Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8521183Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8524946Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8529440Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8533668Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8537550Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8541141Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:05.8541634Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.0730679Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.0731029Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.1628279Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.1628893Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.1629016Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:06.5216394Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"e3bbe0645cfe2480ae3a4eb307312c9c","@sp":"7fa60cad7a37e911","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE54GKMGD4E:00000001","RequestPath":"/","ConnectionId":"0HNE54GKMGD4E","ProcessId":8252,"ProcessName":"MDDPlus.Web","ThreadId":11,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:22:58.7864244Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:58.7992817Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:22:58.8407247Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:22:59.8491596Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:00.8533324Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:01.8653311Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:02.8741759Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:03.4142849Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4152915Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4183275Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4190738Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4195251Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4200181Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4204348Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4208901Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4213200Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4219511Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4225588Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4230377Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4235944Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.4236858Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.7592675Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"21a46d03202add97037451d1b2cc0bab","@sp":"86db09a73f510ef9","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000015-0011-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":5,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-17T11:23:03.8275257Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.8275708Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:03.8275816Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":39272,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:33.5213696Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:33.5326708Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:33.5728811Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:34.5771573Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:35.5855086Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:36.5942333Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:37.6054900Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T11:23:38.2014396Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2024272Z","@mt":"Starting background hosted service for {job}","job":"OpenIddictCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2054251Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2060414Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2065235Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2069498Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2073848Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2078056Z","@mt":"Starting background hosted service for {job}","job":"TemporaryFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2082058Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2086680Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2090854Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2094986Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2099206Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.2099805Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.3336922Z","@mt":"Now listening on: {address}","address":"https://localhost:44387","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.3337323Z","@mt":"Now listening on: {address}","address":"http://localhost:52607","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.3958923Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.3959411Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.3959535Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\project\\mdd_plus\\MDDPlus.Web","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T11:23:38.7950071Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Core.Exceptions.BootFailedException: Boot failed: Umbraco cannot run. See Umbraco's log file for more details.\r\n   at Umbraco.Cms.Core.Exceptions.BootFailedException.Rethrow(BootFailedException bootFailedException)\r\n   at Umbraco.Cms.Web.Common.Middleware.BootFailedMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Umbraco.Cms.Api.Management.Middleware.BackOfficeAuthorizationInitializationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"6db332635f1673e350467a08cd5087b3","@sp":"e244fcafdbb3f177","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNE54HG6S6RL:00000001","RequestPath":"/","ConnectionId":"0HNE54HG6S6RL","ProcessId":18504,"ProcessName":"MDDPlus.Web","ThreadId":10,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
