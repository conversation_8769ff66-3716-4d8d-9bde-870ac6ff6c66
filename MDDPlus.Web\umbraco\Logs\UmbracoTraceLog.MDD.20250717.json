{"@t":"2025-07-17T10:35:24.9292154Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:35:24.9345579Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T10:35:24.9965042Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:26.0108890Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:27.0214143Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:28.0298686Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:29.0388813Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T10:35:29.0657724Z","@mt":"Unhandled exception in AppDomain (terminating).","@l":"Error","@x":"System.IO.DirectoryNotFoundException: D:\\project\\mdd_plus\\MDDPlus.Web\\wwwroot\\media\\\r\n   at Microsoft.Extensions.FileProviders.PhysicalFileProvider..ctor(String root, ExclusionFilters filters)\r\n   at Umbraco.Cms.Core.IO.PhysicalFileSystem.Create()\r\n   at Umbraco.Extensions.FileSystemExtensions.TryCreateFileProvider(IFileSystem fileSystem, IFileProvider& fileProvider)\r\n   at Umbraco.Cms.Core.IO.ShadowWrapper.Create()\r\n   at Umbraco.Extensions.FileSystemExtensions.TryCreateFileProvider(IFileSystem fileSystem, IFileProvider& fileProvider)\r\n   at Umbraco.Extensions.ApplicationBuilderExtensions.UseUmbracoMediaFileProvider(IApplicationBuilder app)\r\n   at Umbraco.Cms.Web.Common.ApplicationBuilder.UmbracoApplicationBuilder.RegisterDefaultRequiredMiddleware()\r\n   at Umbraco.Cms.Web.Common.ApplicationBuilder.UmbracoApplicationBuilder.WithMiddleware(Action`1 configureUmbracoMiddleware)\r\n   at Program.<Main>$(String[] args) in D:\\project\\mdd_plus\\MDDPlus.Web\\Program.cs:line 96\r\n   at Program.<Main>(String[] args)","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.CoreRuntime","ProcessId":42480,"ProcessName":"MDDPlus.Web","ThreadId":1,"ApplicationId":"8a63cc4d8a31426254dbf7e5b0d52affb5dce5c3","MachineName":"MDD","Log4NetLevel":"ERROR"}
