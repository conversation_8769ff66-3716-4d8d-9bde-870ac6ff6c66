using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// Home Page Document Type for MDD Plus Fintech Website
    /// </summary>
    public class HomePage : PublishedContentModel
    {
        public HomePage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Hero Section
        public string HeroTitle => this.Value<string>("heroTitle") ?? "منصة التمويل الجماعي بالدين الرائدة في السعودية";
        public string HeroSubtitle => this.Value<string>("heroSubtitle") ?? "حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي";
        public string HeroButtonText => this.Value<string>("heroButtonText") ?? "ابدأ الاستثمار الآن";
        public string HeroButtonUrl => this.Value<string>("heroButtonUrl") ?? "/register";
        public IPublishedContent? HeroImage => this.Value<IPublishedContent>("heroImage");

        // Statistics Section
        public string TotalFunded => this.Value<string>("totalFunded") ?? "500000000";
        public string AnnualReturn => this.Value<string>("annualReturn") ?? "18";
        public string ActiveInvestors => this.Value<string>("activeInvestors") ?? "5000";
        public string RepaymentRate => this.Value<string>("repaymentRate") ?? "99.8";

        // Services Section
        public string ServicesTitle => this.Value<string>("servicesTitle") ?? "خدماتنا المتميزة";
        public string ServicesDescription => this.Value<string>("servicesDescription") ?? "نقدم حلول تمويلية شاملة للأفراد والشركات";

        // Features Section
        public string FeaturesTitle => this.Value<string>("featuresTitle") ?? "لماذا مدد بلس؟";
        public string FeaturesDescription => this.Value<string>("featuresDescription") ?? "نجمع بين الخبرة المالية والتقنيات الحديثة";

        // CTA Section
        public string CtaTitle => this.Value<string>("ctaTitle") ?? "ابدأ رحلتك الاستثمارية اليوم";
        public string CtaDescription => this.Value<string>("ctaDescription") ?? "انضم إلى آلاف المستثمرين الذين يثقون في مدد بلس";
        public string CtaPrimaryButtonText => this.Value<string>("ctaPrimaryButtonText") ?? "إنشاء حساب مجاني";
        public string CtaPrimaryButtonUrl => this.Value<string>("ctaPrimaryButtonUrl") ?? "/register";
        public string CtaSecondaryButtonText => this.Value<string>("ctaSecondaryButtonText") ?? "احسب عوائدك";
        public string CtaSecondaryButtonUrl => this.Value<string>("ctaSecondaryButtonUrl") ?? "/calculator";

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? "مدد بلس - منصة التمويل الجماعي بالدين الرائدة في السعودية";
        public string MetaDescription => this.Value<string>("metaDescription") ?? "منصة مدد بلس للتمويل الجماعي بالدين - حلول تمويلية متوافقة مع الشريعة الإسلامية، مرخصة من البنك المركزي السعودي";
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? "التمويل الجماعي, الدين, الشريعة الإسلامية, السعودية, الاستثمار";
    }
}
