using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// Calculator Page Document Type for MDD Plus Investment Calculator
    /// </summary>
    public class CalculatorPage : PublishedContentModel
    {
        public CalculatorPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Calculator Settings
        public string CalculatorTitle => this.Value<string>("calculatorTitle") ?? "حاسبة العوائد الاستثمارية";
        public string CalculatorDescription => this.Value<string>("calculatorDescription") ?? "احسب العوائد المتوقعة من استثماراتك مع مدد بلس";
        
        // Investment Parameters
        public decimal MinInvestmentAmount => this.Value<decimal>("minInvestmentAmount", fallback: Fallback.ToDefaultValue, defaultValue: 1000);
        public decimal MaxInvestmentAmount => this.Value<decimal>("maxInvestmentAmount", fallback: Fallback.ToDefaultValue, defaultValue: 1000000);
        public decimal DefaultAnnualReturn => this.Value<decimal>("defaultAnnualReturn", fallback: Fallback.ToDefaultValue, defaultValue: 15.5m);
        public int MinInvestmentPeriod => this.Value<int>("minInvestmentPeriod", fallback: Fallback.ToDefaultValue, defaultValue: 3);
        public int MaxInvestmentPeriod => this.Value<int>("maxInvestmentPeriod", fallback: Fallback.ToDefaultValue, defaultValue: 36);
        
        // Risk Information
        public string RiskDisclaimer => this.Value<string>("riskDisclaimer") ?? "الاستثمار ينطوي على مخاطر وقد تنخفض قيمة الاستثمارات";
        public string ComplianceNote => this.Value<string>("complianceNote") ?? "جميع الاستثمارات متوافقة مع أحكام الشريعة الإسلامية";
        
        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? "حاسبة العوائد الاستثمارية - مدد بلس";
        public string MetaDescription => this.Value<string>("metaDescription") ?? "احسب العوائد المتوقعة من استثماراتك مع مدد بلس. حاسبة دقيقة للاستثمارات المتوافقة مع الشريعة";
    }
}
