{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "MDDPlus", "SanitizeTinyMce": true}, "Content": {"AllowEditInvariantFromNonDefault": true}}}, "ConnectionStrings": {"umbracoDbDSN": "Data Source=App_Data/Umbraco.sqlite.db;Cache=Shared;Foreign Keys=True;Pooling=True", "umbracoDbDSN_ProviderName": "Microsoft.Data.Sqlite"}}