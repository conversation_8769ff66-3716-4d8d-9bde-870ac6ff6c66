@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage<MDDPlus.Models.NewsPage>
@{
    Layout = "_Layout";
    ViewBag.Title = Model?.MetaTitle ?? Model?.PageTitle ?? "الأخبار - مدد بلس";
    ViewBag.MetaDescription = Model?.MetaDescription ?? Model?.PageSubtitle ?? "";
}

<!-- News Page -->
<section class="news-page" style="padding-top: 120px;">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header animate-on-scroll" data-animation="fadeInUp">
            <h1 class="page-title">@(Model?.PageTitle ?? "الأخبار والمقالات")</h1>
            @if (!string.IsNullOrEmpty(Model?.PageSubtitle))
            {
                <p class="page-subtitle">@Model.PageSubtitle</p>
            }
            else
            {
                <p class="page-subtitle" data-ar="آخر الأخبار والتحديثات من مدد بلس" data-en="Latest news and updates from MDD Plus">آخر الأخبار والتحديثات من مدد بلس</p>
            }
        </div>

        <!-- Featured Articles -->
        @if (Model?.FeaturedArticles?.Any() == true)
        {
            var featuredArticle = Model.FeaturedArticles.First();
            <div class="featured-article animate-on-scroll" data-animation="scaleIn" data-delay="200">
                <div class="featured-card">
                    @if (featuredArticle.HasValue("featuredImage"))
                    {
                        <div class="featured-image">
                            <img src="@featuredArticle.Value("featuredImage")" alt="@featuredArticle.Value("articleTitle")">
                            <div class="featured-badge">
                                <span data-ar="مميز" data-en="Featured">مميز</span>
                            </div>
                        </div>
                    }
                    <div class="featured-content">
                        <div class="article-meta">
                            @if (featuredArticle.HasValue("publicationDate"))
                            {
                                <span class="article-date">@featuredArticle.Value<DateTime>("publicationDate").ToString("dd MMMM yyyy")</span>
                            }
                            @if (featuredArticle.HasValue("author"))
                            {
                                <span class="article-author">@featuredArticle.Value("author")</span>
                            }
                        </div>
                        <h2 class="featured-title">
                            <a href="@featuredArticle.Url()">@featuredArticle.Value("articleTitle")</a>
                        </h2>
                        @if (featuredArticle.HasValue("articleSummary"))
                        {
                            <p class="featured-summary">@featuredArticle.Value("articleSummary")</p>
                        }
                        <a href="@featuredArticle.Url()" class="btn btn-primary">
                            <span data-ar="اقرأ المزيد" data-en="Read More">اقرأ المزيد</span>
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        }

        <!-- Articles Grid -->
        <div class="articles-section animate-on-scroll" data-animation="fadeInUp" data-delay="400">
            <div class="section-header">
                <h3 data-ar="جميع المقالات" data-en="All Articles">جميع المقالات</h3>
                <div class="articles-filter">
                    <button class="filter-btn active" data-filter="all" data-ar="الكل" data-en="All">الكل</button>
                    <button class="filter-btn" data-filter="investment" data-ar="الاستثمار" data-en="Investment">الاستثمار</button>
                    <button class="filter-btn" data-filter="fintech" data-ar="التكنولوجيا المالية" data-en="Fintech">التكنولوجيا المالية</button>
                    <button class="filter-btn" data-filter="regulations" data-ar="التنظيمات" data-en="Regulations">التنظيمات</button>
                </div>
            </div>

            <div class="articles-grid">
                @{
                    var articles = Model?.Children?.Where(x => x.ContentType.Alias == "newsArticle")
                        .OrderByDescending(x => x.Value<DateTime?>("publicationDate") ?? DateTime.MinValue);
                }
                @if (articles?.Any() == true)
                {
                    var delay = 100;
                    foreach (var article in articles)
                    {
                        <article class="article-card animate-on-scroll" data-animation="slideInUp" data-delay="@delay">
                            @if (article.HasValue("featuredImage"))
                            {
                                <div class="article-image">
                                    <img src="@article.Value("featuredImage")" alt="@article.Value("articleTitle")">
                                    <div class="article-overlay">
                                        <a href="@article.Url()" class="read-more-btn">
                                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            }
                            <div class="article-content">
                                <div class="article-meta">
                                    @if (article.HasValue("publicationDate"))
                                    {
                                        <span class="article-date">@article.Value<DateTime>("publicationDate").ToString("dd MMM yyyy")</span>
                                    }
                                    @if (article.HasValue("tags") && !string.IsNullOrEmpty(article.Value<string>("tags")))
                                    {
                                        var tags = article.Value<string>("tags").Split(',');
                                        if (tags.Length > 0)
                                        {
                                            <span class="article-tag">@tags[0].Trim()</span>
                                        }
                                    }
                                </div>
                                <h4 class="article-title">
                                    <a href="@article.Url()">@article.Value("articleTitle")</a>
                                </h4>
                                @if (article.HasValue("articleSummary"))
                                {
                                    <p class="article-summary">@article.Value("articleSummary")</p>
                                }
                                <div class="article-footer">
                                    <span class="reading-time">5 دقائق قراءة</span>
                                    @if (article.HasValue("author"))
                                    {
                                        <span class="article-author">@article.Value("author")</span>
                                    }
                                </div>
                            </div>
                        </article>
                        delay += 100;
                    }
                }
                else
                {
                    <div class="no-articles">
                        <div class="no-articles-icon">📰</div>
                        <h4 data-ar="لا توجد مقالات حالياً" data-en="No Articles Yet">لا توجد مقالات حالياً</h4>
                        <p data-ar="نعمل على إضافة مقالات ومحتوى جديد قريباً" data-en="We're working on adding new articles and content soon">نعمل على إضافة مقالات ومحتوى جديد قريباً</p>
                    </div>
                }
            </div>
        </div>

        <!-- Newsletter Signup -->
        <div class="newsletter-section animate-on-scroll" data-animation="fadeInUp" data-delay="600">
            <div class="newsletter-card">
                <div class="newsletter-content">
                    <h3 data-ar="اشترك في النشرة الإخبارية" data-en="Subscribe to Newsletter">اشترك في النشرة الإخبارية</h3>
                    <p data-ar="احصل على آخر الأخبار والتحديثات مباشرة في بريدك الإلكتروني" data-en="Get the latest news and updates directly in your email">احصل على آخر الأخبار والتحديثات مباشرة في بريدك الإلكتروني</p>
                </div>
                <form class="newsletter-form">
                    <div class="form-group">
                        <input type="email" placeholder="البريد الإلكتروني" required>
                        <button type="submit" class="btn btn-primary">
                            <span data-ar="اشتراك" data-en="Subscribe">اشتراك</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<style>
.news-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--gray-50), var(--white));
}

.page-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
    padding: var(--space-2xl) 0;
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.featured-article {
    margin-bottom: var(--space-3xl);
}

.featured-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
    min-height: 400px;
}

.featured-image {
    position: relative;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.featured-card:hover .featured-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: var(--space-lg);
    right: var(--space-lg);
    background: var(--accent-color);
    color: var(--white);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
}

.featured-content {
    padding: var(--space-2xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.article-meta {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
    font-size: 0.875rem;
    color: var(--gray-500);
}

.featured-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-lg);
    line-height: 1.3;
}

.featured-title a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.featured-title a:hover {
    color: var(--secondary-color);
}

.featured-summary {
    font-size: 1.1rem;
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--space-xl);
}

.articles-section {
    margin-bottom: var(--space-3xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2xl);
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.section-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.articles-filter {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--space-sm) var(--space-lg);
    border: 2px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
}

.article-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.article-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.article-card:hover .article-image img {
    transform: scale(1.1);
}

.article-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.article-card:hover .article-overlay {
    opacity: 1;
}

.read-more-btn {
    background: var(--white);
    color: var(--primary-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.read-more-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.article-content {
    padding: var(--space-xl);
}

.article-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
    line-height: 1.4;
}

.article-title a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.article-title a:hover {
    color: var(--secondary-color);
}

.article-summary {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.article-tag {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.no-articles {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--space-3xl);
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
}

.no-articles-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
}

.no-articles h4 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
}

.no-articles p {
    color: var(--gray-600);
}

.newsletter-section {
    margin-top: var(--space-3xl);
}

.newsletter-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: var(--space-3xl);
    border-radius: var(--radius-2xl);
    text-align: center;
}

.newsletter-content h3 {
    font-size: 2rem;
    margin-bottom: var(--space-md);
}

.newsletter-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: var(--space-xl);
}

.newsletter-form .form-group {
    display: flex;
    max-width: 400px;
    margin: 0 auto;
    gap: var(--space-md);
}

.newsletter-form input {
    flex: 1;
    padding: var(--space-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
}

.newsletter-form button {
    white-space: nowrap;
}

@media (max-width: 768px) {
    .featured-card {
        grid-template-columns: 1fr;
    }
    
    .featured-content {
        padding: var(--space-xl);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
    }
}
</style>

@section Scripts {
<script>
    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Get filter value
                const filter = this.getAttribute('data-filter');
                
                // Filter articles logic would go here
                console.log('Filter by:', filter);
            });
        });
    });
</script>
}
