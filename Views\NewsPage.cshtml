@using MDDPlus.Models
@model NewsPage
@{
    Layout = "_Layout";
    ViewBag.Title = Model.MetaTitle ?? Model.PageTitle;
    ViewBag.MetaDescription = Model.MetaDescription;
}

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>@Model.PageTitle</h1>
        @if (!string.IsNullOrEmpty(Model.PageSubtitle))
        {
            <p class="page-subtitle">@Model.PageSubtitle</p>
        }
    </div>
</section>

<!-- Featured Articles -->
@if (Model.FeaturedArticles.Any())
{
    <section class="featured-articles">
        <div class="container">
            <h2>الأخبار المميزة</h2>
            <div class="articles-grid featured">
                @foreach (var article in Model.FeaturedArticles.Take(3))
                {
                    <article class="article-card featured">
                        @if (article.HasValue("featuredImage"))
                        {
                            <div class="article-image">
                                <img src="@article.Value("featuredImage")" alt="@article.Value("articleTitle")" />
                            </div>
                        }
                        <div class="article-content">
                            <h3><a href="@article.Url()">@article.Value("articleTitle")</a></h3>
                            @if (article.HasValue("articleSummary"))
                            {
                                <p class="article-summary">@article.Value("articleSummary")</p>
                            }
                            <div class="article-meta">
                                @if (article.HasValue("publicationDate"))
                                {
                                    <span class="date">@article.Value<DateTime>("publicationDate").ToString("dd MMMM yyyy")</span>
                                }
                                @if (article.HasValue("author"))
                                {
                                    <span class="author">بواسطة @article.Value("author")</span>
                                }
                            </div>
                            <a href="@article.Url()" class="read-more">اقرأ المزيد</a>
                        </div>
                    </article>
                }
            </div>
        </div>
    </section>
}

<!-- All Articles -->
<section class="all-articles">
    <div class="container">
        <h2>جميع الأخبار</h2>
        <div class="articles-grid">
            @foreach (var article in Model.Children.Where(x => x.ContentType.Alias == "newsArticle").OrderByDescending(x => x.Value<DateTime>("publicationDate")))
            {
                <article class="article-card">
                    @if (article.HasValue("featuredImage"))
                    {
                        <div class="article-image">
                            <img src="@article.Value("featuredImage")" alt="@article.Value("articleTitle")" />
                        </div>
                    }
                    <div class="article-content">
                        <h3><a href="@article.Url()">@article.Value("articleTitle")</a></h3>
                        @if (article.HasValue("articleSummary"))
                        {
                            <p class="article-summary">@article.Value("articleSummary")</p>
                        }
                        <div class="article-meta">
                            @if (article.HasValue("publicationDate"))
                            {
                                <span class="date">@article.Value<DateTime>("publicationDate").ToString("dd MMMM yyyy")</span>
                            }
                            @if (article.HasValue("author"))
                            {
                                <span class="author">بواسطة @article.Value("author")</span>
                            }
                        </div>
                        @if (article.HasValue("tags"))
                        {
                            <div class="article-tags">
                                @foreach (var tag in article.Value<string>("tags").Split(','))
                                {
                                    <span class="tag">@tag.Trim()</span>
                                }
                            </div>
                        }
                        <a href="@article.Url()" class="read-more">اقرأ المزيد</a>
                    </div>
                </article>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Add any news page specific JavaScript here
    </script>
}
