using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Web.Models
{
    /// <summary>
    /// Services Page Document Type for MDD Plus
    /// </summary>
    public class ServicesPage : PublishedContentModel
    {
        public ServicesPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Page Header
        public string PageTitle => this.Value<string>("pageTitle") ?? "خدماتنا";
        public string PageSubtitle => this.Value<string>("pageSubtitle") ?? "حلول تمويلية شاملة للأفراد والشركات";
        public IPublishedContent? HeaderImage => this.Value<IPublishedContent>("headerImage");

        // Individual Services
        public string IndividualServicesTitle => this.Value<string>("individualServicesTitle") ?? "للأفراد";
        public string IndividualServicesDescription => this.Value<string>("individualServicesDescription") ??
            "خدمات استثمارية متنوعة تناسب جميع فئات المستثمرين";

        // Investment Service
        public string InvestmentTitle => this.Value<string>("investmentTitle") ?? "استثمار آمن";
        public string InvestmentDescription => this.Value<string>("investmentDescription") ??
            "استثمر أموالك بعوائد تنافسية تصل إلى 18% سنوياً مع ضمانات قوية ومخاطر محسوبة";
        public string InvestmentFeatures => this.Value<string>("investmentFeatures") ??
            "عوائد تنافسية، مخاطر محسوبة، سيولة عالية، شفافية كاملة";

        // Auto Investment Service
        public string AutoInvestmentTitle => this.Value<string>("autoInvestmentTitle") ?? "الاستثمار الآلي";
        public string AutoInvestmentDescription => this.Value<string>("autoInvestmentDescription") ??
            "استثمار تلقائي ذكي يوفر عليك الوقت والجهد ويحقق أفضل العوائد";
        public string AutoInvestmentFeatures => this.Value<string>("autoInvestmentFeatures") ??
            "استثمار تلقائي، توزيع ذكي للمخاطر، إعادة استثمار العوائد، إدارة محفظة متقدمة";

        // Business Services
        public string BusinessServicesTitle => this.Value<string>("businessServicesTitle") ?? "للأعمال";
        public string BusinessServicesDescription => this.Value<string>("businessServicesDescription") ??
            "حلول تمويلية متنوعة تلبي احتياجات الشركات من جميع الأحجام";

        // Invoice Financing
        public string InvoiceFinancingTitle => this.Value<string>("invoiceFinancingTitle") ?? "تمويل الفواتير";
        public string InvoiceFinancingDescription => this.Value<string>("invoiceFinancingDescription") ??
            "تمويل سريع للفواتير الحكومية والخاصة بأسعار تنافسية وإجراءات مبسطة";
        public string InvoiceFinancingFeatures => this.Value<string>("invoiceFinancingFeatures") ??
            "تمويل يصل إلى 90% من قيمة الفاتورة، معالجة سريعة خلال 24 ساعة، أسعار تنافسية تبدأ من 0.5% شهرياً";

        // Working Capital Financing
        public string WorkingCapitalTitle => this.Value<string>("workingCapitalTitle") ?? "تمويل رأس المال العامل";
        public string WorkingCapitalDescription => this.Value<string>("workingCapitalDescription") ??
            "حلول تمويلية مرنة لتلبية احتياجات رأس المال العامل وتمويل النمو";
        public string WorkingCapitalFeatures => this.Value<string>("workingCapitalFeatures") ??
            "مبالغ تمويل تصل إلى 10 مليون ريال، فترات سداد مرنة، معدلات فائدة تنافسية";

        // Supply Chain Financing
        public string SupplyChainTitle => this.Value<string>("supplyChainTitle") ?? "تمويل سلاسل الإمداد";
        public string SupplyChainDescription => this.Value<string>("supplyChainDescription") ??
            "تمويل متخصص لموردي الشركات الكبرى والمؤسسات الحكومية";
        public string SupplyChainFeatures => this.Value<string>("supplyChainFeatures") ??
            "تمويل مضمون من الشركة المشترية، معالجة سريعة، أسعار تفضيلية";

        // Purchase Order Financing
        public string PurchaseOrderTitle => this.Value<string>("purchaseOrderTitle") ?? "تمويل أوامر الشراء";
        public string PurchaseOrderDescription => this.Value<string>("purchaseOrderDescription") ??
            "تمويل أوامر الشراء المؤكدة من العملاء لتمكين الشركات من تنفيذ الطلبات الكبيرة";

        // Letter of Guarantee Financing
        public string LetterGuaranteeTitle => this.Value<string>("letterGuaranteeTitle") ?? "تمويل خطاب الضمان";
        public string LetterGuaranteeDescription => this.Value<string>("letterGuaranteeDescription") ??
            "تمويل خطابات الضمان الحكومية والخاصة بضمانات قوية وأسعار تنافسية";

        // Process Section
        public string ProcessTitle => this.Value<string>("processTitle") ?? "كيف نعمل";
        public string ProcessDescription => this.Value<string>("processDescription") ??
            "عملية بسيطة وسريعة للحصول على التمويل أو بدء الاستثمار";

        // Benefits Section
        public string BenefitsTitle => this.Value<string>("benefitsTitle") ?? "مزايا خدماتنا";
        public string BenefitsDescription => this.Value<string>("benefitsDescription") ??
            "نقدم مزايا فريدة تجعلنا الخيار الأمثل للتمويل والاستثمار";

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? "خدماتنا - مدد بلس";
        public string MetaDescription => this.Value<string>("metaDescription") ??
            "اكتشف خدمات مدد بلس المتنوعة: الاستثمار الآمن، تمويل الفواتير، رأس المال العامل، وسلاسل الإمداد. حلول متوافقة مع الشريعة الإسلامية.";
    }
}
