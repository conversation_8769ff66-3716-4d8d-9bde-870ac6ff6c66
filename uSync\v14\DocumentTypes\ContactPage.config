<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="e7f8a9b0-1c2d-3e4f-5a6b-7c8d9e0f1a2b" Alias="contactPage" Level="1">
  <Info>
    <Name>Contact Page</Name>
    <Icon>icon-phone</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Contact page for MDD Plus fintech website</Description>
    <AllowAtRoot>False</AllowAtRoot>
    <ListView>00000000-0000-0000-0000-000000000000</ListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Compositions />
    <DefaultTemplate>ContactPage</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="f8a9b0c1-2d3e-4f5a-6b7c-8d9e0f1a2b3c">ContactPage</Template>
    </AllowedTemplates>
  </Info>
  <Structure />
  <GenericProperties>
    <!-- Page Content -->
    <GenericProperty>
      <Key>1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Name>Page Title</Name>
      <Alias>pageTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Contact page title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Name>Page Subtitle</Name>
      <Alias>pageSubtitle</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Contact page subtitle]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Name>Contact Introduction</Name>
      <Alias>contactIntroduction</Alias>
      <Definition>ca90c950-0aff-4e72-b976-a30b1ac57dad</Definition>
      <Type>Umbraco.TinyMCE</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Introduction text for contact page]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="content">Content</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Contact Information -->
    <GenericProperty>
      <Key>4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Name>Office Address</Name>
      <Alias>officeAddress</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main office address]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="contactInfo">Contact Information</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Name>Phone Number</Name>
      <Alias>phoneNumber</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main phone number]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="contactInfo">Contact Information</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c</Key>
      <Name>Email Address</Name>
      <Alias>emailAddress</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main email address]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="contactInfo">Contact Information</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d</Key>
      <Name>Working Hours</Name>
      <Alias>workingHours</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Office working hours]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="contactInfo">Contact Information</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>8b9c0d1e-2f3a-4b5c-6d7e-8f9a0b1c2d3e</Key>
      <Name>WhatsApp Number</Name>
      <Alias>whatsappNumber</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[WhatsApp contact number]]></Description>
      <SortOrder>5</SortOrder>
      <Tab Alias="contactInfo">Contact Information</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- Map & Location -->
    <GenericProperty>
      <Key>9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f</Key>
      <Name>Google Maps Embed</Name>
      <Alias>googleMapsEmbed</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Google Maps embed code]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="location">Location</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>0d1e2f3a-4b5c-6d7e-8f9a-0b1c2d3e4f5a</Key>
      <Name>Latitude</Name>
      <Alias>latitude</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Location latitude]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="location">Location</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b</Key>
      <Name>Longitude</Name>
      <Alias>longitude</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Location longitude]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="location">Location</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    
    <!-- SEO Properties -->
    <GenericProperty>
      <Key>2f3a4b5c-6d7e-8f9a-0b1c-2d3e4f5a6b7c</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Caption>Content</Caption>
      <Alias>content</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Caption>Contact Information</Caption>
      <Alias>contactInfo</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Caption>Location</Caption>
      <Alias>location</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
    <Tab>
      <Key>d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>3</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
