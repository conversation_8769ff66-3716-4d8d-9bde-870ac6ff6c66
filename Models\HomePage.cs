using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Models
{
    /// <summary>
    /// Home Page Document Type for MDD Plus
    /// Following Umbraco Learning Base best practices
    /// </summary>
    public class HomePage : PublishedContentModel
    {
        public HomePage(IPublishedContent content, IPublishedValueFallback publishedValueFallback)
            : base(content, publishedValueFallback) { }

        // Hero Section Properties
        public string HeroTitle => this.Value<string>("heroTitle") ?? "مرحباً بكم في مدد بلس";
        public string HeroSubtitle => this.Value<string>("heroSubtitle") ?? "منصة التمويل الجماعي الرائدة";
        public string HeroButtonText => this.Value<string>("heroButtonText") ?? "ابدأ الآن";
        public IPublishedContent? HeroImage => this.Value<IPublishedContent>("heroImage");

        // About Section
        public string AboutTitle => this.Value<string>("aboutTitle") ?? "من نحن";
        public string AboutText => this.Value<string>("aboutText") ?? "";

        // Statistics Section
        public string TotalFunded => this.Value<string>("totalFunded") ?? "500,000,000";
        public string AnnualReturn => this.Value<string>("annualReturn") ?? "18";
        public string ActiveInvestors => this.Value<string>("activeInvestors") ?? "5,000";
        public string RepaymentRate => this.Value<string>("repaymentRate") ?? "99.8";

        // Services Section
        public string ServicesTitle => this.Value<string>("servicesTitle") ?? "خدماتنا";
        public IEnumerable<IPublishedContent> ServiceItems => this.Value<IEnumerable<IPublishedContent>>("serviceItems") ?? Enumerable.Empty<IPublishedContent>();

        // Contact Section
        public string ContactTitle => this.Value<string>("contactTitle") ?? "تواصل معنا";
        public string ContactEmail => this.Value<string>("contactEmail") ?? "<EMAIL>";
        public string ContactPhone => this.Value<string>("contactPhone") ?? "+966 11 234 5678";

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? HeroTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? HeroSubtitle;
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? "";
    }
}
