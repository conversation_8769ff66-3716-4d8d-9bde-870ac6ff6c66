// MDD Plus - Site JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('MDD Plus website loaded successfully!');
    
    // Smooth scrolling for anchor links
    initSmoothScrolling();
    
    // Mobile menu toggle (if needed)
    initMobileMenu();
    
    // Form enhancements
    initForms();
});

function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function initMobileMenu() {
    // Add mobile menu functionality if needed
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        // Mobile menu logic here
    }
}

function initForms() {
    // Add form validation and enhancements
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Add form validation logic here
        });
    });
}

// Utility functions
function showMessage(message, type = 'info') {
    // Create and show notification messages
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}
