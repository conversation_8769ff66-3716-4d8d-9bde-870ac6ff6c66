using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Web.Models;

namespace MDDPlus.Web.Controllers
{
    public class ContactController : UmbracoPageController
    {
        private readonly ILogger<ContactController> _logger;

        public ContactController(ILogger<ContactController> logger, ICompositeViewEngine compositeViewEngine)
            : base(logger, compositeViewEngine)
        {
            _logger = logger;
        }

        public IActionResult Index(ContactPage model)
        {
            // Set language and direction based on current culture
            var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
            ViewBag.Language = currentCulture.Name.StartsWith("ar") ? "ar" : "en";
            ViewBag.Direction = currentCulture.Name.StartsWith("ar") ? "rtl" : "ltr";
            ViewBag.ActivePage = "contact";

            // Set page-specific meta data
            ViewBag.Title = model.MetaTitle;
            ViewBag.MetaDescription = model.MetaDescription;

            return View(model);
        }

        [HttpPost]
        public IActionResult SubmitContactForm(string name, string email, string phone, string subject, string message)
        {
            try
            {
                // TODO: Implement contact form submission logic
                // - Validate input
                // - Send email notification
                // - Store in database
                // - Send confirmation email

                TempData["SuccessMessage"] = "تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting contact form");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.";
                return RedirectToAction("Index");
            }
        }
    }
}
