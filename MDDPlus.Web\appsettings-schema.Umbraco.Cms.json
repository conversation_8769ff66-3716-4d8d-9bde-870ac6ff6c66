{"$schema": "http://json-schema.org/draft-04/schema#", "title": "UmbracoCmsSchema", "type": "object", "properties": {"Umbraco": {"$ref": "#/definitions/UmbracoDefinition"}}, "definitions": {"UmbracoDefinition": {"type": "object", "description": "Configuration container for all Umbraco products.", "properties": {"CMS": {"$ref": "#/definitions/UmbracoCmsDefinition"}}}, "UmbracoCmsDefinition": {"type": "object", "description": "Configuration of Umbraco CMS.", "properties": {"Content": {"$ref": "#/definitions/ContentSettings"}, "DeliveryApi": {"$ref": "#/definitions/DeliveryApiSettings"}, "Debug": {"$ref": "#/definitions/CoreDebugSettings"}, "ExceptionFilter": {"$ref": "#/definitions/ExceptionFilterSettings"}, "ModelsBuilder": {"$ref": "#/definitions/ModelsBuilderSettings"}, "Global": {"$ref": "#/definitions/GlobalSettings"}, "HealthChecks": {"$ref": "#/definitions/HealthChecksSettings"}, "Hosting": {"$ref": "#/definitions/HostingSettings"}, "Imaging": {"$ref": "#/definitions/ImagingSettings"}, "Examine": {"$ref": "#/definitions/IndexCreatorSettings"}, "Indexing": {"$ref": "#/definitions/IndexingSettings"}, "Logging": {"$ref": "#/definitions/LoggingSettings"}, "NuCache": {"$ref": "#/definitions/NuCacheSettings"}, "RequestHandler": {"$ref": "#/definitions/RequestHandlerSettings"}, "Runtime": {"$ref": "#/definitions/RuntimeSettings"}, "Security": {"$ref": "#/definitions/SecuritySettings"}, "TypeFinder": {"$ref": "#/definitions/TypeFinderSettings"}, "WebRouting": {"$ref": "#/definitions/WebRoutingSettings"}, "Plugins": {"$ref": "#/definitions/UmbracoPluginSettings"}, "Unattended": {"$ref": "#/definitions/UnattendedSettings"}, "BasicAuth": {"$ref": "#/definitions/BasicAuthSettings"}, "PackageMigration": {"$ref": "#/definitions/PackageMigrationSettings"}, "LegacyPasswordMigration": {"$ref": "#/definitions/LegacyPasswordMigrationSettings"}, "HelpPage": {"$ref": "#/definitions/HelpPageSettings"}, "InstallDefaultData": {"$ref": "#/definitions/InstallDefaultDataNamedOptions"}, "DataTypes": {"$ref": "#/definitions/DataTypesSettings"}, "Marketplace": {"$ref": "#/definitions/MarketplaceSettings"}, "Webhook": {"$ref": "#/definitions/WebhookSettings"}, "Cache": {"$ref": "#/definitions/CacheSettings"}}}, "ContentSettings": {"type": "object", "description": "Typed configuration options for content settings.\n            ", "properties": {"Notifications": {"description": "Gets or sets a value for the content notification settings.\n            ", "oneOf": [{"$ref": "#/definitions/ContentNotificationSettings"}]}, "Imaging": {"description": "Gets or sets a value for the content imaging settings.\n            ", "oneOf": [{"$ref": "#/definitions/ContentImagingSettings"}]}, "ResolveUrlsFromTextString": {"type": "boolean", "description": "Gets or sets a value indicating whether URLs should be resolved from text strings.\n            ", "default": false}, "Error404Collection": {"type": "array", "description": "Gets or sets a value for the collection of error pages.\n            ", "items": {"$ref": "#/definitions/ContentErrorPage"}}, "PreviewBadge": {"type": "string", "description": "Gets or sets a value for the preview badge mark-up.\n            ", "default": "\r\n<script src=\"{0}/website/preview.js\"></script>\r\n<umb-website-preview path=\"{0}\" url=\"{1}\" unique=\"{2}\"></umb-website-preview>"}, "ShowDeprecatedPropertyEditors": {"type": "boolean", "description": "Gets or sets a value indicating whether deprecated property editors should be shown.\n            ", "default": false}, "LoginBackgroundImage": {"type": "string", "description": "Gets or sets a value for the path to the login screen background image.\n            ", "default": "assets/login.jpg"}, "LoginLogoImage": {"type": "string", "description": "Gets or sets a value for the path to the login screen logo image\nshown on top of the background image set in LoginBackgroundImage.\n            ", "default": "assets/logo_light.svg"}, "LoginLogoImageAlternative": {"type": "string", "description": "Gets or sets a value for the path to the login screen logo image when shown on top\nof a light background (e.g. in mobile resolutions).\n            ", "default": "assets/logo_dark.svg"}, "BackOfficeLogo": {"type": "string", "description": "Gets or sets a value for the path to the backoffice logo.\n            ", "default": "assets/logo.svg"}, "BackOfficeLogoAlternative": {"type": "string", "description": "Gets or sets a value for the path to the alternative backoffice logo, which can be shown\non top of a light background.\n            ", "default": "assets/logo_blue.svg"}, "DisableDeleteWhenReferenced": {"type": "boolean", "description": "Gets or sets a value indicating whether to disable the deletion of items referenced by other items.\n            ", "default": false}, "DisableUnpublishWhenReferenced": {"type": "boolean", "description": "Gets or sets a value indicating whether to disable the unpublishing of items referenced by other items.\n            ", "default": false}, "ContentVersionCleanupPolicy": {"description": "Gets or sets the model representing the global content version cleanup policy\n            ", "oneOf": [{"$ref": "#/definitions/ContentVersionCleanupPolicySettings"}]}, "AllowEditInvariantFromNonDefault": {"type": "boolean", "description": "Gets or sets a value indicating whether to allow editing invariant properties from a non-default language variation.", "default": false}, "AllowedUploadedFileExtensions": {"type": "array", "description": "Gets or sets a value for the collection of file extensions that are allowed for upload.\n            ", "items": {"type": "string"}}, "DisallowedUploadedFileExtensions": {"type": "array", "description": "Gets or sets a value for the collection of file extensions that are disallowed for upload.\n            ", "default": "ashx,aspx,ascx,config,cshtml,vbhtml,asmx,air,axd,xamlx", "items": {"type": "string"}}, "AllowedMediaHosts": {"type": "array", "description": "Gets or sets the allowed external host for media. If empty only relative paths are allowed.", "items": {"type": "string"}}, "ShowDomainWarnings": {"type": "boolean", "description": "Gets or sets a value indicating whether to show domain warnings.", "default": true}, "ShowUnroutableContentWarnings": {"type": "boolean", "description": "Gets or sets a value indicating whether to show unroutable content warnings.", "default": true}}}, "ContentNotificationSettings": {"type": "object", "description": "Typed configuration options for content notification settings.\n            ", "properties": {"Email": {"type": ["null", "string"], "description": "Gets or sets a value for the email address for notifications.\n            "}, "DisableHtmlEmail": {"type": "boolean", "description": "Gets or sets a value indicating whether HTML email notifications should be disabled.\n            ", "default": false}}}, "ContentImagingSettings": {"type": "object", "description": "Typed configuration options for content imaging settings.\n            ", "properties": {"ImageFileTypes": {"type": "array", "description": "Gets or sets a value for the collection of accepted image file extensions.\n            ", "default": "jpeg,jpg,gif,bmp,png,tiff,tif,webp", "items": {"type": "string"}}, "AutoFillImageProperties": {"type": "array", "items": {"$ref": "#/definitions/ImagingAutoFillUploadField"}}}}, "ImagingAutoFillUploadField": {"type": "object", "description": "Typed configuration options for image autofill upload settings.\n            ", "required": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HeightFieldAlias", "LengthFieldAlias", "ExtensionFieldAlias"], "properties": {"Alias": {"type": "string", "description": "Gets or sets a value for the alias of the image upload property.\n            ", "minLength": 1}, "WidthFieldAlias": {"type": "string", "description": "Gets or sets a value for the width field alias of the image upload property.\n            ", "minLength": 1}, "HeightFieldAlias": {"type": "string", "description": "Gets or sets a value for the height field alias of the image upload property.\n            ", "minLength": 1}, "LengthFieldAlias": {"type": "string", "description": "Gets or sets a value for the length field alias of the image upload property.\n            ", "minLength": 1}, "ExtensionFieldAlias": {"type": "string", "description": "Gets or sets a value for the extension field alias of the image upload property.\n            ", "minLength": 1}}}, "ContentErrorPage": {"type": "object", "description": "Typed configuration for a content error page.\n            ", "required": ["Culture"], "properties": {"ContentId": {"type": "integer", "description": "Gets or sets a value for the content Id.\n            ", "format": "int32"}, "ContentKey": {"type": "string", "description": "Gets or sets a value for the content key.\n            ", "format": "guid"}, "Culture": {"type": "string", "description": "Gets or sets a value for the content culture.\n            ", "minLength": 1}}}, "ContentVersionCleanupPolicySettings": {"type": "object", "description": "Model representing the global content version cleanup policy\n            ", "properties": {"EnableCleanup": {"type": "boolean", "description": "Gets or sets a value indicating whether or not the cleanup job should be executed.\n            ", "default": false}, "KeepAllVersionsNewerThanDays": {"type": "integer", "description": "Gets or sets the number of days where all historical content versions are kept.\n            ", "format": "int32", "default": 7}, "KeepLatestVersionPerDayForDays": {"type": "integer", "description": "Gets or sets the number of days where the latest historical content version for that day are kept.\n            ", "format": "int32", "default": 90}}}, "DeliveryApiSettings": {"type": "object", "description": "Typed configuration options for Delivery API settings.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether the Delivery API should be enabled.\n            ", "default": false}, "PublicAccess": {"type": "boolean", "description": "Gets or sets a value indicating whether the Delivery API (if enabled) should be\npublicly available or should require an API key for access.\n            ", "default": true}, "ApiKey": {"type": ["null", "string"], "description": "Gets or sets the API key used for authorizing API access (if the API is not publicly available) and preview access.\n            "}, "DisallowedContentTypeAliases": {"type": "array", "description": "Gets or sets the aliases of the content types that may never be exposed through the Delivery API. Content of these\ntypes will never be returned from any Delivery API endpoint, nor added to the query index.\n            ", "items": {"type": "string"}}, "RichTextOutputAsJson": {"type": "boolean", "description": "Gets or sets a value indicating whether the Delivery API should output rich text values as JSON instead of HTML.\n            ", "default": false}, "Media": {"description": "Gets or sets the settings for the Media APIs of the Delivery API.\n            ", "oneOf": [{"$ref": "#/definitions/MediaSettings"}]}, "MemberAuthorization": {"description": "Gets or sets the member authorization settings for the Delivery API.\n            ", "oneOf": [{"type": "null"}, {"$ref": "#/definitions/MemberAuthorizationSettings"}]}, "OutputCache": {"description": "Gets or sets the settings for the Delivery API output cache.\n            ", "oneOf": [{"$ref": "#/definitions/OutputCacheSettings"}]}}}, "MediaSettings": {"type": "object", "description": "Typed configuration options for the Media APIs of the Delivery API.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether the Media APIs of the Delivery API should be enabled.\n            ", "default": false}, "PublicAccess": {"type": "boolean", "description": "Gets or sets a value indicating whether the Media APIs of the Delivery API (if enabled) should be\npublicly available or should require an API key for access.\n            ", "default": true}}}, "MemberAuthorizationSettings": {"type": "object", "description": "Typed configuration options for member authorization settings for the Delivery API.\n            ", "properties": {"AuthorizationCodeFlow": {"description": "Gets or sets the Authorization Code Flow configuration for the Delivery API.\n            ", "oneOf": [{"type": "null"}, {"$ref": "#/definitions/AuthorizationCodeFlowSettings"}]}, "ClientCredentialsFlow": {"description": "Gets or sets the Client Credentials Flow configuration for the Delivery API.\n            ", "oneOf": [{"type": "null"}, {"$ref": "#/definitions/ClientCredentialsFlowSettings"}]}}}, "AuthorizationCodeFlowSettings": {"type": "object", "description": "Typed configuration options for the Authorization Code Flow settings for the Delivery API.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether Authorization Code Flow should be enabled for the Delivery API.\n            ", "default": false}, "LoginRedirectUrls": {"type": "array", "description": "Gets or sets the URLs allowed to use as redirect targets after a successful login (session authorization).\n            ", "items": {"type": "string", "format": "uri"}}, "LogoutRedirectUrls": {"type": "array", "description": "Gets or sets the URLs allowed to use as redirect targets after a successful logout (session termination).\n            ", "items": {"type": "string", "format": "uri"}}}}, "ClientCredentialsFlowSettings": {"type": "object", "description": "Typed configuration options for the Client Credentials Flow settings for the Delivery API.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether Client Credentials Flow should be enabled for the Delivery API.\n            ", "default": false}, "AssociatedMembers": {"type": "array", "items": {"$ref": "#/definitions/ClientCredentialsFlowMemberSettings"}}}}, "ClientCredentialsFlowMemberSettings": {"type": "object", "properties": {"UserName": {"type": "string", "description": "Gets or sets the user name of the member to associate with the session after a successful login.\n            "}, "ClientId": {"type": "string", "description": "Gets or sets the client ID that allows for a successful login.\n            "}, "ClientSecret": {"type": "string", "description": "Gets or sets the client secret that allows for a successful login.\n            "}}}, "OutputCacheSettings": {"type": "object", "description": "Typed configuration options for output caching of the Delivery API.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether the Delivery API output should be cached.\n            ", "default": false}, "ContentDuration": {"type": "string", "description": "Gets or sets a value indicating how long the Content Delivery API output should be cached.\n            ", "format": "duration", "default": "00:01:00"}, "MediaDuration": {"type": "string", "description": "Gets or sets a value indicating how long the Media Delivery API output should be cached.\n            ", "format": "duration", "default": "00:01:00"}}}, "CoreDebugSettings": {"type": "object", "description": "Typed configuration options for core debug settings.\n            ", "properties": {"LogIncompletedScopes": {"type": "boolean", "description": "Gets or sets a value indicating whether incompleted scopes should be logged.\n            ", "default": false}, "DumpOnTimeoutThreadAbort": {"type": "boolean", "description": "Gets or sets a value indicating whether memory dumps on thread abort should be taken.\n            ", "default": false}}}, "ExceptionFilterSettings": {"type": "object", "description": "Typed configuration options for exception filter settings.\n            ", "properties": {"Disabled": {"type": "boolean", "description": "Gets or sets a value indicating whether the exception filter is disabled.\n            ", "default": false}}}, "ModelsBuilderSettings": {"type": "object", "description": "Typed configuration options for models builder settings.\n            ", "properties": {"ModelsMode": {"description": "Gets or sets a value for the models mode.\n            ", "default": "InMemoryAuto", "oneOf": [{"$ref": "#/definitions/ModelsMode"}]}, "ModelsNamespace": {"type": "string", "description": "Gets or sets a value for models namespace.\n            ", "default": "Umbraco.Cms.Web.Common.PublishedModels"}, "FlagOutOfDateModels": {"type": "boolean", "description": "Gets or sets a value indicating whether we should flag out-of-date models.\n            "}, "ModelsDirectory": {"type": "string", "description": "Gets or sets a value for the models directory.\n            ", "default": "~/umbraco/models"}, "AcceptUnsafeModelsDirectory": {"type": "boolean", "description": "Gets or sets a value indicating whether to accept an unsafe value for ModelsDirectory.\n            ", "default": false}, "DebugLevel": {"type": "integer", "description": "Gets or sets a value indicating the debug log level.\n            ", "format": "int32", "default": 0}, "IncludeVersionNumberInGeneratedModels": {"type": "boolean", "description": "Gets or sets a value indicating whether the version number should be included in generated models.\n            ", "default": true}}}, "ModelsMode": {"type": "string", "description": "Defines the models generation modes.\n            ", "x-enumNames": ["Nothing", "InMemoryAuto", "SourceCodeManual", "SourceCodeAuto"], "enum": ["Nothing", "InMemoryAuto", "SourceCodeManual", "SourceCodeAuto"]}, "GlobalSettings": {"type": "object", "description": "Typed configuration options for global settings.\n            ", "properties": {"ReservedUrls": {"type": "string", "description": "Gets or sets a value for the reserved URLs (must end with a comma).\n            ", "default": "~/.well-known,"}, "ReservedPaths": {"type": "string", "description": "Gets or sets a value for the reserved paths (must end with a comma).\n            ", "default": "~/app_plugins/,~/install/,~/mini-profiler-resources/,~/umbraco/,"}, "TimeOut": {"type": "string", "description": "Gets or sets a value for the back-office login timeout.\n            ", "format": "duration", "default": "00:20:00"}, "DefaultUILanguage": {"type": "string", "description": "Gets or sets a value for the default UI language.\n            ", "default": "en-US"}, "HideTopLevelNodeFromPath": {"type": "boolean", "description": "Gets or sets a value indicating whether to hide the top level node from the path.\n            ", "default": true}, "UseHttps": {"type": "boolean", "description": "Gets or sets a value indicating whether HTTPS should be used.\n            ", "default": false}, "VersionCheckPeriod": {"type": "integer", "description": "Gets or sets a value for the version check period in days.\n            ", "format": "int32", "default": 7}, "IconsPath": {"type": "string", "description": "Gets or sets a value for the Umbraco icons path.\n            ", "default": "umbraco/assets/icons"}, "UmbracoCssPath": {"type": "string", "description": "Gets or sets a value for the Umbraco CSS path.\n            ", "default": "~/css"}, "UmbracoScriptsPath": {"type": "string", "description": "Gets or sets a value for the Umbraco scripts path.\n            ", "default": "~/scripts"}, "UmbracoMediaPath": {"type": "string", "description": "Gets or sets a value for the Umbraco media request path.\n            ", "default": "~/media"}, "UmbracoMediaPhysicalRootPath": {"type": "string", "description": "Gets or sets a value for the physical Umbraco media root path (falls back to UmbracoMediaPath when\nempty).\n            "}, "DisableElectionForSingleServer": {"type": "boolean", "description": "Gets or sets a value indicating whether to disable the election for a single server.\n            ", "default": false}, "DatabaseFactoryServerVersion": {"type": "string", "description": "Gets or sets a value for the database factory server version.\n            "}, "MainDomLock": {"type": "string", "description": "Gets or sets a value for the main dom lock.\n            "}, "MainDomKeyDiscriminator": {"type": "string", "description": "Gets or sets a value to discriminate MainDom boundaries.\n\n    Generally the default should suffice but useful for advanced scenarios e.g. azure deployment slot based zero\n    downtime deployments.\n\n            "}, "MainDomReleaseSignalPollingInterval": {"type": "integer", "description": "Gets or sets the duration (in milliseconds) for which the MainDomLock release signal polling task should sleep.\n            ", "format": "int32", "default": 2000}, "Id": {"type": "string", "description": "Gets or sets the telemetry ID.\n            "}, "NoNodesViewPath": {"type": "string", "description": "Gets or sets a value for the path to the no content view.\n            ", "default": "~/umbraco/UmbracoWebsite/NoNodes.cshtml"}, "DatabaseServerRegistrar": {"description": "Gets or sets a value for the database server registrar settings.\n            ", "oneOf": [{"$ref": "#/definitions/DatabaseServerRegistrarSettings"}]}, "DatabaseServerMessenger": {"description": "Gets or sets a value for the database server messenger settings.\n            ", "oneOf": [{"$ref": "#/definitions/DatabaseServerMessengerSettings"}]}, "Smtp": {"description": "Gets or sets a value for the SMTP settings.\n            ", "oneOf": [{"type": "null"}, {"$ref": "#/definitions/SmtpSettings"}]}, "DistributedLockingReadLockDefaultTimeout": {"type": "string", "description": "Gets or sets a value representing the maximum time to wait whilst attempting to obtain a distributed read lock.\n            ", "format": "duration", "default": "00:01:00"}, "DistributedLockingWriteLockDefaultTimeout": {"type": "string", "description": "Gets or sets a value representing the maximum time to wait whilst attempting to obtain a distributed write lock.\n            ", "format": "duration", "default": "00:00:05"}, "DistributedLockingMechanism": {"type": "string", "description": "Gets or sets a value representing the DistributedLockingMechanism to use."}, "ForceCombineUrlPathLeftToRight": {"type": "boolean", "description": "Force url paths to be left to right, even when the culture has right to left text", "default": true, "x-example": "For the following hierarchy\n- Root (/ar)\n  - 1 (/ar/1)\n    - 2 (/ar/1/2)\n      - 3 (/ar/1/2/3)\n        - 3 (/ar/1/2/3/4)\nWhen forced\n- https://www.umbraco.com/ar/1/2/3/4\nwhen not\n- https://www.umbraco.com/ar/4/3/2/1"}, "ShowMaintenancePageWhenInUpgradeState": {"type": "boolean", "default": true}}}, "DatabaseServerRegistrarSettings": {"type": "object", "description": "Typed configuration options for database server registrar settings.\n            ", "properties": {"WaitTimeBetweenCalls": {"type": "string", "description": "Gets or sets a value for the amount of time to wait between calls to the database on the background thread.\n            ", "format": "duration", "default": "00:01:00"}, "StaleServerTimeout": {"type": "string", "description": "Gets or sets a value for the time span to wait before considering a server stale, after it has last been accessed.\n            ", "format": "duration", "default": "00:02:00"}}}, "DatabaseServerMessengerSettings": {"type": "object", "description": "Typed configuration options for database server messaging settings.\n            ", "properties": {"MaxProcessingInstructionCount": {"type": "integer", "description": "Gets or sets a value for the maximum number of instructions that can be processed at startup; otherwise the server\ncold-boots (rebuilds its caches).\n            ", "format": "int32", "default": 1000}, "TimeToRetainInstructions": {"type": "string", "description": "Gets or sets a value for the time to keep instructions in the database; records older than this number will be\npruned.\n            ", "format": "duration", "default": "2.00:00:00"}, "TimeBetweenSyncOperations": {"type": "string", "description": "Gets or sets a value for the time to wait between each sync operations.\n            ", "format": "duration", "default": "00:00:05"}, "TimeBetweenPruneOperations": {"type": "string", "description": "Gets or sets a value for the time to wait between each prune operations.\n            ", "format": "duration", "default": "00:01:00"}}}, "SmtpSettings": {"type": "object", "description": "Typed configuration options for SMTP settings.\n            ", "required": ["From"], "properties": {"From": {"type": "string", "description": "Gets or sets a value for the SMTP from address to use for messages.\n            ", "format": "email", "minLength": 1}, "Host": {"type": ["null", "string"], "description": "Gets or sets a value for the SMTP host.\n            "}, "Port": {"type": "integer", "description": "Gets or sets a value for the SMTP port.\n            ", "format": "int32"}, "SecureSocketOptions": {"description": "Gets or sets a value for the secure socket options.\n            ", "default": "Auto", "oneOf": [{"$ref": "#/definitions/SecureSocketOptions"}]}, "PickupDirectoryLocation": {"type": ["null", "string"], "description": "Gets or sets a value for the SMTP pick-up directory.\n            "}, "DeliveryMethod": {"description": "Gets or sets a value for the SMTP delivery method.\n            ", "default": "Network", "oneOf": [{"$ref": "#/definitions/SmtpDeliveryMethod"}]}, "Username": {"type": ["null", "string"], "description": "Gets or sets a value for the SMTP user name.\n            "}, "Password": {"type": ["null", "string"], "description": "Gets or sets a value for the SMTP password.\n            "}}}, "SecureSocketOptions": {"type": "string", "description": "Matches MailKit.Security.SecureSocketOptions and defined locally to avoid having to take\na dependency on this external library into Umbraco.Core.\n            ", "x-enumNames": ["None", "Auto", "SslOnConnect", "StartTls", "StartTlsWhenAvailable"], "enum": ["None", "Auto", "SslOnConnect", "StartTls", "StartTlsWhenAvailable"]}, "SmtpDeliveryMethod": {"type": "string", "description": "", "x-enumNames": ["Network", "SpecifiedPickupDirectory", "PickupDirectoryFromIis"], "enum": ["Network", "SpecifiedPickupDirectory", "PickupDirectoryFromIis"]}, "HealthChecksSettings": {"type": "object", "description": "Typed configuration options for healthchecks settings.\n            ", "properties": {"DisabledChecks": {"type": "array", "description": "Gets or sets a value for the collection of healthchecks that are disabled.\n            ", "items": {"$ref": "#/definitions/DisabledHealthCheckSettings"}}, "Notification": {"description": "Gets or sets a value for the healthcheck notification settings.\n            ", "oneOf": [{"$ref": "#/definitions/HealthChecksNotificationSettings"}]}}}, "DisabledHealthCheckSettings": {"type": "object", "description": "Typed configuration options for disabled healthcheck settings.\n            ", "properties": {"Id": {"type": "string", "description": "Gets or sets a value for the healthcheck Id to disable.\n            ", "format": "guid"}, "DisabledOn": {"type": "string", "description": "Gets or sets a value for the date the healthcheck was disabled.\n            ", "format": "date-time"}, "DisabledBy": {"type": "integer", "description": "Gets or sets a value for Id of the user that disabled the healthcheck.\n            ", "format": "int32"}}}, "HealthChecksNotificationSettings": {"type": "object", "description": "Typed configuration options for healthcheck notification settings.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether health check notifications are enabled.\n            ", "default": false}, "FirstRunTime": {"type": "string", "description": "Gets or sets a value for the first run time of a healthcheck notification in crontab format.\n            "}, "Period": {"type": "string", "description": "Gets or sets a value for the period of the healthcheck notification.\n            ", "format": "duration", "default": "1.00:00:00"}, "NotificationMethods": {"type": "object", "description": "Gets or sets a value for the collection of health check notification methods.\n            ", "additionalProperties": {"$ref": "#/definitions/HealthChecksNotificationMethodSettings"}}, "DisabledChecks": {"type": "array", "description": "Gets or sets a value for the collection of health checks that are disabled for notifications.\n            ", "items": {"$ref": "#/definitions/DisabledHealthCheckSettings"}}}}, "HealthChecksNotificationMethodSettings": {"type": "object", "description": "Typed configuration options for healthcheck notification method settings.\n            ", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether the health check notification method is enabled.\n            ", "default": false}, "Verbosity": {"description": "Gets or sets a value for the health check notifications reporting verbosity.\n            ", "default": "Summary", "oneOf": [{"$ref": "#/definitions/HealthCheckNotificationVerbosity"}]}, "FailureOnly": {"type": "boolean", "description": "Gets or sets a value indicating whether the health check notifications should occur on failures only.\n            ", "default": false}, "Settings": {"type": "object", "description": "Gets or sets a value providing provider specific settings for the health check notification method.\n            ", "additionalProperties": {"type": "string"}}}}, "HealthCheckNotificationVerbosity": {"type": "string", "description": "", "x-enumNames": ["Summary", "Detailed"], "enum": ["Summary", "Detailed"]}, "HostingSettings": {"type": "object", "description": "Typed configuration options for hosting settings.\n            ", "properties": {"ApplicationVirtualPath": {"type": ["null", "string"], "description": "Gets or sets a value for the application virtual path.\n            "}, "LocalTempStorageLocation": {"description": "Gets or sets a value for the location of temporary files.\n            ", "default": "<PERSON><PERSON><PERSON>", "oneOf": [{"$ref": "#/definitions/LocalTempStorage"}]}, "Debug": {"type": "boolean", "description": "Gets or sets a value indicating whether umbraco is running in [debug mode].\n            ", "default": false}, "SiteName": {"type": ["null", "string"], "description": "Gets or sets a value specifying the name of the site.\n            "}}}, "LocalTempStorage": {"type": "string", "description": "", "x-enumNames": ["Unknown", "<PERSON><PERSON><PERSON>", "EnvironmentTemp"], "enum": ["Unknown", "<PERSON><PERSON><PERSON>", "EnvironmentTemp"]}, "ImagingSettings": {"type": "object", "description": "Typed configuration options for imaging settings.", "properties": {"HMACSecretKey": {"type": "string", "description": "Gets or sets a value for the Hash-based Message Authentication Code (HMAC) secret key for request authentication.", "format": "byte"}, "Cache": {"description": "Gets or sets a value for imaging cache settings.", "oneOf": [{"$ref": "#/definitions/ImagingCacheSettings"}]}, "Resize": {"description": "Gets or sets a value for imaging resize settings.", "oneOf": [{"$ref": "#/definitions/ImagingResizeSettings"}]}}}, "ImagingCacheSettings": {"type": "object", "description": "Typed configuration options for image cache settings.\n            ", "properties": {"BrowserMaxAge": {"type": "string", "description": "Gets or sets a value for the browser image cache maximum age.\n            ", "format": "duration", "default": "7.00:00:00"}, "CacheMaxAge": {"type": "string", "description": "Gets or sets a value for the image cache maximum age.\n            ", "format": "duration", "default": "365.00:00:00"}, "CacheHashLength": {"type": "integer", "description": "Gets or sets a value for the image cache hash length.\n            ", "default": 12}, "CacheFolderDepth": {"type": "integer", "description": "Gets or sets a value for the image cache folder depth.\n            ", "default": 8}, "CacheFolder": {"type": "string", "description": "Gets or sets a value for the image cache folder.\n            ", "default": "~/umbraco/Data/TEMP/MediaCache"}}}, "ImagingResizeSettings": {"type": "object", "description": "Typed configuration options for image resize settings.", "properties": {"MaxWidth": {"type": "integer", "description": "Gets or sets a value for the maximum resize width.", "format": "int32", "default": 5000}, "MaxHeight": {"type": "integer", "description": "Gets or sets a value for the maximum resize height.", "format": "int32", "default": 5000}}}, "IndexCreatorSettings": {"type": "object", "description": "Typed configuration options for index creator settings.\n            ", "properties": {"LuceneDirectoryFactory": {"description": "Gets or sets a value for lucene directory factory type.\n            ", "oneOf": [{"$ref": "#/definitions/LuceneDirectoryFactory"}]}}}, "LuceneDirectoryFactory": {"type": "string", "description": "", "x-enumNames": ["<PERSON><PERSON><PERSON>", "SyncedTempFileSystemDirectoryFactory", "TempFileSystemDirectoryFactory"], "enum": ["<PERSON><PERSON><PERSON>", "SyncedTempFileSystemDirectoryFactory", "TempFileSystemDirectoryFactory"]}, "IndexingSettings": {"type": "object", "description": "Typed configuration options for index creator settings.\n            ", "properties": {"ExplicitlyIndexEachNestedProperty": {"type": "boolean", "description": "Gets or sets a value for whether each nested property should have it's own indexed value. Requires a rebuild of indexes when changed.", "default": false}}}, "LoggingSettings": {"type": "object", "description": "Typed configuration options for logging settings.", "properties": {"MaxLogAge": {"type": "string", "description": "Gets or sets a value for the maximum age of a log file.", "format": "duration", "default": "1.00:00:00"}, "Directory": {"type": "string", "description": "Gets or sets the folder to use for log files.", "default": "~/umbraco/Logs"}, "FileNameFormat": {"type": "string", "description": "Gets or sets the file name format to use for log files.", "default": "UmbracoTraceLog.{0}..json"}, "FileNameFormatArguments": {"type": "string", "description": "Gets or sets the file name format arguments to use for log files.", "default": "MachineName"}}}, "NuCacheSettings": {"type": "object", "description": "Typed configuration options for NuCache settings.\n            ", "properties": {"NuCacheSerializerType": {"description": "The serializer type that n<PERSON><PERSON> uses to persist documents in the database.\n            ", "default": "MessagePack", "oneOf": [{"$ref": "#/definitions/NuCacheSerializerType"}]}, "SqlPageSize": {"type": "integer", "description": "The paging size to use for nucache SQL queries.\n            ", "format": "int32", "default": 1000}, "UsePagedSqlQuery": {"type": "boolean", "default": true}}}, "NuCacheSerializerType": {"type": "string", "description": "The serializer type that n<PERSON><PERSON> uses to persist documents in the database.\n            ", "x-enumNames": ["MessagePack", "JSON"], "enum": ["MessagePack", "JSON"]}, "RequestHandlerSettings": {"type": "object", "description": "Typed configuration options for request handler settings.\n            ", "properties": {"AddTrailingSlash": {"type": "boolean", "description": "Gets or sets a value indicating whether to add a trailing slash to URLs.\n            ", "default": true}, "ConvertUrlsToAscii": {"type": "string", "description": "Gets or sets a value indicating whether to convert URLs to ASCII (valid values: \"true\", \"try\" or \"false\").\n            ", "default": "try"}, "ConvertFileNamesToAscii": {"type": "string", "description": "Gets or sets a value indicating whether to convert file names to ASCII (valid values: \"true\", \"try\" or \"false\").\n            ", "default": "false"}, "EnableDefaultCharReplacements": {"type": "boolean", "description": "Disable all default character replacements\n            ", "default": true}, "UserDefinedCharCollection": {"type": "array", "description": "Add additional character replacements, or override defaults\n            ", "items": {"$ref": "#/definitions/CharItem"}}}}, "CharItem": {"type": "object", "properties": {"Char": {"type": "string", "description": "The character to replace\n            "}, "Replacement": {"type": "string", "description": "The replacement character\n            "}}}, "RuntimeSettings": {"type": "object", "description": "Typed configuration options for runtime settings.", "properties": {"Mode": {"description": "Gets or sets the runtime mode.", "default": "BackofficeDevelopment", "oneOf": [{"$ref": "#/definitions/RuntimeMode"}]}, "MaxRequestLength": {"type": ["integer", "null"], "description": "Gets or sets a value for the maximum request length in kb.\n            ", "format": "int64"}, "TemporaryFileLifeTime": {"type": "string", "description": "Gets or sets the timespan temporary files are kept, before they are removed by a background task.", "format": "duration", "default": "1.00:00:00"}}}, "RuntimeMode": {"type": "string", "description": "Represents the configured Umbraco runtime mode.", "x-enumNames": ["BackofficeDevelopment", "Development", "Production"], "enum": ["BackofficeDevelopment", "Development", "Production"]}, "SecuritySettings": {"type": "object", "description": "Typed configuration options for security settings.\n            ", "properties": {"KeepUserLoggedIn": {"type": "boolean", "description": "Gets or sets a value indicating whether to keep the user logged in.\n            ", "default": false}, "HideDisabledUsersInBackOffice": {"type": "boolean", "description": "Gets or sets a value indicating whether to hide disabled users in the back-office.\n            ", "default": false}, "AllowPasswordReset": {"type": "boolean", "description": "Gets or sets a value indicating whether to allow user password reset.\n            ", "default": true}, "AuthCookieName": {"type": "string", "description": "Gets or sets a value for the authorization cookie name.\n            ", "default": "UMB_UCONTEXT"}, "AuthCookieDomain": {"type": ["null", "string"], "description": "Gets or sets a value for the authorization cookie domain.\n            "}, "UsernameIsEmail": {"type": "boolean", "description": "Gets or sets a value indicating whether the user's email address is to be considered as their username.\n            ", "default": true}, "MemberRequireUniqueEmail": {"type": "boolean", "description": "Gets or sets a value indicating whether the member's email address must be unique.\n            ", "default": true}, "AllowedUserNameCharacters": {"type": "string", "description": "Gets or sets the set of allowed characters for a username\n            ", "default": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+\\"}, "MemberBypassTwoFactorForExternalLogins": {"type": "boolean", "description": "Gets or sets a value indicating whether to bypass the two factor requirement in Umbraco when using external login\nfor members. Thereby rely on the External login and potential 2FA at that provider.\n            ", "default": true}, "UserBypassTwoFactorForExternalLogins": {"type": "boolean", "description": "Gets or sets a value indicating whether to bypass the two factor requirement in Umbraco when using external login\nfor users. Thereby rely on the External login and potential 2FA at that provider.\n            ", "default": true}, "MemberDefaultLockoutTimeInMinutes": {"type": "integer", "description": "Gets or sets a value for how long (in minutes) a member is locked out when a lockout occurs.\n            ", "format": "int32", "default": 43200}, "UserDefaultLockoutTimeInMinutes": {"type": "integer", "description": "Gets or sets a value for how long (in minutes) a user is locked out when a lockout occurs.\n            ", "format": "int32", "default": 43200}, "AllowConcurrentLogins": {"type": "boolean", "description": "Gets or sets a value indicating whether to allow concurrent logins.\n            ", "default": false}, "UserDefaultFailedLoginDurationInMilliseconds": {"type": "integer", "description": "Gets or sets the default duration (in milliseconds) of failed login attempts.", "format": "int64", "default": 1000, "maximum": 9.22337203685478e+18, "minimum": 0.0}, "UserMinimumFailedLoginDurationInMilliseconds": {"type": "integer", "description": "Gets or sets the minimum duration (in milliseconds) of failed login attempts.", "format": "int64", "default": 250, "maximum": 9.22337203685478e+18, "minimum": 0.0}, "BackOfficeHost": {"type": ["null", "string"], "description": "Gets or sets a value of the back-office host URI. Use this when running the back-office client and the Management API on different hosts. Leave empty when running both on the same host.\n            ", "format": "uri"}, "AuthorizeCallbackPathName": {"type": "string", "description": "Gets or sets the path to use for authorization callback. Will be appended to the BackOfficeHost.\n            ", "default": "/umbraco/oauth_complete"}, "AuthorizeCallbackLogoutPathName": {"type": "string", "description": "Gets or sets the path to use for authorization callback logout. Will be appended to the BackOfficeHost.\n            ", "default": "/umbraco/logout"}, "AuthorizeCallbackErrorPathName": {"type": "string", "description": "Gets or sets the path to use for authorization callback error. Will be appended to the BackOfficeHost.\n            ", "default": "/umbraco/error"}}}, "TypeFinderSettings": {"type": "object", "description": "Typed configuration options for type finder settings.\n            ", "required": ["AssembliesAcceptingLoadExceptions"], "properties": {"AssembliesAcceptingLoadExceptions": {"type": "string", "description": "Gets or sets a value for the assemblies that accept load exceptions during type finder operations.\n            ", "minLength": 1}, "AdditionalEntryAssemblies": {"type": "array", "description": "By default the entry assemblies for scanning plugin types is the Umbraco DLLs. If you require\nscanning for plugins based on different root referenced assemblies you can add the assembly name to this list.\n            ", "items": {"type": "string"}}, "AdditionalAssemblyExclusionEntries": {"type": "array", "description": "Gets or sets a value for the assemblies that will be excluded from scanning.\n            ", "items": {"type": "string"}}}}, "WebRoutingSettings": {"type": "object", "description": "Typed configuration options for web routing settings.\n            ", "properties": {"TryMatchingEndpointsForAllPages": {"type": "boolean", "description": "Gets or sets a value indicating whether to check if any routed endpoints match a front-end request before\nthe Umbraco dynamic router tries to map the request to an Umbraco content item.\n            ", "default": false}, "TrySkipIisCustomErrors": {"type": "boolean", "description": "Gets or sets a value indicating whether IIS custom errors should be skipped.\n            ", "default": false}, "InternalRedirectPreservesTemplate": {"type": "boolean", "description": "Gets or sets a value indicating whether an internal redirect should preserve the template.\n            ", "default": false}, "DisableAlternativeTemplates": {"type": "boolean", "description": "Gets or sets a value indicating whether the use of alternative templates are disabled.\n            ", "default": false}, "ValidateAlternativeTemplates": {"type": "boolean", "description": "Gets or sets a value indicating whether the use of alternative templates should be validated.\n            ", "default": false}, "DisableFindContentByIdentifierPath": {"type": "boolean", "default": false}, "DisableRedirectUrlTracking": {"type": "boolean", "description": "Gets or sets a value indicating whether redirect URL tracking is disabled.\n            ", "default": false}, "UrlProviderMode": {"description": "Gets or sets a value for the URL provider mode (UrlMode).\n            ", "default": "Auto", "oneOf": [{"$ref": "#/definitions/UrlMode"}]}, "UmbracoApplicationUrl": {"type": "string", "description": "Gets or sets a value for the Umbraco application URL.\n            "}}}, "UrlMode": {"type": "string", "description": "Specifies the type of URLs that the URL provider should produce, Auto is the default.\n            ", "x-enumNames": ["<PERSON><PERSON><PERSON>", "Relative", "Absolute", "Auto"], "enum": ["<PERSON><PERSON><PERSON>", "Relative", "Absolute", "Auto"]}, "UmbracoPluginSettings": {"type": "object", "description": "Typed configuration options for the plugins.\n            ", "properties": {"BrowsableFileExtensions": {"type": "array", "description": "Gets or sets the allowed file extensions (including the period \".\") that should be accessible from the browser.\n            ", "items": {"type": "string"}}}}, "UnattendedSettings": {"type": "object", "description": "Typed configuration options for unattended settings.\n            ", "properties": {"InstallUnattended": {"type": "boolean", "description": "Gets or sets a value indicating whether unattended installs are enabled.\n            ", "default": false}, "UpgradeUnattended": {"type": "boolean", "description": "Gets or sets a value indicating whether unattended upgrades are enabled.\n            ", "default": false}, "PackageMigrationsUnattended": {"type": "boolean", "description": "Gets or sets a value indicating whether unattended package migrations are enabled.\n            "}, "UnattendedUserName": {"type": ["null", "string"], "description": "Gets or sets a value to use for creating a user with a name for Unattended Installs\n            "}, "UnattendedUserEmail": {"type": ["null", "string"], "description": "Gets or sets a value to use for creating a user with an email for Unattended Installs\n            ", "format": "email"}, "UnattendedUserPassword": {"type": ["null", "string"], "description": "Gets or sets a value to use for creating a user with a password for Unattended Installs\n            "}}}, "BasicAuthSettings": {"type": "object", "description": "Typed configuration options for basic authentication settings.", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether Basic Auth Middleware is enabled.", "default": false}, "AllowedIPs": {"type": "array", "items": {"type": "string"}}, "SharedSecret": {"$ref": "#/definitions/SharedSecret"}, "RedirectToLoginPage": {"type": "boolean"}}}, "SharedSecret": {"type": "object", "properties": {"HeaderName": {"type": ["null", "string"], "default": "X-Authentication-Shared-Secret"}, "Value": {"type": ["null", "string"]}}}, "PackageMigrationSettings": {"type": "object", "description": "Typed configuration options for package migration settings.\n            ", "properties": {"RunSchemaAndContentMigrations": {"type": "boolean", "description": "Gets or sets a value indicating whether package migration steps that install schema and content should run.\n            ", "default": true}, "AllowComponentOverrideOfRunSchemaAndContentMigrations": {"type": "boolean", "description": "Gets or sets a value indicating whether components can override the configured value for\nRunSchemaAndContentMigrations.\n            ", "default": true}}}, "LegacyPasswordMigrationSettings": {"type": "object", "description": "Typed configuration options for legacy machine key settings used for migration of members from a v8 solution.\n            ", "properties": {"MachineKeyDecryptionKey": {"type": "string", "description": "Gets or sets the decryption hex-formatted string key found in legacy web.config machineKey configuration-element.\n            ", "default": ""}}}, "HelpPageSettings": {"type": "object", "properties": {"HelpPageUrlAllowList": {"type": "array", "description": "Gets or sets the allowed addresses to retrieve data for the content dashboard.\n            ", "items": {"type": "string"}}}}, "InstallDefaultDataNamedOptions": {"type": "object", "properties": {"Languages": {"$ref": "#/definitions/InstallDefaultDataSettings"}, "DataTypes": {"$ref": "#/definitions/InstallDefaultDataSettings"}, "MediaTypes": {"$ref": "#/definitions/InstallDefaultDataSettings"}, "MemberTypes": {"$ref": "#/definitions/InstallDefaultDataSettings"}}}, "InstallDefaultDataSettings": {"type": "object", "description": "Typed configuration options for installation of default data.\n            ", "properties": {"InstallData": {"description": "Gets or sets a value indicating whether to create default data on installation.\n            ", "oneOf": [{"$ref": "#/definitions/InstallDefaultDataOption"}]}, "Values": {"type": "array", "description": "Gets or sets a value indicating which default data (languages, data types, etc.) should be created when\nInstallData is\nset to Values or ExceptValues.\n            ", "items": {"type": "string"}}}}, "InstallDefaultDataOption": {"type": "string", "description": "An enumeration of options available for control over installation of default Umbraco data.\n            ", "x-enumNames": ["None", "Values", "Except<PERSON><PERSON><PERSON>", "All"], "enum": ["None", "Values", "Except<PERSON><PERSON><PERSON>", "All"]}, "DataTypesSettings": {"type": "object", "properties": {"CanBeChanged": {"description": "Gets or sets a value indicating if data types can be changed after they've been used.", "default": "True", "oneOf": [{"$ref": "#/definitions/DataTypeChangeMode"}]}}}, "DataTypeChangeMode": {"type": "string", "description": "", "x-enumNames": ["True", "False", "FalseWithHelpText"], "enum": ["True", "False", "FalseWithHelpText"]}, "MarketplaceSettings": {"type": "object", "description": "Configuration options for the Marketplace.", "properties": {"AdditionalParameters": {"type": "object", "description": "Gets or sets the additional parameters that are sent to the Marketplace.", "additionalProperties": {"type": "string"}}}}, "WebhookSettings": {"type": "object", "properties": {"Enabled": {"type": "boolean", "description": "Gets or sets a value indicating whether webhooks are enabled.\n            ", "default": true}, "MaximumRetries": {"type": "integer", "description": "Gets or sets a value indicating the maximum number of retries for all webhooks.\n            ", "format": "int32", "default": 5}, "Period": {"type": "string", "description": "Gets or sets a value for the period of the webhook firing.\n            ", "format": "duration", "default": "00:00:10"}, "EnableLoggingCleanup": {"type": "boolean", "description": "Gets or sets a value indicating whether cleanup of webhook logs are enabled.\n            ", "default": true}, "KeepLogsForDays": {"type": "integer", "description": "Gets or sets a value indicating number of days to keep logs for.\n            ", "format": "int32", "default": 30}, "PayloadType": {"description": "Gets or sets a value indicating the type of payload used for sending webhooks\n            ", "default": "Legacy", "oneOf": [{"$ref": "#/definitions/WebhookPayloadType"}]}}}, "WebhookPayloadType": {"type": "string", "description": "", "x-enumNames": ["Minimal", "Extended", "Legacy"], "enum": ["Minimal", "Extended", "Legacy"]}, "CacheSettings": {"type": "object", "properties": {"ContentTypeKeys": {"type": "array", "description": "Gets or sets a value for the collection of content type ids to always have in the cache.\n            ", "items": {"type": "string", "format": "guid"}}, "DocumentBreadthFirstSeedCount": {"type": "integer", "format": "int32", "default": 100}, "MediaBreadthFirstSeedCount": {"type": "integer", "format": "int32", "default": 100}, "Entry": {"$ref": "#/definitions/CacheEntry"}}}, "CacheEntry": {"type": "object", "properties": {"Document": {"$ref": "#/definitions/CacheEntrySettings"}, "Media": {"$ref": "#/definitions/CacheEntrySettings"}}}, "CacheEntrySettings": {"type": "object", "properties": {"LocalCacheDuration": {"type": "string", "format": "duration", "default": "1.00:00:00"}, "RemoteCacheDuration": {"type": "string", "format": "duration", "default": "365.00:00:00"}, "SeedCacheDuration": {"type": "string", "format": "duration", "default": "365.00:00:00"}}}}}