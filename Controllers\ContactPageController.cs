using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Core.Web;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class ContactPageController : RenderController
    {
        private readonly ILogger<ContactPageController> _logger;

        public ContactPageController(ILogger<ContactPageController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor)
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
            _logger = logger;
        }

        public IActionResult ContactPage(ContactPage model)
        {
            // Set culture-specific content
            var culture = Thread.CurrentThread.CurrentCulture.Name;
            ViewBag.Culture = culture;
            ViewBag.IsRtl = culture.StartsWith("ar");

            // Add contact-specific data
            ViewBag.ContactInfo = model.GetContactInfo();
            ViewBag.HasMap = model.HasLocationData();
            ViewBag.GoogleMapsLink = model.GetGoogleMapsLink();
            ViewBag.WhatsAppLink = model.GetWhatsAppLink();

            return CurrentTemplate(model);
        }

        [HttpPost]
        public IActionResult SubmitContactForm(ContactFormModel formModel)
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "يرجى ملء جميع الحقول المطلوبة" });
            }

            try
            {
                // Here you would typically:
                // 1. Save the form submission to database
                // 2. Send email notification
                // 3. Send auto-reply to user

                // For now, just log the submission
                _logger.LogInformation("Contact form submitted: {Name} - {Email} - {Subject}",
                    formModel.Name, formModel.Email, formModel.Subject);

                return Json(new { success = true, message = "تم إرسال رسالتك بنجاح! سنتواصل معك قريباً." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing contact form submission");
                return Json(new { success = false, message = "حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى." });
            }
        }
    }

    public class ContactFormModel
    {
        public string Name { get; set; } = "";
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Subject { get; set; } = "";
        public string Message { get; set; } = "";
        public bool Privacy { get; set; }
    }
}
