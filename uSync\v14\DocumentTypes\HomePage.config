<?xml version="1.0" encoding="utf-8"?>
<DocumentType Key="a3c1c456-8c3d-4e2a-9f1b-2d3e4f5a6b7c" Alias="homePage" Level="1">
  <Info>
    <Name>Home Page</Name>
    <Icon>icon-home</Icon>
    <Thumbnail>folder.png</Thumbnail>
    <Description>Main home page for MDD Plus fintech website</Description>
    <AllowAtRoot>True</AllowAtRoot>
    <ListView>00000000-0000-0000-0000-000000000000</ListView>
    <Variations>Culture</Variations>
    <IsElement>false</IsElement>
    <HistoryCleanup>
      <PreventCleanup>False</PreventCleanup>
      <KeepAllVersionsNewerThanDays></KeepAllVersionsNewerThanDays>
      <KeepLatestVersionPerDayForDays></KeepLatestVersionPerDayForDays>
    </HistoryCleanup>
    <Compositions />
    <DefaultTemplate>HomePageModern</DefaultTemplate>
    <AllowedTemplates>
      <Template Key="b4c5d6e7-8f9a-1b2c-3d4e-5f6a7b8c9d0e">HomePage</Template>
      <Template Key="c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f">HomePageModern</Template>
    </AllowedTemplates>
  </Info>
  <Structure>
    <DocumentType Key="c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f">contentPage</DocumentType>
    <DocumentType Key="d6e7f8a9-0b1c-2d3e-4f5a-6b7c8d9e0f1a">newsPage</DocumentType>
    <DocumentType Key="e7f8a9b0-1c2d-3e4f-5a6b-7c8d9e0f1a2b">contactPage</DocumentType>
  </Structure>
  <GenericProperties>
    <!-- Hero Section -->
    <GenericProperty>
      <Key>1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Name>Hero Title</Name>
      <Alias>heroTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>true</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Main hero section title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="heroSection">Hero Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Name>Hero Subtitle</Name>
      <Alias>heroSubtitle</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hero section subtitle/description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="heroSection">Hero Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Name>Hero Button Text</Name>
      <Alias>heroButtonText</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Text for the main CTA button]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="heroSection">Hero Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Name>Hero Image</Name>
      <Alias>heroImage</Alias>
      <Definition>ad9f0cf2-bda2-45d5-9ea1-a63cfc873fd3</Definition>
      <Type>Umbraco.MediaPicker3</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Hero section background image]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="heroSection">Hero Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>

    <!-- About Section -->
    <GenericProperty>
      <Key>5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Name>About Title</Name>
      <Alias>aboutTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[About section title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="aboutSection">About Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c</Key>
      <Name>About Text</Name>
      <Alias>aboutText</Alias>
      <Definition>ca90c950-0aff-4e72-b976-a30b1ac57dad</Definition>
      <Type>Umbraco.TinyMCE</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[About section content]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="aboutSection">About Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>

    <!-- Services Section -->
    <GenericProperty>
      <Key>7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d</Key>
      <Name>Services Title</Name>
      <Alias>servicesTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Services section title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="servicesSection">Services Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>

    <!-- Statistics Section -->
    <GenericProperty>
      <Key>8b9c0d1e-2f3a-4b5c-6d7e-8f9a0b1c2d3e</Key>
      <Name>Total Funded</Name>
      <Alias>totalFunded</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Total amount funded (numbers only)]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="statistics">Statistics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f</Key>
      <Name>Annual Return</Name>
      <Alias>annualReturn</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Annual return percentage]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="statistics">Statistics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>0d1e2f3a-4b5c-6d7e-8f9a-0b1c2d3e4f5a</Key>
      <Name>Active Investors</Name>
      <Alias>activeInvestors</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Number of active investors]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="statistics">Statistics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b</Key>
      <Name>Repayment Rate</Name>
      <Alias>repaymentRate</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Repayment success rate percentage]]></Description>
      <SortOrder>4</SortOrder>
      <Tab Alias="statistics">Statistics</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>

    <!-- Contact Section -->
    <GenericProperty>
      <Key>2f3a4b5c-6d7e-8f9a-0b1c-2d3e4f5a6b7c</Key>
      <Name>Contact Title</Name>
      <Alias>contactTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Contact section title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="contactSection">Contact Section</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d</Key>
      <Name>Contact Email</Name>
      <Alias>contactEmail</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Contact email address]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="contactSection">Contact Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>4b5c6d7e-8f9a-0b1c-2d3e-4f5a6b7c8d9e</Key>
      <Name>Contact Phone</Name>
      <Alias>contactPhone</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[Contact phone number]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="contactSection">Contact Section</Tab>
      <Variations>Nothing</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>

    <!-- SEO Properties -->
    <GenericProperty>
      <Key>5c6d7e8f-9a0b-1c2d-3e4f-5a6b7c8d9e0f</Key>
      <Name>Meta Title</Name>
      <Alias>metaTitle</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta title]]></Description>
      <SortOrder>1</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>6d7e8f9a-0b1c-2d3e-4f5a-6b7c8d9e0f1a</Key>
      <Name>Meta Description</Name>
      <Alias>metaDescription</Alias>
      <Definition>c6bac0dd-4ab9-45b1-8e30-e4b619ee5da3</Definition>
      <Type>Umbraco.TextArea</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta description]]></Description>
      <SortOrder>2</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
    <GenericProperty>
      <Key>7e8f9a0b-1c2d-3e4f-5a6b-7c8d9e0f1a2b</Key>
      <Name>Meta Keywords</Name>
      <Alias>metaKeywords</Alias>
      <Definition>0cc0eba1-9960-42c9-bf9b-60e150b429ae</Definition>
      <Type>Umbraco.TextBox</Type>
      <Mandatory>false</Mandatory>
      <Validation></Validation>
      <Description><![CDATA[SEO meta keywords]]></Description>
      <SortOrder>3</SortOrder>
      <Tab Alias="seo">SEO</Tab>
      <Variations>Culture</Variations>
      <MandatoryMessage></MandatoryMessage>
      <ValidationRegExpMessage></ValidationRegExpMessage>
      <LabelOnTop>false</LabelOnTop>
    </GenericProperty>
  </GenericProperties>
  <Tabs>
    <Tab>
      <Key>a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d</Key>
      <Caption>Hero Section</Caption>
      <Alias>heroSection</Alias>
      <Type>Tab</Type>
      <SortOrder>0</SortOrder>
    </Tab>
    <Tab>
      <Key>b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e</Key>
      <Caption>About Section</Caption>
      <Alias>aboutSection</Alias>
      <Type>Tab</Type>
      <SortOrder>1</SortOrder>
    </Tab>
    <Tab>
      <Key>c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f</Key>
      <Caption>Services Section</Caption>
      <Alias>servicesSection</Alias>
      <Type>Tab</Type>
      <SortOrder>2</SortOrder>
    </Tab>
    <Tab>
      <Key>d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a</Key>
      <Caption>Statistics</Caption>
      <Alias>statistics</Alias>
      <Type>Tab</Type>
      <SortOrder>3</SortOrder>
    </Tab>
    <Tab>
      <Key>e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b</Key>
      <Caption>Contact Section</Caption>
      <Alias>contactSection</Alias>
      <Type>Tab</Type>
      <SortOrder>4</SortOrder>
    </Tab>
    <Tab>
      <Key>f6a7b8c9-d0e1-2f3a-4b5c-6d7e8f9a0b1c</Key>
      <Caption>SEO</Caption>
      <Alias>seo</Alias>
      <Type>Tab</Type>
      <SortOrder>5</SortOrder>
    </Tab>
  </Tabs>
</DocumentType>
