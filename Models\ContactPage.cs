using Umbraco.Cms.Core.Models.PublishedContent;
using Umbraco.Cms.Core.Models;

namespace MDDPlus.Models
{
    public class ContactPage : PublishedContentModel
    {
        public ContactPage(IPublishedContent content, IPublishedValueFallback publishedValueFallback) : base(content, publishedValueFallback) { }

        // Page Content
        public string PageTitle => this.Value<string>("pageTitle") ?? "تواصل معنا";
        public string PageSubtitle => this.Value<string>("pageSubtitle") ?? "";
        public string ContactIntroduction => this.Value<string>("contactIntroduction") ?? "";

        // Contact Information
        public string OfficeAddress => this.Value<string>("officeAddress") ?? "";
        public string PhoneNumber => this.Value<string>("phoneNumber") ?? "";
        public string EmailAddress => this.Value<string>("emailAddress") ?? "";
        public string WorkingHours => this.Value<string>("workingHours") ?? "";
        public string WhatsappNumber => this.Value<string>("whatsappNumber") ?? "";

        // Location
        public string GoogleMapsEmbed => this.Value<string>("googleMapsEmbed") ?? "";
        public string Latitude => this.Value<string>("latitude") ?? "";
        public string Longitude => this.Value<string>("longitude") ?? "";

        // SEO Properties
        public string MetaTitle => this.Value<string>("metaTitle") ?? PageTitle;
        public string MetaDescription => this.Value<string>("metaDescription") ?? "";
        public string MetaKeywords => this.Value<string>("metaKeywords") ?? "";

        // Helper Methods
        public string GetFormattedPhoneNumber()
        {
            if (string.IsNullOrEmpty(PhoneNumber))
                return "";

            // Remove any formatting and return clean number for tel: links
            return PhoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        }

        public string GetWhatsAppLink()
        {
            if (string.IsNullOrEmpty(WhatsappNumber))
                return "";

            var cleanNumber = WhatsappNumber.Replace("+", "").Replace(" ", "").Replace("-", "");
            return $"https://wa.me/{cleanNumber}";
        }

        public string GetGoogleMapsLink()
        {
            if (!string.IsNullOrEmpty(Latitude) && !string.IsNullOrEmpty(Longitude))
            {
                return $"https://www.google.com/maps?q={Latitude},{Longitude}";
            }

            if (!string.IsNullOrEmpty(OfficeAddress))
            {
                return $"https://www.google.com/maps/search/{Uri.EscapeDataString(OfficeAddress)}";
            }

            return "";
        }

        public bool HasLocationData()
        {
            return !string.IsNullOrEmpty(GoogleMapsEmbed) ||
                   (!string.IsNullOrEmpty(Latitude) && !string.IsNullOrEmpty(Longitude));
        }

        public ContactInfo GetContactInfo()
        {
            return new ContactInfo
            {
                Address = OfficeAddress,
                Phone = PhoneNumber,
                Email = EmailAddress,
                WorkingHours = WorkingHours,
                WhatsApp = WhatsappNumber,
                GoogleMapsLink = GetGoogleMapsLink(),
                WhatsAppLink = GetWhatsAppLink()
            };
        }
    }

    public class ContactInfo
    {
        public string Address { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string WorkingHours { get; set; } = "";
        public string WhatsApp { get; set; } = "";
        public string GoogleMapsLink { get; set; } = "";
        public string WhatsAppLink { get; set; } = "";
    }
}
