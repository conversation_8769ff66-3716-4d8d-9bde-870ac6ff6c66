// Enhanced MDD Plus JavaScript - Professional Fintech Features

(function() {
    'use strict';

    // Page Loading Management
    class PageLoader {
        constructor() {
            this.loader = document.getElementById('pageLoader');
            this.init();
        }

        init() {
            // Hide loader when page is fully loaded
            window.addEventListener('load', () => {
                setTimeout(() => {
                    this.hide();
                }, 500);
            });

            // Hide loader if it takes too long
            setTimeout(() => {
                this.hide();
            }, 3000);
        }

        hide() {
            if (this.loader) {
                this.loader.classList.add('hidden');
                setTimeout(() => {
                    this.loader.style.display = 'none';
                }, 500);
            }
        }
    }

    // Intersection Observer for Animations
    class AnimationObserver {
        constructor() {
            this.observer = null;
            this.init();
        }

        init() {
            if ('IntersectionObserver' in window) {
                this.observer = new IntersectionObserver(
                    this.handleIntersection.bind(this),
                    {
                        threshold: 0.1,
                        rootMargin: '0px 0px -50px 0px'
                    }
                );

                this.observeElements();
            }
        }

        observeElements() {
            const elements = document.querySelectorAll('.fade-in, .stat-item, .service-item, .contact-item');
            elements.forEach((el, index) => {
                el.style.setProperty('--delay', index);
                if (this.observer) {
                    this.observer.observe(el);
                }
            });
        }

        handleIntersection(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    if (this.observer) {
                        this.observer.unobserve(entry.target);
                    }
                }
            });
        }
    }

    // Enhanced Statistics Counter
    class StatisticsCounter {
        constructor() {
            this.counters = document.querySelectorAll('.stat-number');
            this.init();
        }

        init() {
            if (this.counters.length > 0) {
                this.setupObserver();
            }
        }

        setupObserver() {
            const observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.animateCounter(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                },
                { threshold: 0.5 }
            );

            this.counters.forEach(counter => {
                observer.observe(counter);
            });
        }

        animateCounter(element) {
            const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Format number with commas
                const formatted = Math.floor(current).toLocaleString();
                element.textContent = element.textContent.replace(/[\d,]+/, formatted);
            }, 16);
        }
    }

    // Smooth Scrolling Enhancement
    class SmoothScroll {
        constructor() {
            this.init();
        }

        init() {
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a[href^="#"]');
                if (link) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        this.scrollToElement(targetElement);
                    }
                }
            });
        }

        scrollToElement(element) {
            const headerHeight = document.querySelector('.navbar')?.offsetHeight || 0;
            const targetPosition = element.offsetTop - headerHeight - 20;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }

    // Enhanced Theme Management
    class ThemeManager {
        constructor() {
            this.currentTheme = localStorage.getItem('theme') || 'light';
            this.init();
        }

        init() {
            this.applyTheme(this.currentTheme);
            this.setupToggle();
        }

        setupToggle() {
            const toggle = document.querySelector('.theme-toggle');
            if (toggle) {
                toggle.addEventListener('click', () => {
                    this.toggleTheme();
                });
                this.updateToggleText(toggle);
            }
        }

        toggleTheme() {
            this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
            this.applyTheme(this.currentTheme);
            localStorage.setItem('theme', this.currentTheme);
            
            const toggle = document.querySelector('.theme-toggle');
            if (toggle) {
                this.updateToggleText(toggle);
            }
        }

        applyTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
        }

        updateToggleText(toggle) {
            const isArabic = document.documentElement.lang === 'ar';
            const lightText = isArabic ? 'فاتح' : 'Light';
            const darkText = isArabic ? 'داكن' : 'Dark';
            
            toggle.textContent = this.currentTheme === 'light' ? darkText : lightText;
        }
    }

    // Performance Monitor
    class PerformanceMonitor {
        constructor() {
            this.init();
        }

        init() {
            if ('performance' in window) {
                window.addEventListener('load', () => {
                    setTimeout(() => {
                        this.logPerformanceMetrics();
                    }, 1000);
                });
            }
        }

        logPerformanceMetrics() {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
                const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
                
                console.log('🚀 MDD Plus Performance Metrics:');
                console.log(`📊 Page Load Time: ${loadTime.toFixed(2)}ms`);
                console.log(`🏗️ DOM Content Loaded: ${domContentLoaded.toFixed(2)}ms`);
                console.log(`🎯 Total Load Time: ${navigation.loadEventEnd.toFixed(2)}ms`);
            }
        }
    }

    // Error Handling
    class ErrorHandler {
        constructor() {
            this.init();
        }

        init() {
            window.addEventListener('error', (e) => {
                console.error('🚨 JavaScript Error:', e.error);
                this.logError(e.error);
            });

            window.addEventListener('unhandledrejection', (e) => {
                console.error('🚨 Unhandled Promise Rejection:', e.reason);
                this.logError(e.reason);
            });
        }

        logError(error) {
            // In production, you would send this to your error tracking service
            const errorData = {
                message: error.message || error,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            };

            console.log('📝 Error logged:', errorData);
        }
    }

    // Accessibility Enhancements
    class AccessibilityEnhancer {
        constructor() {
            this.init();
        }

        init() {
            this.setupKeyboardNavigation();
            this.setupFocusManagement();
            this.setupAriaLabels();
        }

        setupKeyboardNavigation() {
            document.addEventListener('keydown', (e) => {
                // Escape key to close modals or overlays
                if (e.key === 'Escape') {
                    const loader = document.getElementById('pageLoader');
                    if (loader && !loader.classList.contains('hidden')) {
                        loader.classList.add('hidden');
                    }
                }

                // Tab navigation enhancement
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', () => {
                document.body.classList.remove('keyboard-navigation');
            });
        }

        setupFocusManagement() {
            const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
            const focusable = document.querySelectorAll(focusableElements);
            
            focusable.forEach(element => {
                element.addEventListener('focus', () => {
                    element.classList.add('focused');
                });
                
                element.addEventListener('blur', () => {
                    element.classList.remove('focused');
                });
            });
        }

        setupAriaLabels() {
            // Add aria-labels to buttons without text
            const buttons = document.querySelectorAll('button:not([aria-label])');
            buttons.forEach(button => {
                if (!button.textContent.trim()) {
                    const icon = button.querySelector('svg, i');
                    if (icon) {
                        button.setAttribute('aria-label', 'Button');
                    }
                }
            });
        }
    }

    // Mobile Optimizations
    class MobileOptimizer {
        constructor() {
            this.init();
        }

        init() {
            this.setupTouchOptimizations();
            this.setupViewportOptimizations();
        }

        setupTouchOptimizations() {
            // Improve touch targets
            const touchTargets = document.querySelectorAll('button, a, .clickable');
            touchTargets.forEach(target => {
                target.style.minHeight = '44px';
                target.style.minWidth = '44px';
            });

            // Prevent zoom on input focus (iOS)
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    const viewport = document.querySelector('meta[name="viewport"]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });

                input.addEventListener('blur', () => {
                    const viewport = document.querySelector('meta[name="viewport"]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                    }
                });
            });
        }

        setupViewportOptimizations() {
            // Handle orientation change
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            });
        }
    }

    // Initialize all modules when DOM is ready
    function initializeApp() {
        new PageLoader();
        new AnimationObserver();
        new StatisticsCounter();
        new SmoothScroll();
        new ThemeManager();
        new PerformanceMonitor();
        new ErrorHandler();
        new AccessibilityEnhancer();
        new MobileOptimizer();

        console.log('🎉 MDD Plus Enhanced Features Initialized');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }

    // Expose utilities globally for debugging
    window.MDDPlus = {
        version: '1.0.0',
        initialized: true,
        modules: {
            PageLoader,
            AnimationObserver,
            StatisticsCounter,
            SmoothScroll,
            ThemeManager,
            PerformanceMonitor,
            ErrorHandler,
            AccessibilityEnhancer,
            MobileOptimizer
        }
    };

})();
