{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "9b20d30e-e105-4970-af39-467fa4872856", "SanitizeTinyMce": true, "DefaultUILanguage": "ar", "HideTopLevelNodeFromPath": true}, "Content": {"AllowEditInvariantFromNonDefault": true, "ContentVersionCleanupPolicy": {"EnableCleanup": true}, "Multilingual": {"IsEnabled": true}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"AllowConcurrentLogins": false, "AuthCookieName": "MDDPlusAuth", "AuthCookieDomain": "", "KeepUserLoggedIn": true}, "WebRouting": {"TryMatchingEndpointsForAllPages": true, "UrlProviderMode": "Auto"}, "RequestHandler": {"AddTrailingSlash": false}}}, "ConnectionStrings": {"umbracoDbDSN": "Data Source=App_Data/Umbraco.sqlite.db;Cache=Shared;Foreign Keys=True;Pooling=True", "umbracoDbDSN_ProviderName": "Microsoft.Data.Sqlite"}}