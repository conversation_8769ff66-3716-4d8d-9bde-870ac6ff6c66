@model IEnumerable<Umbraco.Cms.Core.Models.IContent>
@{
    ViewBag.Title = "إدارة المحتوى - مدد بلس";
    Layout = "_AdminLayout";
}

<div class="admin-container">
    <div class="admin-header">
        <h1>إدارة المحتوى</h1>
        <p>إدارة محتوى موقع مدد بلس</p>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-error">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="admin-actions">
        <form method="post" action="/admin/content/create">
            <button type="submit" class="btn btn-primary">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                إنشاء صفحة رئيسية جديدة
            </button>
        </form>
    </div>

    <div class="content-list">
        @if (Model.Any())
        {
            <div class="card">
                <div class="card-header">
                    <h3>الصفحات الموجودة</h3>
                </div>
                <div class="card-body">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>اسم الصفحة</th>
                                <th>تاريخ التحديث</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var content in Model)
                            {
                                <tr>
                                    <td>@content.Name</td>
                                    <td>@content.UpdateDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <span class="status @(content.Published ? "published" : "draft")">
                                            @(content.Published ? "منشور" : "مسودة")
                                        </span>
                                    </td>
                                    <td>
                                        <a href="/admin/content/edit/@content.Id" class="btn btn-sm btn-outline">
                                            تحرير
                                        </a>
                                        <a href="/" target="_blank" class="btn btn-sm btn-secondary">
                                            معاينة
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">
                    <svg width="64" height="64" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                </div>
                <h3>لا توجد صفحات</h3>
                <p>لم يتم إنشاء أي صفحات بعد. ابدأ بإنشاء الصفحة الرئيسية.</p>
            </div>
        }
    </div>

    <div class="admin-help">
        <div class="card">
            <div class="card-header">
                <h3>كيفية إدارة المحتوى</h3>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>إنشاء صفحة جديدة:</strong> اضغط على "إنشاء صفحة رئيسية جديدة"</li>
                    <li><strong>تحرير المحتوى:</strong> اضغط على "تحرير" بجانب الصفحة المطلوبة</li>
                    <li><strong>معاينة الصفحة:</strong> اضغط على "معاينة" لرؤية الصفحة كما يراها الزوار</li>
                    <li><strong>الوصول إلى Umbraco:</strong> <a href="/umbraco" target="_blank">اضغط هنا للوصول إلى لوحة تحكم Umbraco الكاملة</a></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: 'Cairo', sans-serif;
    direction: rtl;
}

.admin-header {
    margin-bottom: 2rem;
    text-align: center;
}

.admin-header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.admin-actions {
    margin-bottom: 2rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.admin-table th {
    background: var(--bg-secondary);
    font-weight: 600;
}

.status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status.published {
    background: #d4edda;
    color: #155724;
}

.status.draft {
    background: #fff3cd;
    color: #856404;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-icon {
    margin-bottom: 1rem;
    opacity: 0.5;
}

.admin-help {
    margin-top: 3rem;
}

.admin-help ol {
    padding-right: 1.5rem;
}

.admin-help li {
    margin-bottom: 0.5rem;
}
</style>
