using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Core.Web;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class NewsArticleController : RenderController
    {
        public NewsArticleController(ILogger<NewsArticleController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor)
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
        }

        public IActionResult NewsArticle(NewsArticle model)
        {
            // Set culture-specific content
            var culture = Thread.CurrentThread.CurrentCulture.Name;
            ViewBag.Culture = culture;
            ViewBag.IsRtl = culture.StartsWith("ar");

            // Add article-specific data
            ViewBag.ReadingTime = model.GetReadingTime();
            ViewBag.RelatedArticles = model.GetRelatedArticles(3);
            ViewBag.ArticleExcerpt = model.GetExcerpt(160);

            // Set structured data for SEO
            ViewBag.StructuredData = new
            {
                Type = "Article",
                Headline = model.ArticleTitle,
                Description = model.ArticleSummary,
                Author = model.Author,
                DatePublished = model.PublicationDate?.ToString("yyyy-MM-dd"),
                Keywords = string.Join(", ", model.Tags)
            };

            return CurrentTemplate(model);
        }
    }
}
