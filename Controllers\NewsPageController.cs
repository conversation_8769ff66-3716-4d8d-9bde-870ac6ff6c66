using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.Extensions.Logging;
using Umbraco.Cms.Core.Web;
using Umbraco.Cms.Web.Common.Controllers;
using MDDPlus.Models;

namespace MDDPlus.Controllers
{
    public class NewsPageController : RenderController
    {
        public NewsPageController(ILogger<NewsPageController> logger, ICompositeViewEngine compositeViewEngine, IUmbracoContextAccessor umbracoContextAccessor)
            : base(logger, compositeViewEngine, umbracoContextAccessor)
        {
        }

        public IActionResult NewsPage(NewsPage model)
        {
            // Set culture-specific content
            var culture = Thread.CurrentThread.CurrentCulture.Name;
            ViewBag.Culture = culture;
            ViewBag.IsRtl = culture.StartsWith("ar");

            // Add any additional logic for news page
            ViewBag.TotalArticles = model.GetAllArticles().Count();
            ViewBag.RecentArticles = model.GetRecentArticles(5);

            return CurrentTemplate(model);
        }
    }
}
